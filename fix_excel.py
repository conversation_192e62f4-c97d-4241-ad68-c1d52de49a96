#!/usr/bin/env python3
# Script to fix Excel "Item" to "Cells" in AutoLISP file

import re

# Read the file
with open('Z-Cad2Excel-E1_v3.lsp', 'r', encoding='utf-8') as f:
    content = f.read()

# Replace all "Item" with "Cells" in Excel property access
content = re.sub(r'vlax-get-property\s+xlcells\s+"Item"', 'vlax-get-property xlcells "Cells"', content)

# Write back to file
with open('Z-Cad2Excel-E1_v3.lsp', 'w', encoding='utf-8') as f:
    f.write(content)

print("Fixed Excel Item -> Cells replacements")
