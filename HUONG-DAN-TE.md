# HƯỚNG DẪN SỬ DỤNG LỆNH TE - TABLE EXPORT

## GIỚI THIỆU
Lệnh TE được tạo ra để export table CAD sang Excel một cách nhanh chóng và tiện lợi, dựa trên kinh nghiệm từ file E1-ET.lsp.

## TÍNH NĂNG CHÍNH
- ✅ Export table CAD sang Excel tại vị trí cursor hiện tại
- ✅ Tự động xử lý MTEXT format codes (loại bỏ {}, \xxx;)
- ✅ Chuyển đổi ký hiệu đặc biệt (%%c→Ø, %%p→±, %%d→°)
- ✅ Hỗ trợ ghi handle vào comment (nếu bật)
- ✅ Hỗ trợ đóng khung table (nếu bật)
- ✅ Cursor tự động nhảy xuống dưới table sau khi export

## CÁCH SỬ DỤNG

### Bước 1: Load file
1. Mở AutoCAD
2. G<PERSON> lệnh `APPLOAD`
3. Chọn file `TE-TableExport.lsp`
4. Click Load

### Bước 2: Chu<PERSON>n bị Excel
1. Mở Excel trước khi chạy lệnh
2. Đặt cursor tại vị trí muốn export table

### Bước 3: Sử dụng lệnh TE

#### Phương pháp mới (v2.0) - Khuyến nghị
```
Command: TE-V2
Chon table CAD de xuat sang Excel: [click vào table]
```

#### Phương pháp cũ (v1.4) - Dự phòng
```
Command: TE
Chon table CAD de xuat sang Excel: [click vào table]
```

## CÀI ĐẶT TÙY CHỌN

Lệnh TE sử dụng các biến toàn cục sau (tương thích với E1-ET):

### *E6-handle*
- **Mặc định**: "Y"
- **Chức năng**: Ghi handle của table vào comment Excel
- **Cách thay đổi**: `(setq *E6-handle* "N")` để tắt

### *E6-frame*
- **Mặc định**: "N" 
- **Chức năng**: Đóng khung cho table trong Excel
- **Cách thay đổi**: `(setq *E6-frame* "Y")` để bật

### *E6-jump*
- **Mặc định**: "3"
- **Chức năng**: Số hàng nhảy xuống sau khi export
- **Cách thay đổi**: `(setq *E6-jump* "5")` để nhảy 5 hàng

## VÍ DỤ SỬ DỤNG

### Ví dụ 1: Export table cơ bản
```
Command: TE
Chon table CAD de xuat sang Excel: [chọn table]
Table co 5 hang va 3 cot
Hoan thanh! Da xuat table 5x3 sang Excel
Chuot dang o vi tri 8,1
```

### Ví dụ 2: Bật đóng khung
```
Command: (setq *E6-frame* "Y")
Command: TE
[Export table với khung]
```

### Ví dụ 3: Tắt ghi handle
```
Command: (setq *E6-handle* "N")
Command: TE
[Export table không ghi handle]
```

## XỬ LÝ MTEXT FORMAT VÀ TIẾNG VIỆT

Lệnh TE đã được cải tiến để xử lý tốt hơn với tiếng Việt có dấu:

### Phương pháp mới v2.0 - MTEXT TextString
- **Ý tưởng**: Thay vì làm sạch format codes, sử dụng thuộc tính `TextString` của MTEXT
- **Ưu điểm**: AutoCAD tự động loại bỏ format codes, kết quả chính xác 100%
- **Bảo vệ ký tự có dấu**: Hoàn toàn an toàn với tiếng Việt
- **Hiệu suất**: Nhanh hơn, ít lỗi hơn phương pháp làm sạch thủ công

### Xử lý toàn diện v1.6
- **Unicode escape sequences**: `\U+[HEX]` → ký tự tiếng Việt
- **Format codes**: `\[letter][number];` (có thể có spaces sau semicolon)
- **Ký hiệu đặc biệt**: `%%c` → `Ø`, `%%p` → `±`, `%%d` → `°`
- **Dấu ngoặc**: `{text}` → chỉ giữ lại `text`

### Chuyển đổi ký hiệu
- `%%c` hoặc `%%C` → `Ø` (đường kính)
- `%%p` hoặc `%%P` → `±` (cộng trừ)
- `%%d` hoặc `%%D` → `°` (độ)

### Ví dụ xử lý text tiếng Việt
```
Input:  "{\fArial;Ký hiệu}"
Output: "Ký hiệu"

Input:  "{\fTimes;Số lượng}"
Output: "Số lượng"

Input:  "{TỔNG CỘNG}"
Output: "TỔNG CỘNG"

Input:  "Đơn vị"
Output: "Đơn vị"

Input:  "\Franklin Gothic Medium\b0\i0\c0\p34\W256\MATERIAL\P"
Output: "MATERIAL"

Input:  "{\fFranklin Gothic Medium;\b0;\i0;\c0;\p34;\W256;MATERIAL}"
Output: "MATERIAL"

Input:  "\fArial;\b1;\i0;STT"
Output: "STT"

Input:  "\C256;MATERIAL"
Output: "MATERIAL"

Input:  "\A1;  ĐẦU ĐO pH  (pH METER)"
Output: "ĐẦU ĐO pH  (pH METER)"

Input:  "\A1; LEVEL SWITCH"
Output: "LEVEL SWITCH"

Input:  "T\U+1ED4NG"
Output: "Tổng"

Input:  "S\U+1ED1 l\U+01B0\U+1EE3ng"
Output: "Số lượng"
```

## TÍCH HỢP VỚI E1-ET

Nếu bạn đã có file E1-ET.lsp, lệnh TE đã được tích hợp sẵn và sử dụng chung các cài đặt:

- Sử dụng lệnh `E6` để cài đặt toàn cục
- Tất cả các lệnh E1, E2, E3... và TE đều dùng chung cài đặt

## LỖI THƯỜNG GẶP VÀ CÁCH KHẮC PHỤC

### Lỗi: "Không tìm thấy Excel"
**Nguyên nhân**: Excel chưa được mở
**Khắc phục**: Mở Excel trước khi chạy lệnh TE

### Lỗi: "Không phải table CAD"
**Nguyên nhân**: Đối tượng được chọn không phải table
**Khắc phục**: Chọn đúng table CAD (tạo bằng lệnh TABLE)

### Lỗi: "Lỗi ghi cell"
**Nguyên nhân**: Excel bị khóa hoặc lỗi kết nối
**Khắc phục**:
1. Kiểm tra Excel có đang mở không
2. Thử đóng và mở lại Excel
3. Chạy lại lệnh TE

### Lỗi: Mất ký tự tiếng Việt có dấu
**Nguyên nhân**: MTEXT format codes làm hỏng encoding tiếng Việt
**Khắc phục**:
1. **Đã sửa trong phiên bản mới**: Sử dụng hàm `CLEAN-TABLE-TEXT`
2. Nếu vẫn bị lỗi, kiểm tra encoding của file CAD
3. Thử save file CAD với encoding UTF-8

### Lỗi: Format codes không được loại bỏ
**Nguyên nhân**: Format codes phức tạp không được nhận diện
**Khắc phục**:
1. Kiểm tra text trong table có format codes đặc biệt
2. Có thể cần chỉnh sửa thủ công trong Excel sau khi export

## THÔNG TIN PHIÊN BẢN

- **Phiên bản**: 1.6 (Xử lý Unicode escape sequences)
- **Ngày phát hành**: 19/12/2024
- **Tác giả**: Zit Đại Ka
- **Tương thích**: AutoCAD 2018+, Excel 2016+

### Lịch sử cập nhật
- **v1.6**: Xử lý Unicode escape sequences (T\\U+1ED4NG → Tổng, S\\U+1ED1 l\\U+01B0\\U+1EE3ng → Số lượng)
- **v1.5**: Sửa lỗi hiểu nhầm format codes - pattern đúng là \\[letter][number]; (có spaces)
- **v1.4**: Sửa lỗi format codes không có semicolon (\\W256\\MATERIAL → MATERIAL)
- **v1.3**: Thuật toán pattern-based xử lý format codes
- **v1.2**: Xử lý toàn diện format codes phức tạp (\\f, \\b, \\i, \\c, \\p, \\W, \\P)
- **v1.1**: Sửa lỗi mất ký tự tiếng Việt có dấu, cải tiến hàm `CLEAN-TABLE-TEXT`
- **v1.0**: Phiên bản đầu tiên với đầy đủ tính năng cơ bản

## HỖ TRỢ

Nếu gặp vấn đề, hãy kiểm tra:
1. AutoCAD và Excel đã được mở
2. Table CAD hợp lệ (không bị lỗi)
3. Quyền ghi file Excel
4. Phiên bản AutoCAD hỗ trợ VLA

---
*Lệnh TE được phát triển dựa trên kinh nghiệm từ hệ thống E1-ET, tối ưu hóa cho việc export table CAD sang Excel.*
