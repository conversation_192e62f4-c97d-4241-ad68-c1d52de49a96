; ===================================================================
; CAD TO EXCEL - PHIEN BAN MOI TINH GON
; Tac gia: Toi uu tu phien ban cu
; Ngay: 2024
; ===================================================================

; Bien toan cuc luu thiet lap
(or *E6-handle* (setq *E6-handle* "Y"))
(or *E6-frame* (setq *E6-frame* "N"))
(or *E6-symbol* (setq *E6-symbol* "+"))
(or *E6-factor* (setq *E6-factor* "1"))
(or *E6-jump* (setq *E6-jump* "3"))
(or *E6-tolerance* (setq *E6-tolerance* "50"))
(or *E6-number* (setq *E6-number* "N"))

; ===================================================================
; HAM PHU TRO CHUNG
; ===================================================================

; Ham tim ky tu trong chuoi (thay cho vl-string-search)
(defun FIND-CHAR ( char string / i found pos)
	(setq i 1 found nil pos nil)
	(while (and (<= i (strlen string)) (not found))
		(if (= (substr string i 1) char)
			(progn
				(setq found T)
				(setq pos (1- i))
			)
			(setq i (1+ i))
		)
	)
	pos
)

; Ham chuyen doi ky hieu dac biet trong text
(defun CONVERT-SPECIAL-SYMBOLS ( text-string / result)
	(if (and text-string (= (type text-string) 'STR) (> (strlen text-string) 0))
		(progn
			(setq result text-string)

			; Chuyen doi cac ky hieu dac biet
			(setq result (vl-string-subst "Ø" "%%c" result))  ; Diameter symbol
			(setq result (vl-string-subst "Ø" "%%C" result))  ; Diameter symbol (uppercase)
			(setq result (vl-string-subst "±" "%%p" result))  ; Plus-minus symbol
			(setq result (vl-string-subst "±" "%%P" result))  ; Plus-minus symbol (uppercase)
			(setq result (vl-string-subst "°" "%%d" result))  ; Degree symbol
			(setq result (vl-string-subst "°" "%%D" result))  ; Degree symbol (uppercase)

			result
		)
		""
	)
)

; Ham lam sach MTEXT - phien ban da sua loi va co chuyen doi ky hieu
(defun CLEAN-MTEXT ( mtext-string / result pos end-pos)
	(if (and mtext-string (= (type mtext-string) 'STR) (> (strlen mtext-string) 0))
		(progn
			(setq result mtext-string)

			; Buoc 1: Loai bo format \xxx; truoc
			(while (setq pos (FIND-CHAR "\\" result))
				(setq end-pos (FIND-CHAR ";" result))
				(if (and end-pos (> end-pos pos))
					(setq result (strcat
						(substr result 1 pos)
						(substr result (+ end-pos 2))
					))
					(setq result (substr result 1 pos))
				)
			)

			; Buoc 2: Loai bo dau { va } nhung giu lai noi dung
			(while (setq pos (FIND-CHAR "{" result))
				(setq result (strcat
					(substr result 1 pos)
					(substr result (+ pos 2))
				))
			)
			(while (setq pos (FIND-CHAR "}" result))
				(setq result (strcat
					(substr result 1 pos)
					(substr result (+ pos 2))
				))
			)

			; Buoc 3: Chuyen doi ky hieu dac biet
			(setq result (CONVERT-SPECIAL-SYMBOLS result))

			; Buoc 4: Trim khoang trang
			(while (and (> (strlen result) 0) (= (substr result 1 1) " "))
				(setq result (substr result 2))
			)
			(while (and (> (strlen result) 0) (= (substr result (strlen result) 1) " "))
				(setq result (substr result 1 (1- (strlen result))))
			)

			result
		)
		""
	)
)

; Ham test CLEAN-MTEXT va CONVERT-SPECIAL-SYMBOLS
(defun C:TEST-CLEAN ( / test1 test2 test3 test4 test5)
	(setq test1 "\\fFranklin Gothic Medium|b0|i0|c0|p34;15m\\H0.7x\\S3^;TRUNG")
	(setq test2 "2+2=\\fGothic Medium|b0|i0|c0|p34;2+Gothic Medium|b0|i0|c0|p34;")
	(setq test3 "{\\fArial|b1|i0|c0|p0;BE KHU TRUNG}")
	(setq test4 "%%c50 %%p0.1 %%d90")
	(setq test5 "Duong kinh %%C25, sai so %%P0.05, goc %%D45")

	(princ "\n=== TEST CLEAN-MTEXT ===")
	(princ (strcat "\nTest 1 input: " test1))
	(princ (strcat "\nTest 1 output: " (CLEAN-MTEXT test1)))
	(princ (strcat "\nTest 2 input: " test2))
	(princ (strcat "\nTest 2 output: " (CLEAN-MTEXT test2)))
	(princ (strcat "\nTest 3 input: " test3))
	(princ (strcat "\nTest 3 output: " (CLEAN-MTEXT test3)))

	(princ "\n=== TEST SPECIAL SYMBOLS ===")
	(princ (strcat "\nTest 4 input: " test4))
	(princ (strcat "\nTest 4 output: " (CONVERT-SPECIAL-SYMBOLS test4)))
	(princ (strcat "\nTest 5 input: " test5))
	(princ (strcat "\nTest 5 output: " (CONVERT-SPECIAL-SYMBOLS test5)))
	(princ)
)

; Ham test EXTRACT-NUMBER
(defun C:TEST-EXTRACT ( / test1 test2 test3)
	(setq test1 "\\fFranklin Gothic Medium|b0|i0|c0|p34;15m\\H0.7x\\S3^;TRUNG")
	(setq test2 "123.45")
	(setq test3 "abc 67.89 def")

	(princ "\n=== TEST EXTRACT-NUMBER ===")
	(princ (strcat "\nTest 1 input: " test1))
	(princ (strcat "\nTest 1 output: " (if (EXTRACT-NUMBER test1) (rtos (EXTRACT-NUMBER test1) 2 2) "nil")))
	(princ (strcat "\nTest 2 input: " test2))
	(princ (strcat "\nTest 2 output: " (if (EXTRACT-NUMBER test2) (rtos (EXTRACT-NUMBER test2) 2 2) "nil")))
	(princ (strcat "\nTest 3 input: " test3))
	(princ (strcat "\nTest 3 output: " (if (EXTRACT-NUMBER test3) (rtos (EXTRACT-NUMBER test3) 2 2) "nil")))
	(princ)
)

; Ham ket noi Excel
(defun CONNECT-EXCEL ( / xlapp xlcells startrow startcol)
	(if (not (setq xlapp (vlax-get-object "Excel.Application")))
		(progn
			(princ "\nKhong tim thay Excel. Dang khoi dong Excel...")
			(setq xlapp (vlax-create-object "Excel.Application"))
		)
	)
	(vlax-put-property xlapp "Visible" :vlax-true)
	(setq xlcells (vlax-get-property (vlax-get-property xlapp "ActiveSheet") "Cells"))
	(setq startrow (vlax-get-property (vlax-get-property xlapp "ActiveCell") "Row"))
	(setq startcol (vlax-get-property (vlax-get-property xlapp "ActiveCell") "Column"))
	(list xlapp xlcells startrow startcol)
)

; Ham ghi cell text - cai tien
(defun SETCELLTEXT ( xlcells row col text / result cell worksheet range celladdress)
	; Thu cach 1: Su dung Item truc tiep
	(setq result (vl-catch-all-apply '(lambda ()
		(setq cell (vlax-get-property xlcells "Item" row col))
		(vlax-put-property cell "Value" text)
	)))

	; Neu cach 1 that bai, thu cach 2: Su dung Range
	(if (vl-catch-all-error-p result)
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER col) (itoa row)))
				(setq worksheet (vlax-get-property xlcells "Parent"))
				(setq range (vlax-get-property worksheet "Range" celladdress))
				(vlax-put-property range "Value" text)
			)))

			; Neu cach 2 cung that bai, thu cach 3: Su dung Cells truc tiep
			(if (vl-catch-all-error-p result)
				(progn
					(setq result (vl-catch-all-apply '(lambda ()
						(vlax-put-property xlcells "Item" row col text)
					)))
					(if (vl-catch-all-error-p result)
						(princ (strcat "\nLoi ghi cell: " (vl-catch-all-error-message result)))
					)
				)
			)
		)
	)
)

; Ham lay handle cua doi tuong
(defun GET-HANDLES ( ss / i handlelist ent)
	(setq handlelist '() i 0)
	(repeat (sslength ss)
		(setq ent (ssname ss i))
		(setq handlelist (cons (vla-get-handle (vlax-ename->vla-object ent)) handlelist))
		(setq i (+ i 1))
	)
	(reverse handlelist)
)

; Ham tao comment text
(defun CREATE-COMMENT-TEXT ( handlelist dwgpath dwgname / commenttext)
	(setq commenttext "")
	(foreach handle handlelist
		(setq commenttext (strcat commenttext handle "; "))
	)
	(setq commenttext (strcat commenttext "\nFileCad: " dwgpath))
	commenttext
)

; Ham ghi comment vao cell - sua loi VLA-OBJECT
(defun WRITE-COMMENT-TO-CELL ( cell commenttext / result comments comment xlapp worksheet celladdress)
	; Kiem tra cell co hop le khong
	(if (and cell (not (vl-catch-all-error-p cell)))
		(progn
			; Thu cach 1: Su dung cell truc tiep
			(setq result (vl-catch-all-apply '(lambda ()
				; Xoa comment cu neu co
				(if (vlax-property-available-p cell "Comment")
					(progn
						(setq comments (vlax-get-property cell "Comment"))
						(if comments
							(vlax-invoke-method comments "Delete")
						)
					)
				)
				; Tao comment moi
				(setq comment (vlax-invoke-method cell "AddComment" commenttext))
				(if comment
					(vlax-put-property comment "Visible" :vlax-false)
				)
			)))

			; Neu that bai, thu cach 2: Su dung Excel Application
			(if (vl-catch-all-error-p result)
				(progn
					(setq result (vl-catch-all-apply '(lambda ()
						(setq xlapp (vlax-get-acad-object))
						(setq xlapp (vlax-get-object "Excel.Application"))
						(setq worksheet (vlax-get-property xlapp "ActiveSheet"))
						(setq celladdress (vlax-get-property cell "Address"))
						(setq cell (vlax-get-property worksheet "Range" celladdress))
						(setq comment (vlax-invoke-method cell "AddComment" commenttext))
						(vlax-put-property comment "Visible" :vlax-false)
					)))

					; Neu van that bai, chi tao comment don gian
					(if (vl-catch-all-error-p result)
						(progn
							(setq result (vl-catch-all-apply '(lambda ()
								(vlax-invoke-method cell "AddComment" commenttext)
							)))
						)
					)
				)
			)
		)
		(princ "\nCell khong hop le!")
	)
)

; Ham chuyen so cot thanh chu cai
(defun COLUMN-NUMBER-TO-LETTER ( col / result)
	(setq result "")
	(while (> col 0)
		(setq col (- col 1))
		(setq result (strcat (chr (+ 65 (rem col 26))) result))
		(setq col (/ col 26))
	)
	result
)

; Ham trich xuat so tu text - co xu ly m2/m
(defun EXTRACT-NUMBER ( text-string / cleaned-text i char in-number current-number result has-unit)
	(if (and text-string (= (type text-string) 'STR) (> (strlen text-string) 0))
		(progn
			; Su dung ham CLEAN-MTEXT de lam sach text
			(setq cleaned-text (CLEAN-MTEXT text-string))

			; Kiem tra co don vi m2 hoac m khong
			(setq has-unit (or (vl-string-search "m2" cleaned-text) (vl-string-search "m" cleaned-text)))

			; Tim so trong chuoi
			(setq result "" i 1 in-number nil current-number "")
			(while (<= i (strlen cleaned-text))
				(setq char (substr cleaned-text i 1))
				(cond
					; Ky tu so
					((and (>= (ascii char) 48) (<= (ascii char) 57))
						(setq in-number T)
						(setq current-number (strcat current-number char))
					)
					; Dau thap phan
					((and in-number (member char '("." ",")))
						(setq current-number (strcat current-number char))
					)
					; Ky tu khac - ket thuc so
					(T
						(if (and in-number (> (strlen current-number) 0))
							(progn
								(setq result current-number)
								(setq i (strlen cleaned-text)) ; Thoat vong lap
							)
							(progn
								(setq in-number nil)
								(setq current-number "")
							)
						)
					)
				)
				(setq i (+ i 1))
			)

			; Neu ket thuc chuoi ma van dang trong so
			(if (and in-number (> (strlen current-number) 0))
				(setq result current-number)
			)

			; Chuyen doi thanh so
			(if (and result (> (strlen result) 0))
				(progn
					(setq result (vl-string-subst "." "," result))
					; Neu co don vi m2 hoac m, tra ve list (so, :unit) de nhan biet
					(if has-unit
						(list (atof result) :unit)
						(atof result)
					)
				)
				nil
			)
		)
		nil
	)
)

; Ham chon doi tuong thong minh - tu dong nhan dien cach chon
(defun SMART-SELECT-OBJECTS ( prompt filter / obj-list ent ss)
	(princ prompt)
	(princ "\n[Click tung doi tuong theo thu tu, hoac Window/Crossing de chon nhieu]")
	(setq obj-list '())

	; Thu chon tung doi tuong truoc
	(setq ent (entsel "\nChon doi tuong dau tien (hoac Enter de chon nhieu): "))

	(if ent
		(progn
			; Da chon 1 doi tuong, tiep tuc chon tung cai
			(setq ent (car ent))
			(if (or (not filter) (wcmatch (cdr (assoc 0 (entget ent))) filter))
				(progn
					(setq obj-list (list ent))
					(princ "\nDa chon 1 doi tuong. Tiep tuc chon tung cai...")

					; Chon tiep cac doi tuong khac
					(while (setq ent (entsel "\nChon doi tuong tiep theo (Enter de ket thuc): "))
						(if (= (type ent) 'LIST)
							(progn
								(setq ent (car ent))
								(if (or (not filter) (wcmatch (cdr (assoc 0 (entget ent))) filter))
									(progn
										(setq obj-list (append obj-list (list ent)))
										(princ (strcat "\nDa chon " (itoa (length obj-list)) " doi tuong"))
									)
									(princ "\nDoi tuong khong hop le!")
								)
							)
						)
					)

					; Tao selection set va luu thu tu
					(setq ss (ssadd))
					(foreach obj obj-list
						(ssadd obj ss)
					)
					(setq *SELECTION-ORDER* obj-list)
					ss
				)
				(progn
					(princ "\nDoi tuong khong hop le!")
					nil
				)
			)
		)
		; Khong chon tung cai, chuyen sang chon nhieu
		(progn
			(princ "\nChon nhieu doi tuong:")
			(setq *SELECTION-ORDER* nil)
			(ssget (if filter (list (cons 0 filter)) nil))
		)
	)
)

; Ham chuyen selection set thanh danh sach text (giu thu tu selection)
(defun CONVERT-SS-TO-TEXTLIST ( ss / i ent obj objname txtcontent txtpos measurement result raw-text)
	(setq result '())

	; Neu co thu tu selection da luu, su dung no
	(if (and *SELECTION-ORDER* (= (length *SELECTION-ORDER*) (sslength ss)))
		(progn
			; Su dung thu tu selection da luu
			(foreach ent *SELECTION-ORDER*
				(setq obj (vlax-ename->vla-object ent)
					  objname (vla-get-objectname obj)
					  txtcontent nil
					  txtpos nil)

				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring obj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring obj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
					; DIMENSION
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						(setq measurement (vla-get-measurement obj))
						(setq txtcontent (rtos measurement 2 2))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
				)

				(if txtcontent
					(setq result (append result (list (list txtcontent txtpos objname))))
				)
			)
		)
		; Neu khong co thu tu selection, su dung thu tu mac dinh
		(progn
			(setq i 0)
			(repeat (sslength ss)
				(setq ent (ssname ss i)
					  obj (vlax-ename->vla-object ent)
					  objname (vla-get-objectname obj)
					  txtcontent nil
					  txtpos nil)

				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring obj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring obj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
					; DIMENSION
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						(setq measurement (vla-get-measurement obj))
						(setq txtcontent (rtos measurement 2 2))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
				)

				(if txtcontent
					(setq result (cons (list txtcontent txtpos objname) result))
				)
				(setq i (+ i 1))
			)
			(setq result (reverse result))
		)
	)
	result
)

; Ham nhay chuot Excel - cai tien
(defun MOVE-CURSOR ( xlcells row col / targetcell result xlapp worksheet celladdress)
	; Thu cach 1: Su dung xlcells Item
	(setq result (vl-catch-all-apply '(lambda ()
		(setq targetcell (vlax-get-property xlcells "Item" row col))
		(vlax-invoke-method targetcell "Select")
	)))

	; Neu that bai, thu cach 2: Su dung Excel Application
	(if (vl-catch-all-error-p result)
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				(setq xlapp (vlax-get-object "Excel.Application"))
				(setq worksheet (vlax-get-property xlapp "ActiveSheet"))
				(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER col) (itoa row)))
				(setq targetcell (vlax-get-property worksheet "Range" celladdress))
				(vlax-invoke-method targetcell "Select")
			)))
		)
	)
)

; ===================================================================
; E1 - XUAT TEXT/DIM VAO CELL (THAY CHO E8 CU)
; ===================================================================

(defun C:E1 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	numlist
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))
			(setq numlist '())
			(setq factor (atof *E6-factor*))

			; Thu thap gia tri so
			(foreach txt textlist
				(setq objname (caddr txt))
				(cond
					; TEXT va MTEXT - trich xuat so
					((or (= objname "AcDbText") (= objname "AcDbMText"))
						(setq numvalue (EXTRACT-NUMBER (car txt)))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq numlist (cons (car numvalue) numlist))
									; Khong co don vi - nhan he so
									(setq numlist (cons (* numvalue factor) numlist))
								)
							)
						)
					)
					; DIMENSION - lay measurement
					((wcmatch objname "*Dimension*")
						(setq numvalue (atof (car txt)))
						(if numvalue
							(setq numlist (cons (* numvalue factor) numlist))
						)
					)
				)
			)

			(setq numlist (reverse numlist))

			; Xac dinh so chu so thap phan
			(setq decimal-places (if (= (atof *E6-factor*) 1.0) 0 3))

			; Tao noi dung cell
			(cond
				; 1 gia tri
				((= (length numlist) 1)
					(setq result-text (rtos (car numlist) 2 decimal-places))
				)
				; 2 gia tri
				((= (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel khi symbol la "+"
						(setq result-text (strcat "=" (rtos (car numlist) 2 decimal-places) "+" (rtos (cadr numlist) 2 decimal-places)))
						; Noi binh thuong voi symbol khac
						(setq result-text (strcat (rtos (car numlist) 2 decimal-places) *E6-symbol* (rtos (cadr numlist) 2 decimal-places)))
					)
				)
				; Nhieu hon 2 gia tri
				((> (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel khi symbol la "+"
						(progn
							(setq result-text (strcat "=" (rtos (car numlist) 2 decimal-places)))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text "+" (rtos num 2 decimal-places)))
							)
						)
						; Noi binh thuong voi symbol khac
						(progn
							(setq result-text (rtos (car numlist) 2 decimal-places))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text *E6-symbol* (rtos num 2 decimal-places)))
							)
						)
					)
				)
				; Khong co gia tri
				(T (setq result-text ""))
			)



			; Ghi vao Excel
			(if (and result-text (> (strlen result-text) 0))
				(progn
					(SETCELLTEXT xlcells startrow startcol result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							; Thu nhieu cach lay cell
							(setq result (vl-catch-all-apply '(lambda ()
								(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
							(if (vl-catch-all-error-p result)
								(progn
									; Thu cach khac
									(setq result (vl-catch-all-apply '(lambda ()
										(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
										(setq worksheet (vlax-get-property xlcells "Parent"))
										(setq targetcell (vlax-get-property worksheet "Range" celladdress))
										(WRITE-COMMENT-TO-CELL targetcell commenttext)
									)))
								)
							)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong co gia tri de ghi")
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; E11 - XUAT TEXT/DIM VAO CELL VOI HE SO NHAN THEM (TUONG TU E1)
; ===================================================================

(defun C:E11 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	numlist
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	multiplier
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Nhap he so nhan them
			(if (not *E11-multiplier*)
				(setq *E11-multiplier* "1")
			)
			(setq multiplier (getreal (strcat "\nNhap he so nhan them <" *E11-multiplier* ">: ")))
			(if (not multiplier)
				(setq multiplier (atof *E11-multiplier*))
				(setq *E11-multiplier* (rtos multiplier 2 6))
			)

			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))
			(setq numlist '())
			(setq factor (atof *E6-factor*))

			; Thu thap gia tri so
			(foreach txt textlist
				(setq objname (caddr txt))
				(cond
					; TEXT va MTEXT - trich xuat so
					((or (= objname "AcDbText") (= objname "AcDbMText"))
						(setq numvalue (EXTRACT-NUMBER (car txt)))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq numlist (cons (car numvalue) numlist))
									; Khong co don vi - nhan he so
									(setq numlist (cons (* numvalue factor) numlist))
								)
							)
						)
					)
					; DIMENSION - lay measurement
					((wcmatch objname "*Dimension*")
						(setq numvalue (atof (car txt)))
						(if numvalue
							(setq numlist (cons (* numvalue factor) numlist))
						)
					)
				)
			)

			(setq numlist (reverse numlist))

			; Xac dinh so chu so thap phan
			(setq decimal-places (if (= (atof *E6-factor*) 1.0) 0 3))

			; Tao noi dung cell voi he so nhan them
			(cond
				; 1 gia tri
				((= (length numlist) 1)
					(if (= multiplier 1.0)
						(setq result-text (rtos (car numlist) 2 decimal-places))
						(setq result-text (strcat "=" (rtos (car numlist) 2 decimal-places) "*" (rtos multiplier 2 6)))
					)
				)
				; 2 gia tri
				((= (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel khi symbol la "+"
						(if (= multiplier 1.0)
							(setq result-text (strcat "=" (rtos (car numlist) 2 decimal-places) "+" (rtos (cadr numlist) 2 decimal-places)))
							(setq result-text (strcat "=(" (rtos (car numlist) 2 decimal-places) "+" (rtos (cadr numlist) 2 decimal-places) ")*" (rtos multiplier 2 6)))
						)
						; Noi binh thuong voi symbol khac
						(if (= multiplier 1.0)
							(setq result-text (strcat (rtos (car numlist) 2 decimal-places) *E6-symbol* (rtos (cadr numlist) 2 decimal-places)))
							(setq result-text (strcat "=(" (rtos (car numlist) 2 decimal-places) *E6-symbol* (rtos (cadr numlist) 2 decimal-places) ")*" (rtos multiplier 2 6)))
						)
					)
				)
				; Nhieu hon 2 gia tri
				((> (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel khi symbol la "+"
						(progn
							(setq result-text (strcat "=(" (rtos (car numlist) 2 decimal-places)))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text "+" (rtos num 2 decimal-places)))
							)
							(if (= multiplier 1.0)
								(setq result-text (strcat result-text ")"))
								(setq result-text (strcat result-text ")*" (rtos multiplier 2 6)))
							)
						)
						; Noi binh thuong voi symbol khac
						(progn
							(setq result-text (strcat "=(" (rtos (car numlist) 2 decimal-places)))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text *E6-symbol* (rtos num 2 decimal-places)))
							)
							(if (= multiplier 1.0)
								(setq result-text (strcat result-text ")"))
								(setq result-text (strcat result-text ")*" (rtos multiplier 2 6)))
							)
						)
					)
				)
				; Khong co gia tri
				(T (setq result-text ""))
			)

			; Ghi vao Excel
			(if (and result-text (> (strlen result-text) 0))
				(progn
					(SETCELLTEXT xlcells startrow startcol result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							; Thu nhieu cach lay cell
							(setq result (vl-catch-all-apply '(lambda ()
								(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
							(if (vl-catch-all-error-p result)
								(progn
									; Thu cach khac
									(setq result (vl-catch-all-apply '(lambda ()
										(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
										(setq worksheet (vlax-get-property xlcells "Parent"))
										(setq targetcell (vlax-get-property worksheet "Range" celladdress))
										(WRITE-COMMENT-TO-CELL targetcell commenttext)
									)))
								)
							)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
					(princ (strcat "\nHe so nhan: " (rtos multiplier 2 6)))
				)
				(princ "\nKhong co gia tri de ghi")
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; E2 - XUAT TEXT/DIM VAO CELL (THAY CHO E7 CU)
; ===================================================================

(defun C:E2 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	numlist
	textonly-list
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))
			(setq numlist '())
			(setq textonly-list '())
			(setq factor (atof *E6-factor*))

			; Phan loai text va dimension
			(foreach txt textlist
				(setq objname (caddr txt))
				(cond
					; TEXT va MTEXT - giu nguyen text
					((or (= objname "AcDbText") (= objname "AcDbMText"))
						(setq textonly-list (cons (car txt) textonly-list))
					)
					; DIMENSION - nhan voi he so
					((wcmatch objname "*Dimension*")
						(setq numvalue (atof (car txt)))
						(if numvalue
							(setq numlist (cons (* numvalue factor) numlist))
						)
					)
				)
			)

			(setq numlist (reverse numlist))
			(setq textonly-list (reverse textonly-list))

			; Tao noi dung cell - uu tien text truoc, sau do dimension
			(setq result-text "")

			; Xu ly text
			(if textonly-list
				(progn
					(setq result-text (car textonly-list))
					(foreach txt (cdr textonly-list)
						(setq result-text (strcat result-text *E6-symbol* txt))
					)
				)
			)

			; Xu ly dimension
			(if numlist
				(progn
					; Xac dinh so chu so thap phan cho dimension
					(setq decimal-places (if (= (atof *E6-factor*) 1.0) 0 3))

					(if (> (strlen result-text) 0)
						(setq result-text (strcat result-text *E6-symbol*))
					)

					(cond
						; 1 dimension
						((= (length numlist) 1)
							(setq result-text (strcat result-text (rtos (car numlist) 2 decimal-places)))
						)
						; Nhieu dimension
						((> (length numlist) 1)
							(if (= *E6-symbol* "+")
								; Tao cong thuc Excel cho dimension khi symbol la "+"
								(progn
									(setq dim-formula (strcat "=" (rtos (car numlist) 2 decimal-places)))
									(foreach num (cdr numlist)
										(setq dim-formula (strcat dim-formula "+" (rtos num 2 decimal-places)))
									)
									(setq result-text (strcat result-text dim-formula))
								)
								; Noi binh thuong voi symbol khac
								(progn
									(setq result-text (strcat result-text (rtos (car numlist) 2 decimal-places)))
									(foreach num (cdr numlist)
										(setq result-text (strcat result-text *E6-symbol* (rtos num 2 decimal-places)))
									)
								)
							)
						)
					)
				)
			)

			; Ghi vao Excel
			(if (and result-text (> (strlen result-text) 0))
				(progn
					(SETCELLTEXT xlcells startrow startcol result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							; Thu nhieu cach lay cell
							(setq result (vl-catch-all-apply '(lambda ()
								(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
							(if (vl-catch-all-error-p result)
								(progn
									; Thu cach khac
									(setq result (vl-catch-all-apply '(lambda ()
										(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
										(setq worksheet (vlax-get-property xlcells "Parent"))
										(setq targetcell (vlax-get-property worksheet "Range" celladdress))
										(WRITE-COMMENT-TO-CELL targetcell commenttext)
									)))
								)
							)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong co gia tri de ghi")
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; E4 - CHI GHI HANDLE VAO COMMENT (TRUOC DAY LA E3)
; ===================================================================

(defun C:E4 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	handlelist
	commenttext
	dwgpath
	dwgname
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong
	(princ "\nChon doi tuong de ghi handle: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Lay handle va tao comment
			(setq handlelist (GET-HANDLES otcontents))
			(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
			; Thu nhieu cach lay cell
			(setq result (vl-catch-all-apply '(lambda ()
				(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
				(WRITE-COMMENT-TO-CELL targetcell commenttext)
			)))
			(if (vl-catch-all-error-p result)
				(progn
					; Thu cach khac
					(setq result (vl-catch-all-apply '(lambda ()
						(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
						(setq worksheet (vlax-get-property xlcells "Parent"))
						(setq targetcell (vlax-get-property worksheet "Range" celladdress))
						(WRITE-COMMENT-TO-CELL targetcell commenttext)
					)))
				)
			)

			; Nhay chuot
			(setq newrow (+ startrow (atoi *E6-jump*)))
			(MOVE-CURSOR xlcells newrow startcol)
			(princ (strcat "\nHoan thanh! Da ghi " (itoa (length handlelist)) " handle vao comment"))
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; E5 - XUAT TABLE VAO EXCEL (TRUOC DAY LA E4)
; ===================================================================

(defun C:E5 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	lpxlist
	lpylist
	blocklist
	linelist
	colwidths
	rowheights
	newrow
	newcol
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon bang
	(princ "\nChon bang de xuat: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Khoi tao danh sach - giong E1 goc
			(setq textlist '() lpxlist '() lpylist '() blocklist '() linelist '())

			; Xu ly thong tin - dung logic E1 goc
			(E4-TABLEINFO otcontents)
			(setq lpxlist (vl-sort lpxlist '<) lpylist (vl-sort lpylist '>))
			(mapcar '(lambda (txt)(E4-GETTXTINFO (entget txt))) textlist)
			(mapcar '(lambda (blk)(E4-GETBLOCKINFO blk)) blocklist)
			(setq colwidths (mapcar '(lambda (x)(- (nth (1+ (vl-position x lpxlist)) lpxlist) x))(reverse (cdr (reverse lpxlist))))
				  rowheights (mapcar '(lambda (x)(- x (nth (1+ (vl-position x lpylist)) lpylist)))(reverse(cdr (reverse lpylist)))))

			; Xuat ra Excel
			(E4-EXPORT-TO-EXCEL xlcells startrow startcol)

			; Nhay chuot den cuoi bang
			(setq newrow (+ startrow (length lpylist) (atoi *E6-jump*)))
			(MOVE-CURSOR xlcells newrow startcol)
			(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
		)
		(princ "\nKhong chon duoc bang!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; HAM PHU TRO CHO E4
; ===================================================================

; Ham phan tich thong tin bang - logic E1 goc
(defun E4-TABLEINFO ( ss / n entlist)
	(setq n 0)
	(repeat (sslength ss)
		(setq entlist (entget (ssname ss n)))
		(cond
			((member (cdr (assoc 0 entlist)) '("LINE" "POLYLINE"))
				(E4-GETLINEPTS entlist)(setq linelist (cons (ssname ss n) linelist)))
			((member (cdr (assoc 0 entlist)) '("TEXT" "MTEXT"))
				(setq textlist (cons (ssname ss n) textlist)))
			((member (cdr (assoc 0 entlist)) '("INSERT"))
				(setq blocklist (cons (ssname ss n) blocklist)))
		)
		(setq n (1+ n))
	)
)

; Ham lay diem cua line va polyline - logic E1 goc
(defun E4-GETLINEPTS (alist / x xpt ypt)
	(foreach x alist
		(if (member (car x) '(10 11))
			(progn
				(if (not (vl-position (setq xpt (atof (rtos (car (trans (cdr x) 0 1)) 2 2))) lpxlist))
					(setq lpxlist (cons xpt lpxlist)))
				(if (not (vl-position (setq ypt (atof (rtos (cadr (trans (cdr x) 0 1)) 2 2))) lpylist))
					(setq lpylist (cons ypt lpylist)))
			)
		)
	)
)

; Ham lay thong tin text va vi tri cell - logic E1 goc chinh xac
(defun E4-GETTXTINFO (alist / x vlaobj pos rpos cpos expos txt)
	(setq txt (cdr (assoc -1 alist)))
	(setq vlaobj (vlax-ename->vla-object txt)
			pos (trans (E4-MIDP vlaobj) 0 1);Midpoint
			rpos (1- (vl-position (cadr pos)(vl-sort (cons (cadr pos) lpylist) '>)));Row Position
			cpos (1- (vl-position (car pos) (vl-sort (cons (car pos) lpxlist) '<))));Column Position
	(if (setq expos (vl-position (list rpos cpos) (mapcar '(lambda (x)(cdr (assoc "Position" x))) tinfo)));if cell is taken
		(setq tinfo
			(E4-REPLACE tinfo expos
				(E4-REPLACE
					(nth expos tinfo)
					2
					(cons "Content"
						(if (> (cadr pos) (cdr (assoc "Order" (nth expos tinfo))));in order according to y position
							(strcat (E4-VLA-FIELDCODE vlaobj) " " (cdr (assoc "Content" (nth expos tinfo))))
							(strcat (cdr (assoc "Content" (nth expos tinfo))) " " (E4-VLA-FIELDCODE vlaobj))
						)
					)
				)
			)
		)
		(setq tinfo
			(cons
				(list
					(cons "Order" (cadr pos))
					(cons "Position" (list rpos cpos));Position
					(cons "Content" (E4-VLA-FIELDCODE vlaobj));Content
				)
				tinfo
			)
		)
	)
)

; Ham lay thong tin block va vi tri cell - logic E1 goc chinh xac
(defun E4-GETBLOCKINFO (obj / pos rpos cpos bname objid bobj attid)
	(if (= (type obj) 'ename) (setq obj (vlax-ename->vla-object obj)))
	(setq pos (trans (E4-MIDP obj) 0 1)
		rpos (1- (vl-position (cadr pos) (vl-sort (cons (cadr pos) lpylist) '>)));Row Position
		cpos (1- (vl-position (car pos) (vl-sort (cons (car pos) lpxlist) '<)));Column Position
		bname (vla-get-name obj);Block Name
		bobj (vla-item (vla-get-blocks ActDoc) bname);Block Object
		attid '())
	(vlax-for i bobj
		(if (eq (vla-get-objectname i) "AcDbAttributeDefinition");If item is an attribute
			(setq attid (append attid (list (vla-get-objectid i))));List Attribute Id
		)
	)
	(setq objid (vla-get-objectid bobj));Block Object Id
	(setq binfo
		(cons
			(list
				(cons "Name" bname)
				(cons "Position" (list rpos cpos))
				(cons "ObjID" objid)
				(cons "Attributes"
					(mapcar
						'(lambda (x / attobj raw-text objname)
							(setq attobj (vla-objectidtoobject ActDoc x))
							(setq objname (vla-get-objectname attobj))
							(setq raw-text (vla-get-textstring attobj))
							; Neu la MTEXT, xu ly format text
							(if (= objname "AcDbMText")
								(cons (vla-get-tagstring attobj) (CLEAN-MTEXT raw-text))
								(cons (vla-get-tagstring attobj) raw-text)
							)
						)
						attid
					)
				)
				(cons "Scale" (vla-get-xscalefactor obj))
			)
			binfo
		)
	)
)

; Ham xuat bang ra Excel cho E4 - logic E1 goc chinh xac
(defun E4-EXPORT-TO-EXCEL ( xlcells startrow startcol / r c)
	; Khoi tao bien tinfo va binfo giong E1 goc
	(setq tinfo '() binfo '())

	; Xu ly text va block giong E1 goc
	(mapcar '(lambda (txt)(E4-GETTXTINFO (entget txt))) textlist)
	(mapcar '(lambda (blk)(E4-GETBLOCKINFO blk)) blocklist)

	; Dat text vao Excel tu vi tri con tro - giong E1 goc
	(mapcar '(lambda (x / r c)
			(setq r (+ startrow (cadr (assoc "Position" x)))
				  c (+ startcol (caddr (assoc "Position" x))))
			(SETCELLTEXT xlcells r c (cdr (assoc "Content" x)))
		)
		tinfo
	)

	; Dat thong tin block vao Excel tu vi tri con tro - giong E1 goc
	(mapcar '(lambda (x / r c bstring)
			(setq r (+ startrow (cadr (assoc "Position" x)))
				  c (+ startcol (caddr (assoc "Position" x))))
			(setq bstring "")
			(if (cdr (assoc "Attributes" x))
				(progn
				(mapcar
				'(lambda (y )
				(setq bstring (strcat ":"(cdr y) bstring)))
				(cdr (assoc "Attributes" x)))
				(SETCELLTEXT xlcells r c (strcat "Block:"(cdr (assoc "Name" x)) bstring))
				)
			)
		)
		binfo
	)

	; Dong khung neu can - sua lai kich thuoc
	(if (= *E6-frame* "Y")
		(E4-ADD-BORDERS xlcells startrow startcol (+ startrow (- (length lpylist) 1) -1) (+ startcol (- (length lpxlist) 1) -1))
	)
)

; Ham tim text tai vi tri - logic E1 goc
(defun E4-FIND-TEXT-AT-POSITION ( x y / best-txt min-dist txtitem txtpos dist)
	(setq best-txt nil min-dist 1e10)
	(foreach txtitem txtinfo
		(setq txtpos (cadr txtitem))
		(setq dist (sqrt (+ (expt (- x (car txtpos)) 2) (expt (- y (cadr txtpos)) 2))))
		(if (< dist min-dist)
			(progn
				(setq min-dist dist)
				(setq best-txt txtitem)
			)
		)
	)
	best-txt
)

; Ham ho tro - Midpoint cho E4
(defun E4-MIDP ( obj / )
	(vl-catch-all-apply '(lambda ()
		(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
	))
)

; Ham ho tro - VLA-FIELDCODE cho E4 (da sua loi MTEXT format)
(defun E4-VLA-FIELDCODE ( obj / raw-text objname)
	(setq objname (vla-get-objectname obj))
	(setq raw-text (vl-catch-all-apply '(lambda ()
		(vla-get-textstring obj)
	)))

	; Neu la MTEXT, xu ly format text
	(if (and (not (vl-catch-all-error-p raw-text)) (= objname "AcDbMText"))
		(CLEAN-MTEXT raw-text)
		raw-text
	)
)

; Ham ho tro - Replace cho E4
(defun E4-REPLACE ( lst index new-item / )
	(if (and (>= index 0) (< index (length lst)))
		(append (E4-SUBLIST lst 0 index) (list new-item) (E4-SUBLIST lst (+ index 1) (- (length lst) index 1)))
		lst
	)
)

; Ham ho tro - Sublist cho E4
(defun E4-SUBLIST ( lst start len / result i)
	(setq result '() i 0)
	(while (and (< i len) (< (+ start i) (length lst)))
		(setq result (append result (list (nth (+ start i) lst))))
		(setq i (+ i 1))
	)
	result
)

; Ham dong khung cho E4 - sua loi
(defun E4-ADD-BORDERS ( xlcells startrow startcol endrow endcol / range worksheet xlapp startaddr endaddr result)
	; Thu cach 1: Su dung Range truc tiep
	(setq result (vl-catch-all-apply '(lambda ()
		(setq startaddr (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
		(setq endaddr (strcat (COLUMN-NUMBER-TO-LETTER endcol) (itoa endrow)))
		(setq worksheet (vlax-get-property xlcells "Parent"))
		(setq range (vlax-get-property worksheet "Range" (strcat startaddr ":" endaddr)))
		(vlax-put-property (vlax-get-property range "Borders") "LineStyle" 1)
	)))

	; Neu that bai, thu cach 2: Su dung Excel Application
	(if (vl-catch-all-error-p result)
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				(setq xlapp (vlax-get-object "Excel.Application"))
				(setq worksheet (vlax-get-property xlapp "ActiveSheet"))
				(setq startaddr (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
				(setq endaddr (strcat (COLUMN-NUMBER-TO-LETTER endcol) (itoa endrow)))
				(setq range (vlax-get-property worksheet "Range" (strcat startaddr ":" endaddr)))
				; Dong tat ca cac vien
				(vlax-put-property (vlax-get-property range "Borders" 7) "LineStyle" 1)  ; xlEdgeLeft
				(vlax-put-property (vlax-get-property range "Borders" 8) "LineStyle" 1)  ; xlEdgeTop
				(vlax-put-property (vlax-get-property range "Borders" 9) "LineStyle" 1)  ; xlEdgeBottom
				(vlax-put-property (vlax-get-property range "Borders" 10) "LineStyle" 1) ; xlEdgeRight
				(vlax-put-property (vlax-get-property range "Borders" 11) "LineStyle" 1) ; xlInsideVertical
				(vlax-put-property (vlax-get-property range "Borders" 12) "LineStyle" 1) ; xlInsideHorizontal
			)))
		)
	)
)

; ===================================================================
; E3 - XUAT THEO 3 CHE DO (TRUOC DAY LA E5)
; ===================================================================

(defun C:E3 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	mode
	newrow
	newcol
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Chuyen doi selection set - su dung setting Number nhu cu
			(if (= *E6-number* "Y")
				(setq textlist (E5-CONVERT-SS-TO-TEXTLIST-NUMBER otcontents))
				(setq textlist (E5-CONVERT-SS-TO-TEXTLIST otcontents))
			)

			; Hien thi menu
			(princ "\nChon che do xuat:")
			(princ "\n1. Cot")
			(princ "\n2. Hang")
			(princ "\n3. Mang")
			(setq mode (getint "\nNhap lua chon [1-3]: "))

			(cond
				; Che do cot
				((= mode 1)
					(E5-EXPORT-COL textlist xlcells startrow startcol)
					; Nhay tu o cuoi cot xuong duoi
					(setq newrow (+ startrow (length textlist) (atoi *E6-jump*)) newcol startcol)
				)
				; Che do hang
				((= mode 2)
					(E5-EXPORT-ROW textlist xlcells startrow startcol)
					; Nhay tu o cuoi hang sang phai
					(setq newrow startrow newcol (+ startcol (length textlist) (atoi *E6-jump*)))
				)
				; Che do mang
				((= mode 3)
					(E5-EXPORT-ARRAY textlist xlcells startrow startcol)
					; Nhay tu dong cuoi bang xuong duoi
					(setq newrow (+ startrow (E5-GET-ARRAY-ROWS textlist) (atoi *E6-jump*)) newcol startcol)
				)
				; Lua chon khong hop le
				(T (princ "\nLua chon khong hop le!"))
			)

			; Ghi handle neu can
			(if (= *E6-handle* "Y")
				(progn
					(setq dwgpath (vla-get-fullname (vla-get-activedocument (vlax-get-acad-object))))
					(setq dwgname (vl-filename-base dwgpath))
					(setq handlelist (GET-HANDLES otcontents))
					(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
					; Thu nhieu cach lay cell
					(setq result (vl-catch-all-apply '(lambda ()
						(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
						(WRITE-COMMENT-TO-CELL targetcell commenttext)
					)))
					(if (vl-catch-all-error-p result)
						(progn
							; Thu cach khac
							(setq result (vl-catch-all-apply '(lambda ()
								(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
								(setq worksheet (vlax-get-property xlcells "Parent"))
								(setq targetcell (vlax-get-property worksheet "Range" celladdress))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
						)
					)
				)
			)

			; Nhay chuot
			(if (and newrow newcol)
				(progn
					(MOVE-CURSOR xlcells newrow newcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa newcol)))
				)
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; HAM PHU TRO CHO E5
; ===================================================================

; Ham xuat theo cot - ho tro thu tu selection
(defun E5-EXPORT-COL ( textlist xlcells startrow startcol / sorted-textlist row tolerance xa ya xb yb)
	; Neu co thu tu selection, giu nguyen thu tu, neu khong thi sort
	(if (and *SELECTION-ORDER* (= (length *SELECTION-ORDER*) (length textlist)))
		; Giu nguyen thu tu selection
		(setq sorted-textlist textlist)
		; Sort theo toa do nhu cu
		(progn
			(setq tolerance (atof *E6-tolerance*))
			(setq sorted-textlist (vl-sort textlist '(lambda (a b)
				(setq xa (car (cadr a)) ya (cadr (cadr a))
					  xb (car (cadr b)) yb (cadr (cadr b)))
				; Neu cung cot (X gan bang nhau), sap xep theo Y (tren xuong)
				(if (< (abs (- xa xb)) tolerance)
					(> ya yb) ; Cung cot: tu tren xuong (Y giam dan)
					(< xa xb) ; Khac cot: tu trai sang phai (X tang dan)
				)
			)))
		)
	)
	(setq row startrow)
	(foreach txt sorted-textlist
		(SETCELLTEXT xlcells row startcol (car txt))
		(setq row (+ row 1))
	)
)

; Ham xuat theo hang - ho tro thu tu selection
(defun E5-EXPORT-ROW ( textlist xlcells startrow startcol / sorted-textlist col tolerance xa ya xb yb)
	; Neu co thu tu selection, giu nguyen thu tu, neu khong thi sort
	(if (and *SELECTION-ORDER* (= (length *SELECTION-ORDER*) (length textlist)))
		; Giu nguyen thu tu selection
		(setq sorted-textlist textlist)
		; Sort theo toa do nhu cu
		(progn
			(setq tolerance (atof *E6-tolerance*))
			(setq sorted-textlist (vl-sort textlist '(lambda (a b)
				(setq xa (car (cadr a)) ya (cadr (cadr a))
					  xb (car (cadr b)) yb (cadr (cadr b)))
				; Neu cung hang (Y gan bang nhau), sap xep theo X (trai phai)
				(if (< (abs (- ya yb)) tolerance)
					(< xa xb) ; Cung hang: tu trai sang phai (X tang dan)
					(> ya yb) ; Khac hang: tu tren xuong (Y giam dan)
				)
			)))
		)
	)
	(setq col startcol)
	(foreach txt sorted-textlist
		(SETCELLTEXT xlcells startrow col (car txt))
		(setq col (+ col 1))
	)
)

; Ham xuat theo mang
(defun E5-EXPORT-ARRAY ( textlist xlcells startrow startcol / unique-x unique-y tolerance grid-row grid-col excel-row excel-col)
	(setq tolerance (atof *E6-tolerance*))

	; Thu thap toa do X va Y
	(setq all-x '() all-y '())
	(foreach txt textlist
		(setq txt-pos (cadr txt))
		(setq all-x (cons (car txt-pos) all-x))
		(setq all-y (cons (cadr txt-pos) all-y))
	)

	; Tim cac toa do duy nhat
	(setq unique-x (E5-GET-UNIQUE-COORDS all-x tolerance))
	(setq unique-y (E5-GET-UNIQUE-COORDS all-y tolerance))
	(setq unique-x (vl-sort unique-x '<))
	(setq unique-y (vl-sort unique-y '>))

	; Xuat theo luoi
	(setq grid-row 0 excel-row startrow)
	(foreach y-coord unique-y
		(setq grid-col 0 excel-col startcol)
		(foreach x-coord unique-x
			(setq txt-at-pos (E5-FIND-TEXT-AT-GRID x-coord y-coord textlist tolerance))
			(if txt-at-pos
				(SETCELLTEXT xlcells excel-row excel-col (car txt-at-pos))
			)
			(setq grid-col (+ grid-col 1) excel-col (+ excel-col 1))
		)
		(setq grid-row (+ grid-row 1) excel-row (+ excel-row 1))
	)
)

; Ham lay so hang cua mang
(defun E5-GET-ARRAY-ROWS ( textlist / unique-y tolerance all-y)
	(setq tolerance (atof *E6-tolerance*))
	(setq all-y '())
	(foreach txt textlist
		(setq all-y (cons (cadr (cadr txt)) all-y))
	)
	(setq unique-y (E5-GET-UNIQUE-COORDS all-y tolerance))
	(length unique-y)
)

; Ham lay cac toa do duy nhat
(defun E5-GET-UNIQUE-COORDS ( coord-list tolerance / unique-coords coord)
	(setq unique-coords '())
	(foreach coord coord-list
		(if (not (E5-COORD-EXISTS coord unique-coords tolerance))
			(setq unique-coords (cons coord unique-coords))
		)
	)
	unique-coords
)

; Ham kiem tra toa do da ton tai
(defun E5-COORD-EXISTS ( coord coord-list tolerance / exists)
	(setq exists nil)
	(foreach existing-coord coord-list
		(if (< (abs (- coord existing-coord)) tolerance)
			(setq exists T)
		)
	)
	exists
)

; Ham tim text tai vi tri luoi
(defun E5-FIND-TEXT-AT-GRID ( x y textlist tolerance / best-txt min-dist txt txt-pos dist)
	(setq best-txt nil min-dist 1e10)
	(foreach txt textlist
		(setq txt-pos (cadr txt))
		(setq dist (sqrt (+ (expt (- x (car txt-pos)) 2) (expt (- y (cadr txt-pos)) 2))))
		(if (and (< dist min-dist) (< dist tolerance))
			(progn
				(setq min-dist dist)
				(setq best-txt txt)
			)
		)
	)
	best-txt
)

; ===================================================================
; E6 - SETTINGS (THIET LAP TOAN CUC) - GIAO DIEN DCL
; ===================================================================

(defun C:E6 ( / DCH DCL DIALOG FLAG TITLE)
	(setq title "Thiet lap CAD to Excel v2024")
	(setq dialog "E6SETTINGS")
	(setq dcl (vl-filename-mktemp nil nil ".dcl"))

	(cond
		((not (E6-WRITE-DCL dcl dialog title))
			(princ "\nKhong the tao file DCL.")
		)
		((<= (setq dch (load_dialog dcl)) 0)
			(princ "\nKhong the load file DCL.")
		)
		(t
			(if (not (new_dialog dialog dch))
				(progn
					(princ "\nKhong the tao dialog.")
					(exit)
				)
			)

			; Thiet lap gia tri ban dau
			(E6-SET-TILES)

			; Gan action cho cac control
			(action_tile "E6-HANDLE" "(E6-UPDATE-PREVIEW)")
			(action_tile "E6-FRAME" "(E6-UPDATE-PREVIEW)")
			(action_tile "E6-NUMBER" "(E6-UPDATE-PREVIEW)")
			(action_tile "E6-SYMBOL" "(E6-UPDATE-PREVIEW)")
			(action_tile "E6-FACTOR" "(E6-UPDATE-PREVIEW)")
			(action_tile "E6-JUMP" "(E6-UPDATE-PREVIEW)")
			(action_tile "E6-TOLERANCE" "(E6-UPDATE-PREVIEW)")
			(action_tile "accept" "(E6-SAVE-SETTINGS)")
			(action_tile "cancel" "(done_dialog)")
			(action_tile "E6-RESET" "(E6-RESET-SETTINGS)")

			; Cap nhat preview ban dau
			(E6-UPDATE-PREVIEW)

			; Hien thi dialog
			(start_dialog)
			(unload_dialog dch)
		)
	)
	(princ)
)

; ===================================================================
; HAM PHU TRO CHO E6 DCL
; ===================================================================

; Ham tao file DCL cho E6
(defun E6-WRITE-DCL ( dcl dialog title / des lst)
	(setq lst
		(list
			dialog
			": dialog"
			"{"
			(strcat "\tlabel = \"" title "\";")
			"\t: column"
			"\t\t{"
			"\t\t: boxed_row"
			"\t\t\t{"
			"\t\t\tlabel = \"Tuy chon chinh\";"
			"\t\t\t: boxed_column"
			"\t\t\t\t{"
			"\t\t\t\tlabel = \"Ghi du lieu\";"
			"\t\t\t\t: toggle { key = \"E6-HANDLE\"; label = \"Ghi Handle vao Comment\"; }"
			"\t\t\t\t: toggle { key = \"E6-FRAME\"; label = \"Dong khung cho bang\"; }"
			"\t\t\t\t: toggle { key = \"E6-NUMBER\"; label = \"E3 chi lay so (nhu E1)\"; }"
			"\t\t\t\t}"
			"\t\t\t: boxed_column"
			"\t\t\t\t{"
			"\t\t\t\tlabel = \"Thong so\";"
			"\t\t\t\t: edit_box { key = \"E6-SYMBOL\"; label = \"Ky tu noi\"; edit_width = 8; }"
			"\t\t\t\t: edit_box { key = \"E6-FACTOR\"; label = \"He so\"; edit_width = 8; }"
			"\t\t\t\t: edit_box { key = \"E6-JUMP\"; label = \"So o nhay\"; edit_width = 8; }"
			"\t\t\t\t: edit_box { key = \"E6-TOLERANCE\"; label = \"Sai so cho phep\"; edit_width = 8; }"
			"\t\t\t\t}"
			"\t\t\t}"
			"\t\t: boxed_column"
			"\t\t\t{"
			"\t\t\tlabel = \"Xem truoc\";"
			"\t\t\t: text { key = \"E6-PREVIEW\"; label = \"Thiet lap hien tai:\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-HANDLE\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-FRAME\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-SYMBOL\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-FACTOR\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-JUMP\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-TOLERANCE\"; }"
			"\t\t\t: text { key = \"E6-PREVIEW-NUMBER\"; }"
			"\t\t\t}"
			"\t\t: boxed_column"
			"\t\t\t{"
			"\t\t\tlabel = \"Huong dan\";"
			"\t\t\t: text { value = \"Handle: Ghi ma doi tuong vao comment Excel\"; }"
			"\t\t\t: text { value = \"Frame: Dong khung cho bang (E5)\"; }"
			"\t\t\t: text { value = \"Symbol: Ky tu noi cac gia tri\"; }"
			"\t\t\t: text { value = \"  '+' se tao cong thuc Excel\"; }"
			"\t\t\t: text { value = \"Factor: He so nhan (1=khong doi, 0.001=mm->m)\"; }"
			"\t\t\t: text { value = \"Jump: So o nhay sau khi ghi\"; }"
			"\t\t\t: text { value = \"Tolerance: Sai so cho phep (pixel)\"; }"
			"\t\t\t: text { value = \"Number: E3 chi lay so, nhan he so\"; }"
			"\t\t\t}"
			"\t\t: row"
			"\t\t\t{"
			"\t\t\t: spacer { width = 1; }"
			"\t\t\t: button { key = \"E6-RESET\"; label = \"Mac dinh\"; fixed_width = true; width = 12; }"
			"\t\t\t: button { key = \"accept\"; label = \"OK\"; is_default = true; fixed_width = true; width = 12; }"
			"\t\t\t: button { key = \"cancel\"; label = \"Huy\"; is_cancel = true; fixed_width = true; width = 12; }"
			"\t\t\t: spacer { width = 1; }"
			"\t\t\t}"
			"\t\t}"
			"}"
		)
	)
	(setq des (open dcl "w"))
	(foreach x lst (write-line x des))
	(setq des (close des))
	(while (not (findfile dcl)))
	dcl
)

; Ham thiet lap gia tri ban dau cho cac tile
(defun E6-SET-TILES ( / )
	; Thiet lap checkbox
	(set_tile "E6-HANDLE" (if (= *E6-handle* "Y") "1" "0"))
	(set_tile "E6-FRAME" (if (= *E6-frame* "Y") "1" "0"))
	(set_tile "E6-NUMBER" (if (= *E6-number* "Y") "1" "0"))

	; Thiet lap edit box
	(set_tile "E6-SYMBOL" *E6-symbol*)
	(set_tile "E6-FACTOR" *E6-factor*)
	(set_tile "E6-JUMP" *E6-jump*)
	(set_tile "E6-TOLERANCE" *E6-tolerance*)
)

; Ham cap nhat preview
(defun E6-UPDATE-PREVIEW ( / handle-val frame-val number-val symbol-val factor-val jump-val tolerance-val)
	; Lay gia tri hien tai tu dialog
	(setq handle-val (if (= (get_tile "E6-HANDLE") "1") "Y" "N"))
	(setq frame-val (if (= (get_tile "E6-FRAME") "1") "Y" "N"))
	(setq number-val (if (= (get_tile "E6-NUMBER") "1") "Y" "N"))
	(setq symbol-val (get_tile "E6-SYMBOL"))
	(setq factor-val (get_tile "E6-FACTOR"))
	(setq jump-val (get_tile "E6-JUMP"))
	(setq tolerance-val (get_tile "E6-TOLERANCE"))

	; Cap nhat preview
	(set_tile "E6-PREVIEW-HANDLE" (strcat "Handle: " handle-val))
	(set_tile "E6-PREVIEW-FRAME" (strcat "Frame: " frame-val))
	(set_tile "E6-PREVIEW-SYMBOL" (strcat "Symbol: " symbol-val))
	(set_tile "E6-PREVIEW-FACTOR" (strcat "Factor: " factor-val))
	(set_tile "E6-PREVIEW-JUMP" (strcat "Jump: " jump-val))
	(set_tile "E6-PREVIEW-TOLERANCE" (strcat "Tolerance: " tolerance-val))
	(set_tile "E6-PREVIEW-NUMBER" (strcat "Number: " number-val))
)

; Ham luu thiet lap
(defun E6-SAVE-SETTINGS ( / )
	; Validate du lieu
	(cond
		((= (get_tile "E6-SYMBOL") "")
			(alert "Ky tu noi khong duoc de trong!")
			(mode_tile "E6-SYMBOL" 2)
		)
		((<= (atof (get_tile "E6-FACTOR")) 0)
			(alert "He so phai lon hon 0!")
			(mode_tile "E6-FACTOR" 2)
		)
		((<= (atoi (get_tile "E6-JUMP")) 0)
			(alert "So o nhay phai lon hon 0!")
			(mode_tile "E6-JUMP" 2)
		)
		((<= (atof (get_tile "E6-TOLERANCE")) 0)
			(alert "Sai so cho phep phai lon hon 0!")
			(mode_tile "E6-TOLERANCE" 2)
		)
		(t
			; Luu cac gia tri
			(setq *E6-handle* (if (= (get_tile "E6-HANDLE") "1") "Y" "N"))
			(setq *E6-frame* (if (= (get_tile "E6-FRAME") "1") "Y" "N"))
			(setq *E6-number* (if (= (get_tile "E6-NUMBER") "1") "Y" "N"))
			(setq *E6-symbol* (get_tile "E6-SYMBOL"))
			(setq *E6-factor* (get_tile "E6-FACTOR"))
			(setq *E6-jump* (get_tile "E6-JUMP"))
			(setq *E6-tolerance* (get_tile "E6-TOLERANCE"))

			(princ "\n=== DA LUU THIET LAP ===")
			(princ (strcat "\nHandle: " *E6-handle*))
			(princ (strcat "\nFrame: " *E6-frame*))
			(princ (strcat "\nSymbol: " *E6-symbol*))
			(princ (strcat "\nFactor: " *E6-factor*))
			(princ (strcat "\nJump: " *E6-jump*))
			(princ (strcat "\nTolerance: " *E6-tolerance*))
			(princ (strcat "\nNumber: " *E6-number*))

			; Dong dialog sau khi luu
			(done_dialog)
		)
	)
)

; Ham reset ve gia tri mac dinh
(defun E6-RESET-SETTINGS ( / )
	; Reset ve gia tri mac dinh
	(setq *E6-handle* "Y")
	(setq *E6-frame* "N")
	(setq *E6-symbol* "+")
	(setq *E6-factor* "1")
	(setq *E6-jump* "3")
	(setq *E6-tolerance* "50")
	(setq *E6-number* "N")

	; Cap nhat lai dialog
	(E6-SET-TILES)
	(E6-UPDATE-PREVIEW)

	(princ "\nDa reset ve gia tri mac dinh!")
)

; ===================================================================
; E7 - CONG TAC HANDLE (TOGGLE ON/OFF)
; ===================================================================

(defun C:E7 ( / )
	; Dao trang thai Handle
	(if (= *E6-handle* "Y")
		(progn
			(setq *E6-handle* "N")
			(princ "\n=== HANDLE: TAT ===")
			(princ "\nCac lenh E1, E2, E3, E4, E5, ET se KHONG ghi handle vao comment")
		)
		(progn
			(setq *E6-handle* "Y")
			(princ "\n=== HANDLE: MO ===")
			(princ "\nCac lenh E1, E2, E3, E4, E5, ET se ghi handle vao comment")
		)
	)
	(princ (strcat "\nTrang thai hien tai: Handle = " *E6-handle*))
	(princ)
)

; ===================================================================
; E8 - CONG TAC FACTOR (TOGGLE 1 <-> 0.001)
; ===================================================================

(defun C:E8 ( / )
	; Dao giua 1 va 0.001
	(if (= *E6-factor* "1")
		(progn
			(setq *E6-factor* "0.001")
			(princ "\n=== FACTOR: 0.001 (mm -> m) ===")
			(princ "\nCac lenh E1, E2, E3, ET se nhan gia tri voi 0.001")
			(princ "\nDung de chuyen doi tu mm sang m")
		)
		(progn
			(setq *E6-factor* "1")
			(princ "\n=== FACTOR: 1 (khong doi) ===")
			(princ "\nCac lenh E1, E2, E3, ET se giu nguyen gia tri")
			(princ "\nKhong nhan he so")
		)
	)
	(princ (strcat "\nTrang thai hien tai: Factor = " *E6-factor*))
	(princ)
)

; ===================================================================
; E9 - CONG TAC NUMBER (TOGGLE Y/N)
; ===================================================================

(defun C:E9 ( / )
	; Dao trang thai Number
	(if (= *E6-number* "Y")
		(progn
			(setq *E6-number* "N")
			(princ "\n=== NUMBER: TAT ===")
			(princ "\nE3 se giu nguyen text nhu hien tai")
			(princ "\nKhong trich xuat so, khong nhan he so")
		)
		(progn
			(setq *E6-number* "Y")
			(princ "\n=== NUMBER: MO ===")
			(princ "\nE3 se chi lay so tu text (nhu E1)")
			(princ "\nTrich xuat so, nhan he so, bo qua hau to m2/m")
		)
	)
	(princ (strcat "\nTrang thai hien tai: Number = " *E6-number*))
	(princ)
)

; ===================================================================
; ET - LENH TONG HOP TOI UU
; ===================================================================

(defun C:ET ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	mode
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong
	(princ "\nChon doi tuong: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Hien thi menu chinh
			(princ "\n=== CAD TO EXCEL ===")
			(princ "\nChon che do:")
			(princ "\n1. Table - Xuat bang")
			(princ "\n2. Col - Xuat theo cot")
			(princ "\n3. Row - Xuat theo hang")
			(princ "\n4. Cell - Xuat vao 1 o")
			(princ "\n5. Array - Xuat theo mang")
			(princ "\n6. Handle - Chi ghi handle")
			(princ "\n7. Value - Ghi gia tri + handle")
			(setq mode (getint "\nNhap lua chon [1-7]: "))

			(cond
				; Table mode
				((= mode 1) (ET-EXECUTE-TABLE otcontents xlcells startrow startcol))
				; Col mode
				((= mode 2) (ET-EXECUTE-COL otcontents xlcells startrow startcol))
				; Row mode
				((= mode 3) (ET-EXECUTE-ROW otcontents xlcells startrow startcol))
				; Cell mode
				((= mode 4) (ET-EXECUTE-CELL otcontents xlcells startrow startcol))
				; Array mode
				((= mode 5) (ET-EXECUTE-ARRAY otcontents xlcells startrow startcol))
				; Handle mode
				((= mode 6) (ET-EXECUTE-HANDLE otcontents xlcells startrow startcol))
				; Value mode
				((= mode 7) (ET-EXECUTE-VALUE otcontents xlcells startrow startcol))
				; Lua chon khong hop le
				(T (princ "\nLua chon khong hop le!"))
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; HAM PHU TRO CHUNG - E5-CONVERT-SS-TO-TEXTLIST
; ===================================================================

; Ham chuyen selection set thanh list text cho E5 (ho tro dimension)
(defun E5-CONVERT-SS-TO-TEXTLIST ( ss / n ent textlist txtobj txtcontent txtpos objname measurement)
	(setq textlist '())

	; Neu co thu tu selection da luu, su dung no
	(if (and *SELECTION-ORDER* (= (length *SELECTION-ORDER*) (sslength ss)))
		(progn
			; Su dung thu tu selection da luu
			(foreach ent *SELECTION-ORDER*
				(setq txtobj (vlax-ename->vla-object ent))
				(setq objname (vla-get-objectname txtobj))

				; Xu ly theo loai doi tuong
				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; DIMENSION (tat ca cac loai)
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						; Lay gia tri Measurement va lam tron 3 chu so thap phan
						(setq measurement (vla-get-measurement txtobj))
						(setq txtcontent (rtos measurement 2 3)) ; Lam tron 3 chu so thap phan
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; Loai khac - bo qua
					(T
						(setq txtcontent nil txtpos nil)
					)
				)

				; Them vao list neu co noi dung
				(if (and txtcontent txtpos)
					(setq textlist (append textlist (list (list txtcontent txtpos))))
				)
			)
		)
		; Neu khong co thu tu selection, su dung thu tu mac dinh
		(progn
			(setq n 0)
			(repeat (sslength ss)
				(setq ent (ssname ss n))
				(setq txtobj (vlax-ename->vla-object ent))
				(setq objname (vla-get-objectname txtobj))

				; Xu ly theo loai doi tuong
				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; DIMENSION (tat ca cac loai)
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						; Lay gia tri Measurement va lam tron 3 chu so thap phan
						(setq measurement (vla-get-measurement txtobj))
						(setq txtcontent (rtos measurement 2 3)) ; Lam tron 3 chu so thap phan
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; Loai khac - bo qua
					(T
						(setq txtcontent nil txtpos nil)
					)
				)

				; Them vao list neu co noi dung
				(if (and txtcontent txtpos)
					(setq textlist (append textlist (list (list txtcontent txtpos))))
				)
				(setq n (+ n 1))
			)
		)
	)
	textlist
)

; Ham chuyen selection set thanh list text cho E5 - che do Number (chi lay so)
(defun E5-CONVERT-SS-TO-TEXTLIST-NUMBER ( ss / n ent textlist txtobj txtcontent txtpos objname measurement numvalue factor decimal-places)
	(setq textlist '())
	(setq factor (atof *E6-factor*))
	(setq decimal-places (if (= factor 1.0) 0 3))

	; Neu co thu tu selection da luu, su dung no
	(if (and *SELECTION-ORDER* (= (length *SELECTION-ORDER*) (sslength ss)))
		(progn
			; Su dung thu tu selection da luu
			(foreach ent *SELECTION-ORDER*
				(setq txtobj (vlax-ename->vla-object ent))
				(setq objname (vla-get-objectname txtobj))

				; Xu ly theo loai doi tuong
				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
						; Trich xuat so va nhan he so
						(setq numvalue (EXTRACT-NUMBER txtcontent))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq txtcontent (rtos (car numvalue) 2 decimal-places))
									; Khong co don vi - nhan he so
									(setq txtcontent (rtos (* numvalue factor) 2 decimal-places))
								)
							)
							; Neu khong trich xuat duoc so, giu nguyen text
							(setq txtcontent txtcontent)
						)
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
						; Trich xuat so va nhan he so
						(setq numvalue (EXTRACT-NUMBER txtcontent))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq txtcontent (rtos (car numvalue) 2 decimal-places))
									; Khong co don vi - nhan he so
									(setq txtcontent (rtos (* numvalue factor) 2 decimal-places))
								)
							)
							; Neu khong trich xuat duoc so, giu nguyen text
							(setq txtcontent txtcontent)
						)
					)
					; DIMENSION (tat ca cac loai)
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						; Lay gia tri Measurement, nhan he so va lam tron
						(setq measurement (vla-get-measurement txtobj))
						(setq txtcontent (rtos (* measurement factor) 2 decimal-places))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; Loai khac - bo qua
					(T
						(setq txtcontent nil txtpos nil)
					)
				)

				; Them vao list neu co noi dung
				(if (and txtcontent txtpos)
					(setq textlist (append textlist (list (list txtcontent txtpos))))
				)
			)
		)
		; Neu khong co thu tu selection, su dung thu tu mac dinh
		(progn
			(setq n 0)
			(repeat (sslength ss)
				(setq ent (ssname ss n))
				(setq txtobj (vlax-ename->vla-object ent))
				(setq objname (vla-get-objectname txtobj))

				; Xu ly theo loai doi tuong
				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
						; Trich xuat so va nhan he so
						(setq numvalue (EXTRACT-NUMBER txtcontent))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq txtcontent (rtos (car numvalue) 2 decimal-places))
									; Khong co don vi - nhan he so
									(setq txtcontent (rtos (* numvalue factor) 2 decimal-places))
								)
							)
							; Neu khong trich xuat duoc so, giu nguyen text
							(setq txtcontent txtcontent)
						)
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring txtobj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
						; Trich xuat so va nhan he so
						(setq numvalue (EXTRACT-NUMBER txtcontent))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq txtcontent (rtos (car numvalue) 2 decimal-places))
									; Khong co don vi - nhan he so
									(setq txtcontent (rtos (* numvalue factor) 2 decimal-places))
								)
							)
							; Neu khong trich xuat duoc so, giu nguyen text
							(setq txtcontent txtcontent)
						)
					)
					; DIMENSION (tat ca cac loai)
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						; Lay gia tri Measurement, nhan he so va lam tron
						(setq measurement (vla-get-measurement txtobj))
						(setq txtcontent (rtos (* measurement factor) 2 decimal-places))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition txtobj)))
						)))
						(if (vl-catch-all-error-p txtpos)
							(setq txtpos (list 0.0 0.0 0.0))
						)
					)
					; Loai khac - bo qua
					(T
						(setq txtcontent nil txtpos nil)
					)
				)

				; Them vao list neu co noi dung
				(if (and txtcontent txtpos)
					(setq textlist (append textlist (list (list txtcontent txtpos))))
				)
				(setq n (+ n 1))
			)
		)
	)
	textlist
)

; ===================================================================
; HAM PHU TRO CHO ET
; ===================================================================

; Ham thuc hien Table mode
(defun ET-EXECUTE-TABLE ( ss xlcells startrow startcol / textlist newrow newcol dwgpath dwgname handlelist commenttext targetcell)
	; Chuyen doi selection set thanh text list - dung ham E5 de co toa do
	(if (= *E6-number* "Y")
		(setq textlist (E5-CONVERT-SS-TO-TEXTLIST-NUMBER ss))
		(setq textlist (E5-CONVERT-SS-TO-TEXTLIST ss))
	)

	; Xuat text theo hang ngang
	(setq newcol startcol)
	(foreach txt textlist
		(SETCELLTEXT xlcells startrow newcol (car txt))
		(setq newcol (+ newcol 1))
	)

	; Ghi handle neu can
	(if (= *E6-handle* "Y")
		(progn
			(setq dwgpath (vla-get-fullname (vla-get-activedocument (vlax-get-acad-object))))
			(setq dwgname (vl-filename-base dwgpath))
			(setq handlelist (GET-HANDLES ss))
			(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
			(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
			(WRITE-COMMENT-TO-CELL targetcell commenttext)
		)
	)

	; Nhay chuot
	(setq newrow (+ startrow (atoi *E6-jump*)) newcol newcol)
	(MOVE-CURSOR xlcells newrow newcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa newcol)))
)

; Ham thuc hien Col mode
(defun ET-EXECUTE-COL ( ss xlcells startrow startcol / textlist sorted-textlist newrow newcol dwgpath dwgname handlelist commenttext targetcell)
	; Chuyen doi va sap xep
	(if (= *E6-number* "Y")
		(setq textlist (E5-CONVERT-SS-TO-TEXTLIST-NUMBER ss))
		(setq textlist (E5-CONVERT-SS-TO-TEXTLIST ss))
	)
	(setq sorted-textlist (ET-SORT-FOR-COL textlist))

	; Xuat theo cot
	(setq newrow startrow)
	(foreach txt sorted-textlist
		(SETCELLTEXT xlcells newrow startcol (car txt))
		(setq newrow (+ newrow 1))
	)

	; Ghi handle neu can
	(if (= *E6-handle* "Y")
		(progn
			(setq dwgpath (vla-get-fullname (vla-get-activedocument (vlax-get-acad-object))))
			(setq dwgname (vl-filename-base dwgpath))
			(setq handlelist (GET-HANDLES ss))
			(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
			(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
			(WRITE-COMMENT-TO-CELL targetcell commenttext)
		)
	)

	; Nhay chuot
	(setq newrow (+ newrow (atoi *E6-jump*)) newcol startcol)
	(MOVE-CURSOR xlcells newrow newcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa newcol)))
)

; Ham thuc hien Row mode
(defun ET-EXECUTE-ROW ( ss xlcells startrow startcol / textlist sorted-textlist newcol newrow dwgpath dwgname handlelist commenttext targetcell)
	(if (= *E6-number* "Y")
		(setq textlist (E5-CONVERT-SS-TO-TEXTLIST-NUMBER ss))
		(setq textlist (E5-CONVERT-SS-TO-TEXTLIST ss))
	)
	(setq sorted-textlist (ET-SORT-FOR-ROW textlist))

	; Xuat theo hang
	(setq newcol startcol)
	(foreach txt sorted-textlist
		(SETCELLTEXT xlcells startrow newcol (car txt))
		(setq newcol (+ newcol 1))
	)

	; Ghi handle neu can
	(if (= *E6-handle* "Y")
		(progn
			(setq dwgpath (vla-get-fullname (vla-get-activedocument (vlax-get-acad-object))))
			(setq dwgname (vl-filename-base dwgpath))
			(setq handlelist (GET-HANDLES ss))
			(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
			(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
			(WRITE-COMMENT-TO-CELL targetcell commenttext)
		)
	)

	; Nhay chuot
	(setq newrow (+ startrow (atoi *E6-jump*)) newcol newcol)
	(MOVE-CURSOR xlcells newrow newcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa newcol)))
)

; Ham thuc hien Cell mode
(defun ET-EXECUTE-CELL ( ss xlcells startrow startcol / textlist result-text newrow)
	(setq textlist (CONVERT-SS-TO-TEXTLIST ss))
	(if (= *E6-symbol* "+")
		(setq result-text (ET-CREATE-FORMULA textlist))
		(setq result-text (ET-JOIN-TEXT textlist))
	)
	(SETCELLTEXT xlcells startrow startcol result-text)
	(setq newrow (+ startrow 1 (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
)

; Ham thuc hien Array mode
(defun ET-EXECUTE-ARRAY ( ss xlcells startrow startcol / textlist newrow)
	(setq textlist (CONVERT-SS-TO-TEXTLIST ss))
	(E5-EXPORT-ARRAY textlist xlcells startrow startcol)
	(setq newrow (+ startrow (E5-GET-ARRAY-ROWS textlist) (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
)

; Ham thuc hien Handle mode
(defun ET-EXECUTE-HANDLE ( ss xlcells startrow startcol / handlelist commenttext dwgpath dwgname newrow targetcell)
	(setq dwgpath (vla-get-fullname ActDoc)
		  dwgname (vl-filename-base dwgpath))
	(setq handlelist (GET-HANDLES ss))
	(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
	(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
	(WRITE-COMMENT-TO-CELL targetcell commenttext)
	(setq newrow (+ startrow (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Da ghi " (itoa (length handlelist)) " handle"))
)

; Ham thuc hien Value mode
(defun ET-EXECUTE-VALUE ( ss xlcells startrow startcol / celltext handlelist commenttext dwgpath dwgname newrow targetcell)
	(setq dwgpath (vla-get-fullname ActDoc)
		  dwgname (vl-filename-base dwgpath))
	(setq celltext (ET-CREATE-CELL-TEXT ss))
	(setq handlelist (GET-HANDLES ss))
	(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))

	(SETCELLTEXT xlcells startrow startcol celltext)
	(if (= *E6-handle* "Y")
		(progn
			(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
			(WRITE-COMMENT-TO-CELL targetcell commenttext)
		)
	)
	(setq newrow (+ startrow (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
)

; Ham tao cong thuc cho ET
(defun ET-CREATE-FORMULA ( textlist / result)
	(setq result (strcat "=" (car (car textlist))))
	(foreach txt (cdr textlist)
		(setq result (strcat result "+" (car txt)))
	)
	result
)

; Ham noi text cho ET
(defun ET-JOIN-TEXT ( textlist / result)
	(setq result (car (car textlist)))
	(foreach txt (cdr textlist)
		(setq result (strcat result *E6-symbol* (car txt)))
	)
	result
)

; Ham ho tro - Sap xep cho Col mode
(defun ET-SORT-FOR-COL ( textlist / )
	; Sap xep theo Y giam dan (top to bottom) - dung Z coordinate
	(vl-sort textlist '(lambda (a b) (> (caddr (cadr a)) (caddr (cadr b)))))
)

; Ham ho tro - Sap xep cho Row mode
(defun ET-SORT-FOR-ROW ( textlist / )
	; Sap xep theo X tang dan (left to right) - dung X coordinate
	(vl-sort textlist '(lambda (a b) (< (car (cadr a)) (car (cadr b)))))
)

; Ham tao noi dung cell cho ET
(defun ET-CREATE-CELL-TEXT ( ss / textlist result)
	(setq textlist (CONVERT-SS-TO-TEXTLIST ss))
	(if (= *E6-symbol* "+")
		(setq result (ET-CREATE-FORMULA textlist))
		(setq result (ET-JOIN-TEXT textlist))
	)
	result
)

; ===================================================================
; KET THUC FILE
; ===================================================================

(princ "\n=== CAD TO EXCEL - PHIEN BAN MOI (DA DOI TEN LENH) ===")
(princ "\nCac lenh:")
(princ "\n  E1 - Xuat text/dim vao cell (co he so)")
(princ "\n  E2 - Xuat text/dim vao cell (text giu nguyen)")
(princ "\n  E3 - Xuat theo 3 che do (Col/Row/Array) - TRUOC DAY LA E5")
(princ "\n  E4 - Chi ghi handle vao comment - TRUOC DAY LA E3")
(princ "\n  E5 - Xuat table vao Excel - TRUOC DAY LA E4")
(princ "\n  E6 - Thiet lap toan cuc")
(princ "\n  E7 - Cong tac Handle (Toggle On/Off) - MOI")
(princ "\n  E8 - Cong tac Factor (Toggle 1/0.001) - MOI")
(princ "\n  E9 - Cong tac Number (Toggle Y/N) - MOI")
(princ "\n  E11 - Xuat text/dim voi he so nhan them - MOI")
(princ "\n  ET - Lenh tong hop toi uu")
(princ "\n=== LOADED ===")
(princ)
