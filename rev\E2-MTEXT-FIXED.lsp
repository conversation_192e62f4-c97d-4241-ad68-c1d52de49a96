; E2 - XUAT MTEXT VAO EXCEL (DA SUA LOI FORMAT TEXT)
; <PERSON><PERSON> ban hoan chinh - xu ly dung MTEXT format

; <PERSON>ien toan cuc
(or *E6-jump* (setq *E6-jump* "3"))

; Ham tim ky tu trong chuoi
(defun FIND-CHAR ( char string / i found pos)
	(setq i 1 found nil pos nil)
	(while (and (<= i (strlen string)) (not found))
		(if (= (substr string i 1) char)
			(progn
				(setq found T)
				(setq pos (1- i))
			)
			(setq i (1+ i))
		)
	)
	pos
)

; Ham lam sach MTEXT - da sua loi
(defun CLEAN-MTEXT ( text / result pos end-pos)
	(if (and text (= (type text) 'STR) (> (strlen text) 0))
		(progn
			(setq result text)
			
			; Loai bo format \xxx; truoc
			(while (setq pos (FIND-CHAR "\\" result))
				(setq end-pos (FIND-CHAR ";" result))
				(if (and end-pos (> end-pos pos))
					(setq result (strcat
						(substr result 1 pos)
						(substr result (+ end-pos 2))
					))
					(setq result (substr result 1 pos))
				)
			)
			
			; Loai bo dau { va } nhung giu lai noi dung
			(while (setq pos (FIND-CHAR "{" result))
				(setq result (strcat
					(substr result 1 pos)
					(substr result (+ pos 2))
				))
			)
			(while (setq pos (FIND-CHAR "}" result))
				(setq result (strcat
					(substr result 1 pos)
					(substr result (+ pos 2))
				))
			)
			
			; Trim khoang trang
			(while (and (> (strlen result) 0) (= (substr result 1 1) " "))
				(setq result (substr result 2))
			)
			(while (and (> (strlen result) 0) (= (substr result (strlen result) 1) " "))
				(setq result (substr result 1 (1- (strlen result))))
			)
			
			result
		)
		""
	)
)

; Ham ket noi Excel
(defun CONNECT-EXCEL ( / xlapp xlcells startrow startcol)
	(setq xlapp (vlax-get-object "Excel.Application"))
	(if (not xlapp)
		(progn
			(princ "\nKhong tim thay Excel. Dang khoi dong...")
			(setq xlapp (vlax-create-object "Excel.Application"))
		)
	)
	(vlax-put-property xlapp "Visible" :vlax-true)
	(setq xlcells (vlax-get-property (vlax-get-property xlapp "ActiveSheet") "Cells"))
	(setq startrow (vlax-get-property (vlax-get-property xlapp "ActiveCell") "Row"))
	(setq startcol (vlax-get-property (vlax-get-property xlapp "ActiveCell") "Column"))
	(list xlapp xlcells startrow startcol)
)

; Lenh E2 hoan chinh
(defun C:E2 ( / xlapp xlcells startrow startcol ss ent entdata raw-text cleaned-text excel-data newrow)
	(vl-load-com)
	
	; Chon MTEXT
	(princ "\nChon MTEXT: ")
	(setq ss (ssget '((0 . "MTEXT"))))
	
	(if ss
		(progn
			; Lay text tu MTEXT
			(setq ent (ssname ss 0))
			(setq entdata (entget ent))
			(setq raw-text "")
			
			; Lay text tu DXF codes 1 va 3
			(foreach item entdata
				(if (= (car item) 1)
					(setq raw-text (strcat raw-text (cdr item)))
				)
				(if (= (car item) 3)
					(setq raw-text (strcat raw-text (cdr item)))
				)
			)
			
			; Lam sach text
			(setq cleaned-text (CLEAN-MTEXT raw-text))
			
			(princ (strcat "\nRaw MTEXT: " raw-text))
			(princ (strcat "\nCleaned text: " cleaned-text))
			
			(if (> (strlen cleaned-text) 0)
				(progn
					; Ket noi Excel
					(setq excel-data (CONNECT-EXCEL))
					(setq xlapp (nth 0 excel-data)
						  xlcells (nth 1 excel-data)
						  startrow (nth 2 excel-data)
						  startcol (nth 3 excel-data))
					
					; Ghi vao Excel
					(vlax-put-property xlcells "Item" startrow startcol cleaned-text)
					
					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(setq targetcell (vlax-get-property xlcells "Item" newrow startcol))
					(vlax-invoke-method targetcell "Select")
					
					(princ (strcat "\nHoan thanh! Da ghi: '" cleaned-text "'"))
					(princ (strcat "\nChuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong co noi dung de ghi!")
			)
		)
		(princ "\nKhong chon duoc MTEXT!")
	)
	(princ)
)

; Test function
(defun C:TEST-E2 ()
	(setq test1 "{\\fArial;Hello World}")
	(setq test2 "\\fTimes;Simple text")
	(setq test3 "Normal text")
	
	(princ "\n=== TEST CLEAN-MTEXT ===")
	(princ (strcat "\nTest 1: " test1))
	(princ (strcat "\nResult 1: '" (CLEAN-MTEXT test1) "'"))
	(princ (strcat "\nTest 2: " test2))
	(princ (strcat "\nResult 2: '" (CLEAN-MTEXT test2) "'"))
	(princ (strcat "\nTest 3: " test3))
	(princ (strcat "\nResult 3: '" (CLEAN-MTEXT test3) "'"))
	(princ)
)

(princ "\nE2-MTEXT-FIXED loaded. Commands: E2, TEST-E2")
(princ "\nSu dung: E2 [chon MTEXT co format] -> tu dong ghi vao Excel")
