; ===================================================================
; Z-CAD2EXCEL-E1_V3.LSP - TICH HOP 4 FILE AUTOLISP
; Tac gia: <PERSON>it <PERSON>
; Ngay phat hanh: 20/12/2024
; Phien ban: 3.0 Integrated
; ===================================================================
;
; HUONG DAN SU DUNG:
; 1. Load file: APPLOAD -> chon file .lsp
; 2. Su dung cac lenh E0-E4 va cac lenh con
; 3. Excel phai duoc mo truoc khi chay lenh
;
; DANH SACH LENH MOI:
; === NHOM E1: XUAT CELL ===
; E1  - Xuat text/dim vao cell (co he so factor)
; E11 - Xuat text/dim voi he so nhan them (=(n1+n2+...)*a)
; E12 - Xuat text/dim vao cell (text giu nguyen + dim co factor)
; E14 - Dat nhanh he so (factor)=1
; E15 - Dat nhanh he so (factor)=0.001
;
; === NHOM E2: XUAT COL, ROW, ARRAY ===
; E2  - Xuat theo 3 che do (Col/Row/Array) voi thu tu selection
; E21 - Xuat nhanh ra cot (vi tri chuot tu dong nhay xuong o cuoi)
; E22 - Xuat nhanh ra hang (vi tri chuot tu dong nhay qua phai o cuoi)
; E23 - Xuat nhanh ra mang (vi tri chuot tu dong nhay xuong o cuoi)
; E24 - Bat nhanh Number (Y)
; E25 - Tat nhanh Number (N)
;
; === NHOM E3: GHI HANDLE ===
; E3  - Chi ghi handle vao comment
; E31 - Mo file Cad theo comment (tuong duong lenh HTC cu)
; E32 - Zoom va highlight doi tuong theo handle (tuong duong lenh ZTH cu)
; E33 - Zoom va select doi tuong theo handle (tuong duong lenh STH cu)
; E34 - Bat nhanh handle (gan che do thiet lap handle Y)
; E35 - Tat nhanh handle (gan che do thiet lap handle N)
;
; === NHOM E4: XUAT TABLE ===
; E4  - Xuat bang theo line (tuong duong E5 cu)
; E41 - Xuat bang theo line (giu format bang) - goi lenh CTE
; E42 - Xuat bang theo Table - tuong duong lenh TE cu
; E43 - Xuat bang tu excel qua cad - goi lenh ETC
; E44 - Cong tac mo Frame (Y/N)
;
; === THIET LAP ===
; E0  - Mo bang thiet lap (tuong duong E6 cu)
;
; ===================================================================

; Thiet lap chu ky
(setvar "MODEMACRO" "**Zit Đại Ka**")

; Bien toan cuc luu thiet lap
(or *E6-handle* (setq *E6-handle* "Y"))
(or *E6-frame* (setq *E6-frame* "N"))
(or *E6-symbol* (setq *E6-symbol* "+"))
(or *E6-factor* (setq *E6-factor* "1"))
(or *E6-jump* (setq *E6-jump* "3"))
(or *E6-tolerance* (setq *E6-tolerance* "50"))
(or *E6-number* (setq *E6-number* "N"))

; ===================================================================
; PHAN 1: CODE TU Z-Cad2Excel-E1-ET.lsp
; ===================================================================

; Ham tim ky tu trong chuoi (thay cho vl-string-search)
(defun FIND-CHAR ( char string / i found pos)
	(setq i 1 found nil pos nil)
	(while (and (<= i (strlen string)) (not found))
		(if (= (substr string i 1) char)
			(progn
				(setq found T)
				(setq pos (1- i))
			)
			(setq i (1+ i))
		)
	)
	pos
)

; Ham chuyen doi ky hieu dac biet trong text
(defun CONVERT-SPECIAL-SYMBOLS ( text-string / result)
	(if (and text-string (= (type text-string) 'STR) (> (strlen text-string) 0))
		(progn
			(setq result text-string)

			; Chuyen doi cac ky hieu dac biet
			(setq result (vl-string-subst "Ø" "%%c" result))  ; Diameter symbol
			(setq result (vl-string-subst "Ø" "%%C" result))  ; Diameter symbol (uppercase)
			(setq result (vl-string-subst "±" "%%p" result))  ; Plus-minus symbol
			(setq result (vl-string-subst "±" "%%P" result))  ; Plus-minus symbol (uppercase)
			(setq result (vl-string-subst "°" "%%d" result))  ; Degree symbol
			(setq result (vl-string-subst "°" "%%D" result))  ; Degree symbol (uppercase)

			result
		)
		""
	)
)

; Ham lam sach MTEXT - phien ban cai tien cho tieng Viet
(defun CLEAN-MTEXT ( mtext-string / result pos end-pos char-list i char new-result)
	(if (and mtext-string (= (type mtext-string) 'STR) (> (strlen mtext-string) 0))
		(progn
			(setq result mtext-string)

			; Buoc 1: Loai bo format \xxx; an toan hon
			(setq result (REMOVE-FORMAT-CODES result))

			; Buoc 2: Loai bo dau { va } nhung giu lai noi dung
			(setq result (REMOVE-BRACES result))

			; Buoc 3: Chuyen doi ky hieu dac biet
			(setq result (CONVERT-SPECIAL-SYMBOLS result))

			; Buoc 4: Trim khoang trang
			(setq result (TRIM-STRING result))

			result
		)
		""
	)
)

; Ham loai bo format codes an toan hon
(defun REMOVE-FORMAT-CODES ( text-string / result i char in-format format-start)
	(setq result "" i 1 in-format nil format-start 0)
	(while (<= i (strlen text-string))
		(setq char (substr text-string i 1))
		(cond
			; Bat dau format code
			((and (= char "\\") (not in-format))
				(setq in-format T format-start i)
			)
			; Ket thuc format code
			((and (= char ";") in-format)
				(setq in-format nil)
			)
			; Ky tu binh thuong (khong trong format code)
			((not in-format)
				(setq result (strcat result char))
			)
		)
		(setq i (+ i 1))
	)
	result
)

; Ham loai bo dau ngoac nhon an toan hon
(defun REMOVE-BRACES ( text-string / result i char brace-count)
	(setq result "" i 1 brace-count 0)
	(while (<= i (strlen text-string))
		(setq char (substr text-string i 1))
		(cond
			; Mo ngoac nhon
			((= char "{")
				(setq brace-count (+ brace-count 1))
			)
			; Dong ngoac nhon
			((= char "}")
				(setq brace-count (- brace-count 1))
			)
			; Ky tu binh thuong
			(T
				(setq result (strcat result char))
			)
		)
		(setq i (+ i 1))
	)
	result
)

; Ham trim string an toan
(defun TRIM-STRING ( text-string / result)
	(setq result text-string)
	; Loai bo khoang trang dau
	(while (and (> (strlen result) 0) (= (substr result 1 1) " "))
		(setq result (substr result 2))
	)
	; Loai bo khoang trang cuoi
	(while (and (> (strlen result) 0) (= (substr result (strlen result) 1) " "))
		(setq result (substr result 1 (1- (strlen result))))
	)
	result
)

; Bang chuyen doi Unicode escape sequences sang ky tu tieng Viet
(defun INIT-UNICODE-TABLE ( / unicode-table)
	(setq unicode-table (list
		; Cac ky tu thuong gap nhat
		(list "\\U+1ED4" "Ổ")  (list "\\U+1ED1" "ố")  ; O circumflex
		(list "\\U+1EE3" "ợ")  (list "\\U+01B0" "ư")  ; o horn, u horn
		(list "\\U+1EA3" "ả")  (list "\\U+1EA1" "ạ")  ; a hook, a dot
		(list "\\U+1EBB" "ẻ")  (list "\\U+1EB9" "ẹ")  ; e hook, e dot
		(list "\\U+1EC9" "ỉ")  (list "\\U+1ECB" "ị")  ; i hook, i dot
		(list "\\U+1ECD" "ọ")  (list "\\U+1ECF" "ỏ")  ; o dot, o hook
		(list "\\U+1EE5" "ụ")  (list "\\U+1EE7" "ủ")  ; u dot, u hook
		(list "\\U+1EF3" "ỳ")  (list "\\U+1EF5" "ỵ")  ; y grave, y dot
		(list "\\U+0110" "Đ")  (list "\\U+0111" "đ")  ; D stroke
		(list "\\U+01A0" "Ơ")  (list "\\U+01A1" "ơ")  ; O horn
		(list "\\U+01AF" "Ư")  (list "\\U+01B0" "ư")  ; U horn
		; Them cac ky tu khac neu can...
	))
	unicode-table
)

; Ham chuyen doi Unicode escape sequences
(defun CONVERT-UNICODE-ESCAPES (text-string / result unicode-table escape-seq vietnamese-char)
	(setq result text-string)
	(setq unicode-table (INIT-UNICODE-TABLE))

	; Duyet qua tung Unicode escape sequence trong bang
	(foreach unicode-pair unicode-table
		(setq escape-seq (nth 0 unicode-pair))
		(setq vietnamese-char (nth 1 unicode-pair))

		; Thay the tat ca cac escape sequence trong text
		(while (vl-string-search escape-seq result)
			(setq result (vl-string-subst vietnamese-char escape-seq result))
		)
	)

	result
)

; Ham xu ly text table co Unicode escape sequences - phien ban nang cao
(defun CLEAN-TABLE-TEXT ( text-string / result has-unicode)
	(if (and text-string (= (type text-string) 'STR) (> (strlen text-string) 0))
		(progn
			(setq result text-string)

			; Kiem tra xem co Unicode escape sequences khong
			(setq has-unicode (vl-string-search "\\U+" result))

			; Buoc 1: Xu ly Unicode escape sequences neu co
			(if has-unicode
				(setq result (CONVERT-UNICODE-ESCAPES result))
			)

			; Buoc 2: Xu ly format codes binh thuong neu co
			(if (or (vl-string-search "\\" result)
					(vl-string-search "{" result)
					(vl-string-search "}" result))
				(progn
					; Loai bo format codes
					(setq result (SIMPLE-REMOVE-FORMAT-CODES result))

					; Loai bo dau ngoac nhon
					(setq result (vl-string-subst "" "{" result))
					(setq result (vl-string-subst "" "}" result))
				)
			)

			; Buoc 3: Chuyen doi ky hieu dac biet
			(setq result (CONVERT-SPECIAL-SYMBOLS result))

			; Buoc 4: Trim va lam sach cuoi cung
			(setq result (TRIM-STRING result))

			result
		)
		""
	)
)

; Ham loai bo format codes DUNG - da hieu dung pattern
(defun SIMPLE-REMOVE-FORMAT-CODES (text-string / result start-pos end-pos after-semicolon)
	(setq result text-string)

	; Loai bo pattern: \[letter][number]; (co the co spaces sau semicolon)
	(while (setq start-pos (vl-string-search "\\" result))
		; Tim semicolon sau backslash
		(setq end-pos (vl-string-search ";" result start-pos))

		(if end-pos
			(progn
				; Tim vi tri sau semicolon va spaces
				(setq after-semicolon (+ end-pos 1))

				; Bo qua cac spaces sau semicolon
				(while (and (< after-semicolon (strlen result))
							(= (substr result (+ after-semicolon 1) 1) " "))
					(setq after-semicolon (+ after-semicolon 1))
				)

				; Loai bo tu backslash den het spaces
				(setq result (strcat
					(substr result 1 start-pos)
					(substr result (+ after-semicolon 1))
				))
			)
			; Neu khong co semicolon, loai bo het tu backslash
			(setq result (substr result 1 start-pos))
		)
	)

	result
)

; Ham ket noi Excel
(defun CONNECT-EXCEL ( / xlapp xlcells startrow startcol)
	(if (not (setq xlapp (vlax-get-object "Excel.Application")))
		(progn
			(princ "\nKhong tim thay Excel. Dang khoi dong Excel...")
			(setq xlapp (vlax-create-object "Excel.Application"))
		)
	)
	(vlax-put-property xlapp "Visible" :vlax-true)
	(setq xlcells (vlax-get-property (vlax-get-property xlapp "ActiveSheet") "Cells"))
	(setq startrow (vlax-get-property (vlax-get-property xlapp "ActiveCell") "Row"))
	(setq startcol (vlax-get-property (vlax-get-property xlapp "ActiveCell") "Column"))
	(list xlapp xlcells startrow startcol)
)

; Ham ghi cell text - cai tien
(defun SETCELLTEXT ( xlcells row col text / result cell worksheet range celladdress)
	; Thu cach 1: Su dung Item truc tiep
	(setq result (vl-catch-all-apply '(lambda ()
		(setq cell (vlax-get-property xlcells "Item" row col))
		(vlax-put-property cell "Value" text)
	)))

	; Neu cach 1 that bai, thu cach 2: Su dung Range
	(if (vl-catch-all-error-p result)
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER col) (itoa row)))
				(setq worksheet (vlax-get-property xlcells "Parent"))
				(setq range (vlax-get-property worksheet "Range" celladdress))
				(vlax-put-property range "Value" text)
			)))

			; Neu cach 2 cung that bai, thu cach 3: Su dung Cells truc tiep
			(if (vl-catch-all-error-p result)
				(progn
					(setq result (vl-catch-all-apply '(lambda ()
						(vlax-put-property xlcells "Item" row col text)
					)))
					(if (vl-catch-all-error-p result)
						(princ (strcat "\nLoi ghi cell: " (vl-catch-all-error-message result)))
					)
				)
			)
		)
	)
)

; Ham lay handle cua doi tuong
(defun GET-HANDLES ( ss / i handlelist ent)
	(setq handlelist '() i 0)
	(repeat (sslength ss)
		(setq ent (ssname ss i))
		(setq handlelist (cons (vla-get-handle (vlax-ename->vla-object ent)) handlelist))
		(setq i (+ i 1))
	)
	(reverse handlelist)
)

; Ham tao comment text
(defun CREATE-COMMENT-TEXT ( handlelist dwgpath dwgname / commenttext)
	(setq commenttext "")
	(foreach handle handlelist
		(setq commenttext (strcat commenttext handle "; "))
	)
	(setq commenttext (strcat commenttext "\nFileCad: " dwgpath))
	commenttext
)

; Ham ghi comment vao cell - sua loi VLA-OBJECT
(defun WRITE-COMMENT-TO-CELL ( cell commenttext / result comments comment xlapp worksheet celladdress)
	; Kiem tra cell co hop le khong
	(if (and cell (not (vl-catch-all-error-p cell)))
		(progn
			; Thu cach 1: Su dung cell truc tiep
			(setq result (vl-catch-all-apply '(lambda ()
				; Xoa comment cu neu co
				(if (vlax-property-available-p cell "Comment")
					(progn
						(setq comments (vlax-get-property cell "Comment"))
						(if comments
							(vlax-invoke-method comments "Delete")
						)
					)
				)
				; Tao comment moi
				(setq comment (vlax-invoke-method cell "AddComment" commenttext))
				(if comment
					(vlax-put-property comment "Visible" :vlax-false)
				)
			)))

			; Neu that bai, thu cach 2: Su dung Excel Application
			(if (vl-catch-all-error-p result)
				(progn
					(setq result (vl-catch-all-apply '(lambda ()
						(setq xlapp (vlax-get-acad-object))
						(setq xlapp (vlax-get-object "Excel.Application"))
						(setq worksheet (vlax-get-property xlapp "ActiveSheet"))
						(setq celladdress (vlax-get-property cell "Address"))
						(setq cell (vlax-get-property worksheet "Range" celladdress))
						(setq comment (vlax-invoke-method cell "AddComment" commenttext))
						(vlax-put-property comment "Visible" :vlax-false)
					)))

					; Neu van that bai, chi tao comment don gian
					(if (vl-catch-all-error-p result)
						(progn
							(setq result (vl-catch-all-apply '(lambda ()
								(vlax-invoke-method cell "AddComment" commenttext)
							)))
						)
					)
				)
			)
		)
		(princ "\nCell khong hop le!")
	)
)

; Ham chuyen so cot thanh chu cai
(defun COLUMN-NUMBER-TO-LETTER ( col / result)
	(setq result "")
	(while (> col 0)
		(setq col (- col 1))
		(setq result (strcat (chr (+ 65 (rem col 26))) result))
		(setq col (/ col 26))
	)
	result
)

; Ham trich xuat so tu text - co xu ly m2/m
(defun EXTRACT-NUMBER ( text-string / cleaned-text i char in-number current-number result has-unit)
	(if (and text-string (= (type text-string) 'STR) (> (strlen text-string) 0))
		(progn
			; Su dung ham CLEAN-MTEXT de lam sach text
			(setq cleaned-text (CLEAN-MTEXT text-string))

			; Kiem tra co don vi m2 hoac m khong
			(setq has-unit (or (vl-string-search "m2" cleaned-text) (vl-string-search "m" cleaned-text)))

			; Tim so trong chuoi
			(setq result "" i 1 in-number nil current-number "")
			(while (<= i (strlen cleaned-text))
				(setq char (substr cleaned-text i 1))
				(cond
					; Ky tu so
					((and (>= (ascii char) 48) (<= (ascii char) 57))
						(setq in-number T)
						(setq current-number (strcat current-number char))
					)
					; Dau thap phan
					((and in-number (member char '("." ",")))
						(setq current-number (strcat current-number char))
					)
					; Ky tu khac - ket thuc so
					(T
						(if (and in-number (> (strlen current-number) 0))
							(progn
								(setq result current-number)
								(setq i (strlen cleaned-text)) ; Thoat vong lap
							)
							(progn
								(setq in-number nil)
								(setq current-number "")
							)
						)
					)
				)
				(setq i (+ i 1))
			)

			; Neu ket thuc chuoi ma van dang trong so
			(if (and in-number (> (strlen current-number) 0))
				(setq result current-number)
			)

			; Chuyen doi thanh so
			(if (and result (> (strlen result) 0))
				(progn
					(setq result (vl-string-subst "." "," result))
					; Neu co don vi m2 hoac m, tra ve list (so, :unit) de nhan biet
					(if has-unit
						(list (atof result) :unit)
						(atof result)
					)
				)
				nil
			)
		)
		nil
	)
)

; Ham chon doi tuong thong minh - tu dong nhan dien cach chon
(defun SMART-SELECT-OBJECTS ( prompt filter / obj-list ent ss)
	(princ prompt)
	(princ "\n[Click tung doi tuong theo thu tu, hoac Window/Crossing de chon nhieu]")
	(setq obj-list '())

	; Thu chon tung doi tuong truoc
	(setq ent (entsel "\nChon doi tuong dau tien (hoac Enter de chon nhieu): "))

	(if ent
		(progn
			; Da chon 1 doi tuong, tiep tuc chon tung cai
			(setq ent (car ent))
			(if (or (not filter) (wcmatch (cdr (assoc 0 (entget ent))) filter))
				(progn
					(setq obj-list (list ent))
					(princ "\nDa chon 1 doi tuong. Tiep tuc chon tung cai...")

					; Chon tiep cac doi tuong khac
					(while (setq ent (entsel "\nChon doi tuong tiep theo (Enter de ket thuc): "))
						(if (= (type ent) 'LIST)
							(progn
								(setq ent (car ent))
								(if (or (not filter) (wcmatch (cdr (assoc 0 (entget ent))) filter))
									(progn
										(setq obj-list (append obj-list (list ent)))
										(princ (strcat "\nDa chon " (itoa (length obj-list)) " doi tuong"))
									)
									(princ "\nDoi tuong khong hop le!")
								)
							)
						)
					)

					; Tao selection set va luu thu tu
					(setq ss (ssadd))
					(foreach obj obj-list
						(ssadd obj ss)
					)
					(setq *SELECTION-ORDER* obj-list)
					ss
				)
				(progn
					(princ "\nDoi tuong khong hop le!")
					nil
				)
			)
		)
		; Khong chon tung cai, chuyen sang chon nhieu
		(progn
			(princ "\nChon nhieu doi tuong:")
			(setq *SELECTION-ORDER* nil)
			(ssget (if filter (list (cons 0 filter)) nil))
		)
	)
)

; Ham chuyen selection set thanh danh sach text (giu thu tu selection)
(defun CONVERT-SS-TO-TEXTLIST ( ss / i ent obj objname txtcontent txtpos measurement result raw-text)
	(setq result '())

	; Neu co thu tu selection da luu, su dung no
	(if (and *SELECTION-ORDER* (= (length *SELECTION-ORDER*) (sslength ss)))
		(progn
			; Su dung thu tu selection da luu
			(foreach ent *SELECTION-ORDER*
				(setq obj (vlax-ename->vla-object ent)
					  objname (vla-get-objectname obj)
					  txtcontent nil
					  txtpos nil)

				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring obj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring obj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
					; DIMENSION
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						(setq measurement (vla-get-measurement obj))
						(setq txtcontent (rtos measurement 2 2))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
				)

				(if txtcontent
					(setq result (append result (list (list txtcontent txtpos objname))))
				)
			)
		)
		; Neu khong co thu tu selection, su dung thu tu mac dinh
		(progn
			(setq i 0)
			(repeat (sslength ss)
				(setq ent (ssname ss i)
					  obj (vlax-ename->vla-object ent)
					  objname (vla-get-objectname obj)
					  txtcontent nil
					  txtpos nil)

				(cond
					; TEXT
					((= objname "AcDbText")
						(setq raw-text (vla-get-textstring obj))
						(setq txtcontent (CONVERT-SPECIAL-SYMBOLS raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
					; MTEXT
					((= objname "AcDbMText")
						(setq raw-text (vla-get-textstring obj))
						(setq txtcontent (CLEAN-MTEXT raw-text))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
					; DIMENSION
					((or (= objname "AcDbAlignedDimension")
						 (= objname "AcDbRotatedDimension")
						 (= objname "AcDbRadialDimension")
						 (= objname "AcDbDiametricDimension")
						 (= objname "AcDbAngularDimension")
						 (wcmatch objname "*Dimension*"))
						(setq measurement (vla-get-measurement obj))
						(setq txtcontent (rtos measurement 2 2))
						(setq txtpos (vl-catch-all-apply '(lambda ()
							(vlax-safearray->list (vlax-variant-value (vla-get-textposition obj)))
						)))
						(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
					)
				)

				(if txtcontent
					(setq result (cons (list txtcontent txtpos objname) result))
				)
				(setq i (+ i 1))
			)
			(setq result (reverse result))
		)
	)
	result
)

; Ham nhay chuot Excel - cai tien
(defun MOVE-CURSOR ( xlcells row col / targetcell result xlapp worksheet celladdress)
	; Thu cach 1: Su dung xlcells Item
	(setq result (vl-catch-all-apply '(lambda ()
		(setq targetcell (vlax-get-property xlcells "Item" row col))
		(vlax-invoke-method targetcell "Select")
	)))

	; Neu that bai, thu cach 2: Su dung Excel Application
	(if (vl-catch-all-error-p result)
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				(setq xlapp (vlax-get-object "Excel.Application"))
				(setq worksheet (vlax-get-property xlapp "ActiveSheet"))
				(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER col) (itoa row)))
				(setq targetcell (vlax-get-property worksheet "Range" celladdress))
				(vlax-invoke-method targetcell "Select")
			)))
		)
	)
)

; ===================================================================
; LENH CHINH TU Z-Cad2Excel-E1-ET.lsp
; ===================================================================

; E1 - XUAT TEXT/DIM VAO CELL (THAY CHO E8 CU)
(defun C:E1 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	numlist
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))
			(setq numlist '())
			(setq factor (atof *E6-factor*))

			; Thu thap gia tri so
			(foreach txt textlist
				(setq objname (caddr txt))
				(cond
					; TEXT va MTEXT - trich xuat so
					((or (= objname "AcDbText") (= objname "AcDbMText"))
						(setq numvalue (EXTRACT-NUMBER (car txt)))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq numlist (cons (car numvalue) numlist))
									; Khong co don vi - nhan he so
									(setq numlist (cons (* numvalue factor) numlist))
								)
							)
						)
					)
					; DIMENSION - lay measurement
					((wcmatch objname "*Dimension*")
						(setq numvalue (atof (car txt)))
						(if numvalue
							(setq numlist (cons (* numvalue factor) numlist))
						)
					)
				)
			)

			(setq numlist (reverse numlist))

			; Xac dinh so chu so thap phan
			(setq decimal-places (if (= (atof *E6-factor*) 1.0) 0 3))

			; Tao noi dung cell
			(cond
				; 1 gia tri
				((= (length numlist) 1)
					(setq result-text (rtos (car numlist) 2 decimal-places))
				)
				; 2 gia tri
				((= (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel khi symbol la "+"
						(setq result-text (strcat "=" (rtos (car numlist) 2 decimal-places) "+" (rtos (cadr numlist) 2 decimal-places)))
						; Noi binh thuong voi symbol khac
						(setq result-text (strcat (rtos (car numlist) 2 decimal-places) *E6-symbol* (rtos (cadr numlist) 2 decimal-places)))
					)
				)
				; Nhieu hon 2 gia tri
				((> (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel khi symbol la "+"
						(progn
							(setq result-text (strcat "=" (rtos (car numlist) 2 decimal-places)))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text "+" (rtos num 2 decimal-places)))
							)
						)
						; Noi binh thuong voi symbol khac
						(progn
							(setq result-text (rtos (car numlist) 2 decimal-places))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text *E6-symbol* (rtos num 2 decimal-places)))
							)
						)
					)
				)
				; Khong co gia tri
				(T (setq result-text ""))
			)

			; Ghi vao Excel
			(if (and result-text (> (strlen result-text) 0))
				(progn
					(SETCELLTEXT xlcells startrow startcol result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							; Thu nhieu cach lay cell
							(setq result (vl-catch-all-apply '(lambda ()
								(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
							(if (vl-catch-all-error-p result)
								(progn
									; Thu cach khac
									(setq result (vl-catch-all-apply '(lambda ()
										(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
										(setq worksheet (vlax-get-property xlcells "Parent"))
										(setq targetcell (vlax-get-property worksheet "Range" celladdress))
										(WRITE-COMMENT-TO-CELL targetcell commenttext)
									)))
								)
							)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong co gia tri de ghi")
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; PHAN 2: CODE TU ZoomToHandle.lsp
; ===================================================================

; Ham chinh - Zoom den handle tu Excel (ZTH cu)
(defun C:ZTH (/ excel-app excel-wb excel-ws active-cell comment-obj comment-text cell-value
              handle-text handle-list clean-handle obj found-count zoom-objects
              min-pt max-pt i file-pos)

  ;; Ket noi den Excel
  (setq excel-app (vlax-get-or-create-object "Excel.Application"))

  (if excel-app
    (progn
      ;; Lay workbook va worksheet hien tai
      (setq excel-wb nil)
      (setq excel-ws nil)
      (setq active-cell nil)

      (vl-catch-all-apply
        '(lambda ()
           (setq excel-wb (vlax-get-property excel-app "ActiveWorkbook"))
           (setq excel-ws (vlax-get-property excel-app "ActiveSheet"))
           (setq active-cell (vlax-get-property excel-app "ActiveCell"))
         )
      )

      (if (not active-cell)
        (progn
          (vlax-release-object excel-app)
        )
        (progn
          ;; Lay text tu comment neu co
          (setq comment-text "")
          (setq comment-obj nil)

          ;; Thu lay comment object
          (setq comment-result
            (vl-catch-all-apply
              '(lambda ()
                 (setq comment-obj (vlax-get-property active-cell "Comment"))
               )
            )
          )

          ;; Neu co comment, lay text
          (if (vl-catch-all-error-p comment-result)
            (progn
              (princ "\nKhong co comment hoac loi khi lay comment.")
              (setq comment-text "")
            )
            (progn
              (if comment-obj
                (progn
                  (princ "\nDa tim thay comment object.")
                  ;; Thu lay comment text bang nhieu cach
                  (setq comment-text nil)

                  ;; Cach 1: Dung vlax-get-property
                  (setq text-result1
                    (vl-catch-all-apply
                      '(lambda ()
                         (setq comment-text (vlax-get-property comment-obj "Text"))
                       )
                    )
                  )

                  ;; Cach 2: Neu cach 1 that bai, thu dung vlax-invoke
                  (if (or (vl-catch-all-error-p text-result1) (not comment-text))
                    (setq text-result2
                      (vl-catch-all-apply
                        '(lambda ()
                           (setq comment-text (vlax-invoke-method comment-obj "Text"))
                         )
                      )
                    )
                  )

                  ;; Cach 3: Thu truy cap truc tiep
                  (if (or (not comment-text) (= comment-text ""))
                    (setq text-result3
                      (vl-catch-all-apply
                        '(lambda ()
                           (setq comment-text (vlax-get comment-obj "Text"))
                         )
                      )
                    )
                  )

                  ;; Kiem tra ket qua
                  (if (and comment-text (/= comment-text ""))
                    (progn
                      ;; Xu ly variant value
                      (if (= (type comment-text) 'VARIANT)
                        (setq comment-text (vlax-variant-value comment-text))
                      )
                      (if (not (= (type comment-text) 'STR))
                        (setq comment-text (vl-princ-to-string comment-text))
                      )
                      (princ (strcat "\nComment text: " comment-text))
                    )
                    (progn
                      (princ "\nKhong lay duoc comment text bang bat ky cach nao.")
                      (setq comment-text "")
                    )
                  )
                )
                (progn
                  (princ "\nComment object la nil.")
                  (setq comment-text "")
                )
              )
            )
          )

          ;; Xu ly comment text - loai bo phan FileCad neu co
          (if (and comment-text (/= comment-text ""))
            (progn
              ;; Tim vi tri cua FileCad hoac \n de cat chuoi
              (setq file-pos (vl-string-search "FILECAD:" (strcase comment-text)))
              (if (not file-pos)
                (setq file-pos (vl-string-search "\n" comment-text))
              )

              (if file-pos
                (setq handle-text (vl-string-trim " \t\n\r" (substr comment-text 1 file-pos)))
                (setq handle-text (vl-string-trim " \t\n\r" comment-text))
              )

              ;; Neu van chua co handle, thu tim trong toan bo comment
              (if (or (not handle-text) (= handle-text ""))
                (setq handle-text (ZTH-extract-handles-from-text comment-text))
              )
            )
            (setq handle-text "")
          )

          ;; Neu comment khong co handle, lay tu cell value
          (if (or (not handle-text) (= handle-text ""))
            (progn
              (setq cell-value nil)
              (setq value-result
                (vl-catch-all-apply
                  '(lambda ()
                     (setq cell-value (vlax-get-property active-cell "Value"))
                   )
                )
              )

              (if (and (not (vl-catch-all-error-p value-result)) cell-value)
                (progn
                  ;; Xu ly variant value
                  (if (= (type cell-value) 'VARIANT)
                    (setq cell-value (vlax-variant-value cell-value))
                  )
                  (setq cell-value (vl-princ-to-string cell-value))
                  (princ (strcat "\nCell value: " cell-value))
                  (setq file-pos (vl-string-search "FILECAD:" (strcase cell-value)))
                  (if file-pos
                    (setq handle-text (vl-string-trim " \t\n\r" (substr cell-value 1 file-pos)))
                    (setq handle-text (vl-string-trim " \t\n\r" cell-value))
                  )
                )
                (setq handle-text "")
              )
            )
          )

          ;; Kiem tra co handle hay khong
          (if (or (not handle-text) (= handle-text ""))
            (progn
              (princ "\nKhong tim thay ma handle trong o hoac comment.")
              (vlax-release-object excel-app)
            )
            (progn
              (princ (strcat "\nHandle text: " handle-text))

              ;; Tach chuoi handle theo dau ";"
              (setq handle-list (ZTH-split-string handle-text ";"))
              (setq found-count 0)
              (setq zoom-objects '())

              ;; Kiem tra tung handle
              (foreach handle handle-list
                (setq clean-handle (ZTH-clean-handle handle))
                (if (and clean-handle (/= clean-handle ""))
                  (progn
                    (setq obj (ZTH-get-object-by-handle clean-handle))
                    (if obj
                      (progn
                        (setq found-count (1+ found-count))
                        (setq zoom-objects (cons obj zoom-objects))
                        (princ (strcat "\nTim thay doi tuong: " clean-handle))
                      )
                      (princ (strcat "\nKhong tim thay doi tuong: " clean-handle))
                    )
                  )
                )
              )

              ;; Neu tim thay doi tuong
              (if (> found-count 0)
                (progn
                  ;; Zoom den tat ca doi tuong truoc
                  (if (> (length zoom-objects) 1)
                    (progn
                      ;; Tinh bounding box chung cho tat ca doi tuong
                      (setq all-min-pt nil)
                      (setq all-max-pt nil)

                      (foreach obj zoom-objects
                        (vl-catch-all-apply
                          '(lambda ()
                             (vla-getboundingbox obj 'temp-min 'temp-max)
                             (if (and temp-min temp-max)
                               (progn
                                 (setq temp-min (vlax-safearray->list temp-min))
                                 (setq temp-max (vlax-safearray->list temp-max))

                                 (if (not all-min-pt)
                                   (setq all-min-pt temp-min)
                                   (setq all-min-pt (list (min (car all-min-pt) (car temp-min))
                                                          (min (cadr all-min-pt) (cadr temp-min))
                                                          (min (caddr all-min-pt) (caddr temp-min))))
                                 )

                                 (if (not all-max-pt)
                                   (setq all-max-pt temp-max)
                                   (setq all-max-pt (list (max (car all-max-pt) (car temp-max))
                                                          (max (cadr all-max-pt) (cadr temp-max))
                                                          (max (caddr all-max-pt) (caddr temp-max))))
                                 )
                               )
                             )
                           )
                        )
                      )

                      ;; Zoom den vung chung
                      (if (and all-min-pt all-max-pt)
                        (progn
                          (command "_.zoom" "_window" all-min-pt all-max-pt)
                          (command "_.zoom" "0.8x")
                        )
                      )
                    )
                    ;; Neu chi co 1 doi tuong
                    (progn
                      (setq obj (car zoom-objects))
                      (vl-catch-all-apply
                        '(lambda ()
                           (vla-getboundingbox obj 'min-pt 'max-pt)
                           (if (and min-pt max-pt)
                             (progn
                               (setq min-pt (vlax-safearray->list min-pt))
                               (setq max-pt (vlax-safearray->list max-pt))
                               (command "_.zoom" "_window" min-pt max-pt)
                               (command "_.zoom" "0.8x")
                             )
                           )
                         )
                      )
                    )
                  )

                  ;; Highlight cac doi tuong sau khi zoom
                  (foreach obj zoom-objects
                    (setq ent (vlax-vla-object->ename obj))
                    (if ent
                      (progn
                        (redraw ent 3) ; Highlight
                        (princ (strcat "\nDa highlight doi tuong: " (vla-get-handle obj)))
                      )
                    )
                  )

                  (princ (strcat "\nHoan thanh! Da zoom va highlight " (itoa found-count) " doi tuong."))
                )
                (princ "\nKhong tim thay doi tuong nao voi cac handle da cung cap.")
              )

              (vlax-release-object excel-app)
            )
          )
        )
      )
    )
    (princ "\nLoi: Khong the ket noi den Excel.")
  )

  (princ)
)

; Ham tach chuoi theo delimiter
(defun ZTH-split-string (str delimiter / pos result temp)
  (setq result '())
  (setq temp str)

  (while (setq pos (vl-string-search delimiter temp))
    (if (> pos 0)
      (setq result (cons (substr temp 1 pos) result))
    )
    (setq temp (substr temp (+ pos (strlen delimiter) 1)))
  )

  ; Them phan cuoi cung
  (if (and temp (/= temp ""))
    (setq result (cons temp result))
  )

  (reverse result)
)

; Ham lam sach handle (loai bo khoang trang va dau ')
(defun ZTH-clean-handle (handle-str / clean-str)
  (setq clean-str (vl-string-trim " \t\n\r" handle-str))

  ; Loai bo dau ' o dau neu co
  (if (and (> (strlen clean-str) 0) (= (substr clean-str 1 1) "'"))
    (setq clean-str (substr clean-str 2))
  )

  ; Loai bo cac ky tu khong phai hex (chi giu lai 0-9, A-F)
  (setq clean-str (ZTH-extract-hex-only clean-str))

  (vl-string-trim " \t\n\r" clean-str)
)

; Ham chi lay cac ky tu hex hop le tu chuoi
(defun ZTH-extract-hex-only (str / result i char)
  (setq result "")
  (setq i 1)

  (while (<= i (strlen str))
    (setq char (substr str i 1))
    (if (or (and (>= char "0") (<= char "9"))
            (and (>= (strcase char) "A") (<= (strcase char) "F")))
      (setq result (strcat result (strcase char)))
    )
    (setq i (1+ i))
  )

  result
)

; Ham tim tat ca handle trong text (tim cac chuoi hex 6-8 ky tu)
(defun ZTH-extract-handles-from-text (text / handles i j temp-str char result)
  (setq handles '())
  (setq i 1)
  (setq text (strcase text))

  (while (<= i (strlen text))
    (setq char (substr text i 1))

    ; Neu gap ky tu hex, bat dau thu thap
    (if (or (and (>= char "0") (<= char "9"))
            (and (>= char "A") (<= char "F")))
      (progn
        (setq temp-str "")
        (setq j i)

        ; Thu thap cac ky tu hex lien tiep
        (while (and (<= j (strlen text))
                    (progn
                      (setq char (substr text j 1))
                      (or (and (>= char "0") (<= char "9"))
                          (and (>= char "A") (<= char "F")))
                    ))
          (setq temp-str (strcat temp-str char))
          (setq j (1+ j))
        )

        ; Neu chuoi hex co do dai 6-8 ky tu, coi nhu la handle
        (if (and (>= (strlen temp-str) 6) (<= (strlen temp-str) 8))
          (setq handles (cons temp-str handles))
        )

        (setq i j)
      )
      (setq i (1+ i))
    )
  )

  ; Tra ve chuoi handle cach nhau boi dau ;
  (if handles
    (apply 'strcat (reverse (cons (car handles)
                                  (mapcar '(lambda (x) (strcat ";" x)) (cdr handles)))))
    ""
  )
)

; Ham lay doi tuong theo handle
(defun ZTH-get-object-by-handle (handle-str / obj)
  (setq obj nil)

  (if (and handle-str (/= handle-str ""))
    (progn
      (vl-catch-all-apply
        '(lambda ()
           (setq obj (vlax-ename->vla-object (handent handle-str)))
         )
      )
    )
  )

  obj
)

; Ham chinh - Select To Handle tu Excel (STH cu)
(defun C:STH (/ excel-app excel-wb excel-ws active-cell comment-obj comment-text cell-value
              handle-text handle-list clean-handle obj found-count zoom-objects select-objects
              min-pt max-pt i file-pos all-min-pt all-max-pt temp-min temp-max ent ss)

  (princ "\nBat dau ket noi Excel...")

  ; Ket noi den Excel
  (setq excel-app (vlax-get-or-create-object "Excel.Application"))

  (if excel-app
    (progn
      (princ "\nDa ket noi Excel thanh cong.")

      ; Lay workbook va worksheet hien tai
      (setq excel-wb nil)
      (setq excel-ws nil)
      (setq active-cell nil)

      (vl-catch-all-apply
        '(lambda ()
           (setq excel-wb (vlax-get-property excel-app "ActiveWorkbook"))
           (setq excel-ws (vlax-get-property excel-app "ActiveSheet"))
           (setq active-cell (vlax-get-property excel-app "ActiveCell"))
         )
      )

      (if (not active-cell)
        (progn
          (princ "\nLoi: Khong the lay active cell tu Excel.")
          (vlax-release-object excel-app)
        )
        (progn
          (princ "\nDa lay active cell thanh cong.")

          ; Lay text tu comment neu co
          (setq comment-text "")
          (setq comment-obj nil)

          ; Thu lay comment object
          (setq comment-result
            (vl-catch-all-apply
              '(lambda ()
                 (setq comment-obj (vlax-get-property active-cell "Comment"))
               )
            )
          )

          ; Neu co comment, lay text
          (if (vl-catch-all-error-p comment-result)
            (progn
              (princ "\nKhong co comment hoac loi khi lay comment.")
              (setq comment-text "")
            )
            (progn
              (if comment-obj
                (progn
                  (princ "\nDa tim thay comment object.")
                  ; Thu lay comment text bang nhieu cach
                  (setq comment-text nil)

                  ; Cach 1: Dung vlax-get-property
                  (setq text-result1
                    (vl-catch-all-apply
                      '(lambda ()
                         (setq comment-text (vlax-get-property comment-obj "Text"))
                       )
                    )
                  )

                  ; Cach 2: Neu cach 1 that bai, thu dung vlax-invoke
                  (if (or (vl-catch-all-error-p text-result1) (not comment-text))
                    (setq text-result2
                      (vl-catch-all-apply
                        '(lambda ()
                           (setq comment-text (vlax-invoke-method comment-obj "Text"))
                         )
                      )
                    )
                  )

                  ; Cach 3: Thu truy cap truc tiep
                  (if (or (not comment-text) (= comment-text ""))
                    (setq text-result3
                      (vl-catch-all-apply
                        '(lambda ()
                           (setq comment-text (vlax-get comment-obj "Text"))
                         )
                      )
                    )
                  )

                  ; Kiem tra ket qua
                  (if (and comment-text (/= comment-text ""))
                    (progn
                      ; Xu ly variant value
                      (if (= (type comment-text) 'VARIANT)
                        (setq comment-text (vlax-variant-value comment-text))
                      )
                      (if (not (= (type comment-text) 'STR))
                        (setq comment-text (vl-princ-to-string comment-text))
                      )
                      (princ (strcat "\nComment text: " comment-text))
                    )
                    (progn
                      (princ "\nKhong lay duoc comment text bang bat ky cach nao.")
                      (setq comment-text "")
                    )
                  )
                )
                (progn
                  (princ "\nComment object la nil.")
                  (setq comment-text "")
                )
              )
            )
          )

          ; Xu ly comment text - loai bo phan FileCad neu co
          (if (and comment-text (/= comment-text ""))
            (progn
              ; Tim vi tri cua FileCad hoac \n de cat chuoi
              (setq file-pos (vl-string-search "FILECAD:" (strcase comment-text)))
              (if (not file-pos)
                (setq file-pos (vl-string-search "\n" comment-text))
              )

              (if file-pos
                (setq handle-text (vl-string-trim " \t\n\r" (substr comment-text 1 file-pos)))
                (setq handle-text (vl-string-trim " \t\n\r" comment-text))
              )

              ; Neu van chua co handle, thu tim trong toan bo comment
              (if (or (not handle-text) (= handle-text ""))
                (setq handle-text (ZTH-extract-handles-from-text comment-text))
              )
            )
            (setq handle-text "")
          )

          ; Neu comment khong co handle, lay tu cell value
          (if (or (not handle-text) (= handle-text ""))
            (progn
              (setq cell-value nil)
              (setq value-result
                (vl-catch-all-apply
                  '(lambda ()
                     (setq cell-value (vlax-get-property active-cell "Value"))
                   )
                )
              )

              (if (and (not (vl-catch-all-error-p value-result)) cell-value)
                (progn
                  ; Xu ly variant value
                  (if (= (type cell-value) 'VARIANT)
                    (setq cell-value (vlax-variant-value cell-value))
                  )
                  (setq cell-value (vl-princ-to-string cell-value))
                  (princ (strcat "\nCell value: " cell-value))
                  (setq file-pos (vl-string-search "FILECAD:" (strcase cell-value)))
                  (if file-pos
                    (setq handle-text (vl-string-trim " \t\n\r" (substr cell-value 1 file-pos)))
                    (setq handle-text (vl-string-trim " \t\n\r" cell-value))
                  )
                )
                (setq handle-text "")
              )
            )
          )

          ; Kiem tra co handle hay khong
          (if (or (not handle-text) (= handle-text ""))
            (progn
              (princ "\nKhong tim thay ma handle trong o hoac comment.")
              (vlax-release-object excel-app)
            )
            (progn
              (princ (strcat "\nHandle text: " handle-text))

              ; Tach chuoi handle theo dau ";"
              (setq handle-list (ZTH-split-string handle-text ";"))
              (setq found-count 0)
              (setq zoom-objects '())
              (setq select-objects '())

              ; Kiem tra tung handle
              (foreach handle handle-list
                (setq clean-handle (ZTH-clean-handle handle))
                (if (and clean-handle (/= clean-handle ""))
                  (progn
                    (setq obj (ZTH-get-object-by-handle clean-handle))
                    (if obj
                      (progn
                        (setq found-count (1+ found-count))
                        (setq zoom-objects (cons obj zoom-objects))
                        (setq ent (vlax-vla-object->ename obj))
                        (if ent
                          (setq select-objects (cons ent select-objects))
                        )
                        (princ (strcat "\nTim thay doi tuong: " clean-handle))
                      )
                      (princ (strcat "\nKhong tim thay doi tuong: " clean-handle))
                    )
                  )
                )
              )

              ; Neu tim thay doi tuong
              (if (> found-count 0)
                (progn
                  ; Zoom den tat ca doi tuong truoc
                  (if (> (length zoom-objects) 1)
                    (progn
                      ; Tinh bounding box chung cho tat ca doi tuong
                      (setq all-min-pt nil)
                      (setq all-max-pt nil)

                      (foreach obj zoom-objects
                        (vl-catch-all-apply
                          '(lambda ()
                             (vla-getboundingbox obj 'temp-min 'temp-max)
                             (if (and temp-min temp-max)
                               (progn
                                 (setq temp-min (vlax-safearray->list temp-min))
                                 (setq temp-max (vlax-safearray->list temp-max))

                                 (if (not all-min-pt)
                                   (setq all-min-pt temp-min)
                                   (setq all-min-pt (list (min (car all-min-pt) (car temp-min))
                                                          (min (cadr all-min-pt) (cadr temp-min))
                                                          (min (caddr all-min-pt) (caddr temp-min))))
                                 )

                                 (if (not all-max-pt)
                                   (setq all-max-pt temp-max)
                                   (setq all-max-pt (list (max (car all-max-pt) (car temp-max))
                                                          (max (cadr all-max-pt) (cadr temp-max))
                                                          (max (caddr all-max-pt) (caddr temp-max))))
                                 )
                               )
                             )
                           )
                        )
                      )

                      ; Zoom den vung chung
                      (if (and all-min-pt all-max-pt)
                        (progn
                          (command "_.zoom" "_window" all-min-pt all-max-pt)
                          (command "_.zoom" "0.8x")
                        )
                      )
                    )
                    ; Neu chi co 1 doi tuong
                    (progn
                      (setq obj (car zoom-objects))
                      (vl-catch-all-apply
                        '(lambda ()
                           (vla-getboundingbox obj 'min-pt 'max-pt)
                           (if (and min-pt max-pt)
                             (progn
                               (setq min-pt (vlax-safearray->list min-pt))
                               (setq max-pt (vlax-safearray->list max-pt))
                               (command "_.zoom" "_window" min-pt max-pt)
                               (command "_.zoom" "0.8x")
                             )
                           )
                         )
                      )
                    )
                  )

                  ; Select cac doi tuong sau khi zoom
                  (if select-objects
                    (progn
                      ; Tao selection set moi
                      (setq ss (ssadd))
                      (foreach ent select-objects
                        (if ent
                          (setq ss (ssadd ent ss))
                        )
                      )

                      ; Set selection set hien tai
                      (if (> (sslength ss) 0)
                        (progn
                          (sssetfirst nil ss)
                          (princ (strcat "\nDa select " (itoa (sslength ss)) " doi tuong."))
                        )
                        (princ "\nKhong co doi tuong nao de select.")
                      )
                    )
                  )

                  (princ (strcat "\nHoan thanh! Da zoom va select " (itoa found-count) " doi tuong."))
                )
                (princ "\nKhong tim thay doi tuong nao voi cac handle da cung cap.")
              )

              (vlax-release-object excel-app)
            )
          )
        )
      )
    )
    (princ "\nLoi: Khong the ket noi den Excel.")
  )

  (princ)
)

; ===================================================================
; PHAN 3: CODE TU HNP_Cad Link Excel_CTE_fixed.lsp
; ===================================================================

; Ham mo file CAD theo comment (HTC cu)
(defun C:HTC (/ excel-app excel-wb excel-ws active-cell comment-obj comment-text cell-value
              file-path file-pos clean-path)

  (princ "\nBat dau ket noi Excel...")

  ; Ket noi den Excel
  (setq excel-app (vlax-get-or-create-object "Excel.Application"))

  (if excel-app
    (progn
      (princ "\nDa ket noi Excel thanh cong.")

      ; Lay workbook va worksheet hien tai
      (setq excel-wb nil)
      (setq excel-ws nil)
      (setq active-cell nil)

      (vl-catch-all-apply
        '(lambda ()
           (setq excel-wb (vlax-get-property excel-app "ActiveWorkbook"))
           (setq excel-ws (vlax-get-property excel-app "ActiveSheet"))
           (setq active-cell (vlax-get-property excel-app "ActiveCell"))
         )
      )

      (if (not active-cell)
        (progn
          (princ "\nLoi: Khong the lay active cell tu Excel.")
          (vlax-release-object excel-app)
        )
        (progn
          (princ "\nDa lay active cell thanh cong.")

          ; Lay text tu comment neu co
          (setq comment-text "")
          (setq comment-obj nil)

          ; Thu lay comment object
          (setq comment-result
            (vl-catch-all-apply
              '(lambda ()
                 (setq comment-obj (vlax-get-property active-cell "Comment"))
               )
            )
          )

          ; Neu co comment, lay text
          (if (vl-catch-all-error-p comment-result)
            (progn
              (princ "\nKhong co comment hoac loi khi lay comment.")
              (setq comment-text "")
            )
            (progn
              (if comment-obj
                (progn
                  (princ "\nDa tim thay comment object.")
                  ; Thu lay comment text bang nhieu cach
                  (setq comment-text nil)

                  ; Cach 1: Dung vlax-get-property
                  (setq text-result1
                    (vl-catch-all-apply
                      '(lambda ()
                         (setq comment-text (vlax-get-property comment-obj "Text"))
                       )
                    )
                  )

                  ; Cach 2: Neu cach 1 that bai, thu dung vlax-invoke
                  (if (or (vl-catch-all-error-p text-result1) (not comment-text))
                    (setq text-result2
                      (vl-catch-all-apply
                        '(lambda ()
                           (setq comment-text (vlax-invoke-method comment-obj "Text"))
                         )
                      )
                    )
                  )

                  ; Cach 3: Thu truy cap truc tiep
                  (if (or (not comment-text) (= comment-text ""))
                    (setq text-result3
                      (vl-catch-all-apply
                        '(lambda ()
                           (setq comment-text (vlax-get comment-obj "Text"))
                         )
                      )
                    )
                  )

                  ; Kiem tra ket qua
                  (if (and comment-text (/= comment-text ""))
                    (progn
                      ; Xu ly variant value
                      (if (= (type comment-text) 'VARIANT)
                        (setq comment-text (vlax-variant-value comment-text))
                      )
                      (if (not (= (type comment-text) 'STR))
                        (setq comment-text (vl-princ-to-string comment-text))
                      )
                      (princ (strcat "\nComment text: " comment-text))
                    )
                    (progn
                      (princ "\nKhong lay duoc comment text bang bat ky cach nao.")
                      (setq comment-text "")
                    )
                  )
                )
                (progn
                  (princ "\nComment object la nil.")
                  (setq comment-text "")
                )
              )
            )
          )

          ; Neu comment khong co file path, lay tu cell value
          (if (or (not comment-text) (= comment-text ""))
            (progn
              (setq cell-value nil)
              (setq value-result
                (vl-catch-all-apply
                  '(lambda ()
                     (setq cell-value (vlax-get-property active-cell "Value"))
                   )
                )
              )

              (if (and (not (vl-catch-all-error-p value-result)) cell-value)
                (progn
                  ; Xu ly variant value
                  (if (= (type cell-value) 'VARIANT)
                    (setq cell-value (vlax-variant-value cell-value))
                  )
                  (setq cell-value (vl-princ-to-string cell-value))
                  (princ (strcat "\nCell value: " cell-value))
                  (setq comment-text cell-value)
                )
                (setq comment-text "")
              )
            )
          )

          ; Tim file path trong comment hoac cell
          (setq file-path "")
          (if (and comment-text (/= comment-text ""))
            (progn
              ; Tim vi tri cua "FileCad:" hoac "FILECAD:"
              (setq file-pos (vl-string-search "FILECAD:" (strcase comment-text)))
              (if file-pos
                (progn
                  ; Lay phan sau "FileCad:"
                  (setq file-path (substr comment-text (+ file-pos 9)))
                  ; Loai bo khoang trang va xuong dong
                  (setq file-path (vl-string-trim " \t\n\r" file-path))
                  ; Loai bo cac ky tu dac biet neu co
                  (setq clean-path file-path)
                  (princ (strcat "\nFile path tim thay: " clean-path))
                )
                (progn
                  ; Neu khong co "FileCad:", coi toan bo text la file path
                  (setq clean-path (vl-string-trim " \t\n\r" comment-text))
                  (princ (strcat "\nSu dung toan bo text lam file path: " clean-path))
                )
              )
            )
          )

          ; Kiem tra va mo file
          (if (and clean-path (/= clean-path ""))
            (progn
              ; Kiem tra file co ton tai khong
              (if (findfile clean-path)
                (progn
                  (princ (strcat "\nDang mo file: " clean-path))
                  ; Mo file CAD
                  (vl-catch-all-apply
                    '(lambda ()
                       (command "_.open" clean-path)
                     )
                  )
                  (princ "\nDa mo file thanh cong!")
                )
                (progn
                  (princ (strcat "\nLoi: Khong tim thay file: " clean-path))
                  ; Thu tim file trong cac thu muc gan do
                  (setq file-name (vl-filename-base clean-path))
                  (setq file-ext (vl-filename-extension clean-path))
                  (if (not file-ext) (setq file-ext ".dwg"))
                  (setq search-file (strcat file-name file-ext))

                  ; Thu tim trong thu muc hien tai
                  (if (findfile search-file)
                    (progn
                      (princ (strcat "\nTim thay file trong thu muc hien tai: " search-file))
                      (vl-catch-all-apply
                        '(lambda ()
                           (command "_.open" search-file)
                         )
                      )
                      (princ "\nDa mo file thanh cong!")
                    )
                    (princ (strcat "\nKhong tim thay file: " search-file " trong thu muc hien tai."))
                  )
                )
              )
            )
            (princ "\nKhong tim thay duong dan file trong comment hoac cell.")
          )

          (vlax-release-object excel-app)
        )
      )
    )
    (princ "\nLoi: Khong the ket noi den Excel.")
  )

  (princ)
)

; ===================================================================
; PHAN 4: CAC LENH MOI THEO YEU CAU
; ===================================================================

; ===================================================================
; NHOM LENH E1: XUAT CELL
; ===================================================================

; E11 - Xuat text/dim voi he so nhan them (=(n1+n2+...)*a)
(defun C:E11 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	numlist
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	coefficient
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Nhap he so nhan
	(or *last-coefficient* (setq *last-coefficient* "1"))
	(setq coefficient (getstring (strcat "\nNhap he so nhan <" *last-coefficient* ">: ")))
	(if (or (not coefficient) (= coefficient ""))
		(setq coefficient *last-coefficient*)
		(setq *last-coefficient* coefficient)
	)
	(setq coefficient (atof coefficient))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))
			(setq numlist '())
			(setq factor (atof *E6-factor*))

			; Thu thap gia tri so
			(foreach txt textlist
				(setq objname (caddr txt))
				(cond
					; TEXT va MTEXT - trich xuat so
					((or (= objname "AcDbText") (= objname "AcDbMText"))
						(setq numvalue (EXTRACT-NUMBER (car txt)))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq numlist (cons (car numvalue) numlist))
									; Khong co don vi - nhan he so
									(setq numlist (cons (* numvalue factor) numlist))
								)
							)
						)
					)
					; DIMENSION - lay measurement
					((wcmatch objname "*Dimension*")
						(setq numvalue (atof (car txt)))
						(if numvalue
							(setq numlist (cons (* numvalue factor) numlist))
						)
					)
				)
			)

			(setq numlist (reverse numlist))

			; Xac dinh so chu so thap phan
			(setq decimal-places (if (= (atof *E6-factor*) 1.0) 0 3))

			; Tao cong thuc Excel =(n1+n2+...)*a
			(if (> (length numlist) 0)
				(progn
					(setq result-text "=(")
					(setq first-item T)
					(foreach num numlist
						(if first-item
							(progn
								(setq result-text (strcat result-text (rtos num 2 decimal-places)))
								(setq first-item nil)
							)
							(setq result-text (strcat result-text "+" (rtos num 2 decimal-places)))
						)
					)
					(setq result-text (strcat result-text ")*" (rtos coefficient 2 3)))
				)
				(setq result-text "")
			)

			; Ghi vao Excel
			(if (and result-text (> (strlen result-text) 0))
				(progn
					(SETCELLTEXT xlcells startrow startcol result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							; Thu nhieu cach lay cell
							(setq result (vl-catch-all-apply '(lambda ()
								(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
							(if (vl-catch-all-error-p result)
								(progn
									; Thu cach khac
									(setq result (vl-catch-all-apply '(lambda ()
										(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
										(setq worksheet (vlax-get-property xlcells "Parent"))
										(setq targetcell (vlax-get-property worksheet "Range" celladdress))
										(WRITE-COMMENT-TO-CELL targetcell commenttext)
									)))
								)
							)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong co gia tri de ghi")
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; E12 - Xuat text/dim vao cell (text giu nguyen + dim co factor) - tuong duong E2 cu
(defun C:E12 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	numlist
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))
			(setq numlist '())
			(setq factor (atof *E6-factor*))

			; Thu thap gia tri
			(foreach txt textlist
				(setq objname (caddr txt))
				(cond
					; TEXT va MTEXT - giu nguyen text
					((or (= objname "AcDbText") (= objname "AcDbMText"))
						(setq numlist (cons (car txt) numlist))
					)
					; DIMENSION - lay measurement va nhan factor
					((wcmatch objname "*Dimension*")
						(setq numvalue (atof (car txt)))
						(if numvalue
							(progn
								(setq decimal-places (if (= (atof *E6-factor*) 1.0) 0 3))
								(setq numlist (cons (rtos (* numvalue factor) 2 decimal-places) numlist))
							)
						)
					)
				)
			)

			(setq numlist (reverse numlist))

			; Tao noi dung cell
			(cond
				; 1 gia tri
				((= (length numlist) 1)
					(setq result-text (car numlist))
				)
				; 2 gia tri
				((= (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel khi symbol la "+"
						(setq result-text (strcat "=" (car numlist) "+" (cadr numlist)))
						; Noi binh thuong voi symbol khac
						(setq result-text (strcat (car numlist) *E6-symbol* (cadr numlist)))
					)
				)
				; Nhieu hon 2 gia tri
				((> (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel khi symbol la "+"
						(progn
							(setq result-text (strcat "=" (car numlist)))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text "+" num))
							)
						)
						; Noi binh thuong voi symbol khac
						(progn
							(setq result-text (car numlist))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text *E6-symbol* num))
							)
						)
					)
				)
				; Khong co gia tri
				(T (setq result-text ""))
			)

			; Ghi vao Excel
			(if (and result-text (> (strlen result-text) 0))
				(progn
					(SETCELLTEXT xlcells startrow startcol result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							; Thu nhieu cach lay cell
							(setq result (vl-catch-all-apply '(lambda ()
								(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
							(if (vl-catch-all-error-p result)
								(progn
									; Thu cach khac
									(setq result (vl-catch-all-apply '(lambda ()
										(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
										(setq worksheet (vlax-get-property xlcells "Parent"))
										(setq targetcell (vlax-get-property worksheet "Range" celladdress))
										(WRITE-COMMENT-TO-CELL targetcell commenttext)
									)))
								)
							)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong co gia tri de ghi")
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; E14 - Dat nhanh he so (factor)=1
(defun C:E14 ()
	(setq *E6-factor* "1")
	(princ "\nDa dat he so factor = 1")
	(princ)
)

; E15 - Dat nhanh he so (factor)=0.001
(defun C:E15 ()
	(setq *E6-factor* "0.001")
	(princ "\nDa dat he so factor = 0.001")
	(princ)
)

; ===================================================================
; NHOM LENH E2: XUAT COL, ROW, ARRAY
; ===================================================================

; E2 - Xuat theo 3 che do (Col/Row/Array) voi thu tu selection - tuong duong E3 cu
(defun C:E2 ()
	(princ "\nGoi lenh E3 cu...")
	(if (fboundp 'C:E3-OLD)
		(C:E3-OLD)
		(princ "\nLenh E3-OLD chua duoc dinh nghia. Se bo sung sau.")
	)
	(princ)
)

; E21 - Xuat nhanh ra cot (vi tri chuot tu dong nhay xuong o cuoi cung cua cot)
(defun C:E21 ()
	(princ "\nXuat nhanh ra cot...")
	; Tam thoi luu che do hien tai
	(setq temp-mode (if (boundp '*E5-mode*) *E5-mode* "1"))
	(setq *E5-mode* "1") ; Che do cot
	(if (fboundp 'C:E3-OLD)
		(C:E3-OLD)
		(princ "\nLenh E3-OLD chua duoc dinh nghia. Se bo sung sau.")
	)
	; Khoi phuc che do cu
	(setq *E5-mode* temp-mode)
	(princ)
)

; E22 - Xuat nhanh ra hang (vi tri chuot tu dong nhay qua phai o cuoi cung cua hang)
(defun C:E22 ()
	(princ "\nXuat nhanh ra hang...")
	; Tam thoi luu che do hien tai
	(setq temp-mode (if (boundp '*E5-mode*) *E5-mode* "2"))
	(setq *E5-mode* "2") ; Che do hang
	(if (fboundp 'C:E3-OLD)
		(C:E3-OLD)
		(princ "\nLenh E3-OLD chua duoc dinh nghia. Se bo sung sau.")
	)
	; Khoi phuc che do cu
	(setq *E5-mode* temp-mode)
	(princ)
)

; E23 - Xuat nhanh ra mang (vi tri chuot tu dong nhay xuong o cuoi cung cua mang)
(defun C:E23 ()
	(princ "\nXuat nhanh ra mang...")
	; Tam thoi luu che do hien tai
	(setq temp-mode (if (boundp '*E5-mode*) *E5-mode* "4"))
	(setq *E5-mode* "4") ; Che do mang
	(if (fboundp 'C:E3-OLD)
		(C:E3-OLD)
		(princ "\nLenh E3-OLD chua duoc dinh nghia. Se bo sung sau.")
	)
	; Khoi phuc che do cu
	(setq *E5-mode* temp-mode)
	(princ)
)

; E24 - Bat nhanh Number (Y)
(defun C:E24 ()
	(setq *E6-number* "Y")
	(princ "\nDa bat Number = Y")
	(princ)
)

; E25 - Tat nhanh Number (N)
(defun C:E25 ()
	(setq *E6-number* "N")
	(princ "\nDa tat Number = N")
	(princ)
)

; ===================================================================
; NHOM LENH E3: GHI HANDLE
; ===================================================================

; E3 - Chi ghi handle vao comment - tuong duong E4 cu
(defun C:E3 ()
	(princ "\nGoi lenh E4 cu...")
	(if (fboundp 'C:E4-OLD)
		(C:E4-OLD)
		(princ "\nLenh E4-OLD chua duoc dinh nghia. Se bo sung sau.")
	)
	(princ)
)

; E31 - Mo file Cad theo comment (tuong duong lenh HTC cu)
(defun C:E31 ()
	(princ "\nGoi lenh HTC...")
	(C:HTC)
	(princ)
)

; E32 - Zoom va highlight doi tuong theo handle (tuong duong lenh ZTH cu)
(defun C:E32 ()
	(princ "\nGoi lenh ZTH...")
	(C:ZTH)
	(princ)
)

; E33 - Zoom va select doi tuong theo handle (tuong duong lenh STH cu)
(defun C:E33 ()
	(princ "\nGoi lenh STH...")
	(C:STH)
	(princ)
)

; E34 - Bat nhanh handle (gan che do thiet lap handle Y)
(defun C:E34 ()
	(setq *E6-handle* "Y")
	(princ "\nDa bat Handle = Y")
	(princ)
)

; E35 - Tat nhanh handle (gan che do thiet lap handle N)
(defun C:E35 ()
	(setq *E6-handle* "N")
	(princ "\nDa tat Handle = N")
	(princ)
)

; ===================================================================
; NHOM LENH E4: XUAT TABLE
; ===================================================================

; E4 - Xuat bang theo line (tuong duong E5 cu)
(defun C:E4 ()
	(princ "\nGoi lenh E5 cu...")
	(if (fboundp 'C:E5-OLD)
		(C:E5-OLD)
		(princ "\nLenh E5-OLD chua duoc dinh nghia. Se bo sung sau.")
	)
	(princ)
)

; E41 - Xuat bang theo line (giu format bang) - goi lenh CTE
(defun C:E41 ()
	(princ "\nGoi lenh CTE...")
	(if (fboundp 'C:CTE)
		(C:CTE)
		(princ "\nLenh CTE chua duoc dinh nghia. Se bo sung sau.")
	)
	(princ)
)

; E42 - Xuat bang theo Table - tuong duong lenh TE cu
(defun C:E42 ()
	(princ "\nGoi lenh TE cu...")
	(if (fboundp 'C:TE-OLD)
		(C:TE-OLD)
		(princ "\nLenh TE-OLD chua duoc dinh nghia. Se bo sung sau.")
	)
	(princ)
)

; E43 - Xuat bang tu excel qua cad - goi lenh ETC
(defun C:E43 ()
	(princ "\nGoi lenh ETC...")
	(if (fboundp 'C:ETC)
		(C:ETC)
		(princ "\nLenh ETC chua duoc dinh nghia. Se bo sung sau.")
	)
	(princ)
)

; E44 - Cong tac mo Frame (Y/N)
(defun C:E44 ()
	(if (= *E6-frame* "Y")
		(progn
			(setq *E6-frame* "N")
			(princ "\nDa tat Frame = N")
		)
		(progn
			(setq *E6-frame* "Y")
			(princ "\nDa bat Frame = Y")
		)
	)
	(princ)
)

; ===================================================================
; THIET LAP
; ===================================================================

; E0 - Mo bang thiet lap (tuong duong E6 cu)
(defun C:E0 ()
	(princ "\nGoi lenh E6 cu...")
	(if (fboundp 'C:E6-OLD)
		(C:E6-OLD)
		(princ "\nLenh E6-OLD chua duoc dinh nghia. Se bo sung sau.")
	)
	(princ)
)

; ===================================================================
; PHAN 5: CAC LENH CU DUOC DOI TEN
; ===================================================================

; E3-OLD - Xuat theo 3 che do (Col/Row/Array) voi thu tu selection (tu E3 cu)
(defun C:E3-OLD ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	mode
	newrow
	newcol
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong thong minh
	(setq otcontents (SMART-SELECT-OBJECTS "\nChon text/mtext/dimension:" "TEXT,MTEXT,DIMENSION,*DIMENSION"))

	(if otcontents
		(progn
			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))

			; Hoi che do xuat (neu chua co bien *E5-mode*)
			(if (not (boundp '*E5-mode*))
				(progn
					(princ "\n=== CHON CHE DO XUAT ===")
					(princ "\n1. Cot")
					(princ "\n2. Hang")
					(princ "\n3. O")
					(princ "\n4. Mang")
					(setq mode (getstring "\nChon che do [1/2/3/4]: "))
					(setq *E5-mode* mode)
				)
				(setq mode *E5-mode*)
			)

			(cond
				; Che do cot
				((= mode "1")
					(E5-EXPORT-COL textlist xlcells startrow startcol)
					(setq newrow (+ startrow (length textlist) (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				; Che do hang
				((= mode "2")
					(E5-EXPORT-ROW textlist xlcells startrow startcol)
					(setq newcol (+ startcol (length textlist) (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells startrow newcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa startrow) "," (itoa newcol)))
				)
				; Che do o (cell)
				((= mode "3")
					(E5-EXPORT-CELL textlist xlcells startrow startcol)
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				; Che do mang
				((= mode "4")
					(E5-EXPORT-ARRAY textlist xlcells startrow startcol)
					(setq newrow (+ startrow (E5-GET-ARRAY-ROWS textlist) (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				; Lua chon khong hop le
				(T (princ "\nLua chon khong hop le!"))
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; Ham xuat cot cho E5
(defun E5-EXPORT-COL ( textlist xlcells startrow startcol / i txt content)
	(setq i 0)
	(foreach txt textlist
		(setq content (car txt))
		; Xu ly Number mode
		(if (= *E6-number* "Y")
			(progn
				(setq numvalue (EXTRACT-NUMBER content))
				(if numvalue
					(progn
						; Kiem tra co phai la list (so, :unit) khong
						(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
							; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
							(setq content (rtos (car numvalue) 2 3))
							; Khong co don vi - nhan he so
							(setq content (rtos (* numvalue (atof *E6-factor*)) 2 3))
						)
					)
				)
			)
		)
		(SETCELLTEXT xlcells (+ startrow i) startcol content)
		(setq i (+ i 1))
	)
)

; Ham xuat hang cho E5
(defun E5-EXPORT-ROW ( textlist xlcells startrow startcol / i txt content)
	(setq i 0)
	(foreach txt textlist
		(setq content (car txt))
		; Xu ly Number mode
		(if (= *E6-number* "Y")
			(progn
				(setq numvalue (EXTRACT-NUMBER content))
				(if numvalue
					(progn
						; Kiem tra co phai la list (so, :unit) khong
						(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
							; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
							(setq content (rtos (car numvalue) 2 3))
							; Khong co don vi - nhan he so
							(setq content (rtos (* numvalue (atof *E6-factor*)) 2 3))
						)
					)
				)
			)
		)
		(SETCELLTEXT xlcells startrow (+ startcol i) content)
		(setq i (+ i 1))
	)
)

; Ham xuat cell cho E5
(defun E5-EXPORT-CELL ( textlist xlcells startrow startcol / result-text separator coefficient)
	; Lay separator
	(or *last-separator* (setq *last-separator* "+"))
	(setq separator (getstring (strcat "\nNhap ky tu noi <" *last-separator* ">: ")))
	(if (or (not separator) (= separator ""))
		(setq separator *last-separator*)
		(setq *last-separator* separator)
	)

	; Neu separator la "+", hoi he so
	(if (= separator "+")
		(progn
			(or *last-coefficient* (setq *last-coefficient* "1"))
			(setq coefficient (getstring (strcat "\nNhap he so <" *last-coefficient* ">: ")))
			(if (or (not coefficient) (= coefficient ""))
				(setq coefficient *last-coefficient*)
				(setq *last-coefficient* coefficient)
			)
			(setq coefficient (atof coefficient))

			; Tao cong thuc Excel
			(setq result-text "=(")
			(setq first-item T)
			(foreach txt textlist
				(setq content (car txt))
				; Xu ly Number mode
				(if (= *E6-number* "Y")
					(progn
						(setq numvalue (EXTRACT-NUMBER content))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq content (rtos (car numvalue) 2 3))
									; Khong co don vi - nhan he so
									(setq content (rtos (* numvalue (atof *E6-factor*)) 2 3))
								)
							)
						)
					)
				)
				(if first-item
					(progn
						(setq result-text (strcat result-text content))
						(setq first-item nil)
					)
					(setq result-text (strcat result-text "+" content))
				)
			)
			(setq result-text (strcat result-text ")*" (rtos coefficient 2 3)))
		)
		; Separator khac "+"
		(progn
			(setq result-text "")
			(setq first-item T)
			(foreach txt textlist
				(setq content (car txt))
				; Xu ly Number mode
				(if (= *E6-number* "Y")
					(progn
						(setq numvalue (EXTRACT-NUMBER content))
						(if numvalue
							(progn
								; Kiem tra co phai la list (so, :unit) khong
								(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
									; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
									(setq content (rtos (car numvalue) 2 3))
									; Khong co don vi - nhan he so
									(setq content (rtos (* numvalue (atof *E6-factor*)) 2 3))
								)
							)
						)
					)
				)
				(if first-item
					(progn
						(setq result-text content)
						(setq first-item nil)
					)
					(setq result-text (strcat result-text separator content))
				)
			)
		)
	)

	(SETCELLTEXT xlcells startrow startcol result-text)
)

; Ham xuat mang cho E5
(defun E5-EXPORT-ARRAY ( textlist xlcells startrow startcol / sorted-list grid-data row col content)
	; Sap xep theo toa do Y giam dan, roi X tang dan
	(setq sorted-list (vl-sort textlist '(lambda (a b)
		(if (equal (cadr (cadr a)) (cadr (cadr b)) 50.0)
			(< (car (cadr a)) (car (cadr b)))
			(> (cadr (cadr a)) (cadr (cadr b)))
		)
	)))

	; Tao luoi ao
	(setq grid-data (E5-CREATE-VIRTUAL-GRID sorted-list))

	; Xuat ra Excel
	(setq row 0)
	(foreach grid-row grid-data
		(setq col 0)
		(foreach cell-content grid-row
			(if (and cell-content (/= cell-content ""))
				(progn
					; Xu ly Number mode
					(setq content cell-content)
					(if (= *E6-number* "Y")
						(progn
							(setq numvalue (EXTRACT-NUMBER content))
							(if numvalue
								(progn
									; Kiem tra co phai la list (so, :unit) khong
									(if (and (= (type numvalue) 'LIST) (= (cadr numvalue) :unit))
										; Co don vi m2/m - giu nguyen gia tri, khong nhan he so
										(setq content (rtos (car numvalue) 2 3))
										; Khong co don vi - nhan he so
										(setq content (rtos (* numvalue (atof *E6-factor*)) 2 3))
									)
								)
							)
						)
					)
					(SETCELLTEXT xlcells (+ startrow row) (+ startcol col) content)
				)
			)
			(setq col (+ col 1))
		)
		(setq row (+ row 1))
	)
)

; Ham tao luoi ao cho mang
(defun E5-CREATE-VIRTUAL-GRID ( textlist / x-coords y-coords unique-x unique-y grid-data row-idx col-idx txt x y content)
	; Thu thap toa do X va Y
	(setq x-coords '() y-coords '())
	(foreach txt textlist
		(setq x (car (cadr txt)) y (cadr (cadr txt)))
		(setq x-coords (cons x x-coords))
		(setq y-coords (cons y y-coords))
	)

	; Lay cac gia tri duy nhat va sap xep
	(setq unique-x (vl-sort (E5-REMOVE-DUPLICATES x-coords 50.0) '<))
	(setq unique-y (vl-sort (E5-REMOVE-DUPLICATES y-coords 50.0) '>))

	; Tao luoi trong
	(setq grid-data '())
	(foreach y-val unique-y
		(setq row-data '())
		(foreach x-val unique-x
			; Tim text tai vi tri nay
			(setq content "")
			(foreach txt textlist
				(setq x (car (cadr txt)) y (cadr (cadr txt)))
				(if (and (< (abs (- x x-val)) 50.0) (< (abs (- y y-val)) 50.0))
					(setq content (car txt))
				)
			)
			(setq row-data (append row-data (list content)))
		)
		(setq grid-data (append grid-data (list row-data)))
	)

	grid-data
)

; Ham loai bo cac gia tri trung lap voi tolerance
(defun E5-REMOVE-DUPLICATES ( lst tolerance / result item)
	(setq result '())
	(foreach item lst
		(if (not (E5-FIND-SIMILAR result item tolerance))
			(setq result (cons item result))
		)
	)
	(reverse result)
)

; Ham tim gia tri tuong tu trong danh sach
(defun E5-FIND-SIMILAR ( lst value tolerance / found item)
	(setq found nil)
	(foreach item lst
		(if (< (abs (- item value)) tolerance)
			(setq found T)
		)
	)
	found
)

; Ham lay so hang cua mang
(defun E5-GET-ARRAY-ROWS ( textlist / y-coords unique-y)
	(setq y-coords '())
	(foreach txt textlist
		(setq y-coords (cons (cadr (cadr txt)) y-coords))
	)
	(setq unique-y (E5-REMOVE-DUPLICATES y-coords 50.0))
	(length unique-y)
)

; E4-OLD - Chi ghi handle vao comment (tu E4 cu)
(defun C:E4-OLD ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	handlelist
	commenttext
	dwgpath
	dwgname
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong
	(princ "\nChon doi tuong de ghi handle: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Lay handle
			(setq handlelist (GET-HANDLES otcontents))
			(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))

			; Ghi comment vao cell hien tai
			(setq result (vl-catch-all-apply '(lambda ()
				(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
				(WRITE-COMMENT-TO-CELL targetcell commenttext)
			)))
			(if (vl-catch-all-error-p result)
				(progn
					; Thu cach khac
					(setq result (vl-catch-all-apply '(lambda ()
						(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
						(setq worksheet (vlax-get-property xlcells "Parent"))
						(setq targetcell (vlax-get-property worksheet "Range" celladdress))
						(WRITE-COMMENT-TO-CELL targetcell commenttext)
					)))
				)
			)

			; Nhay chuot
			(setq newrow (+ startrow (atoi *E6-jump*)))
			(MOVE-CURSOR xlcells newrow startcol)
			(princ (strcat "\nHoan thanh! Da ghi handle vao comment. Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; E5-OLD - Xuat table vao Excel (tu E5 cu)
(defun C:E5-OLD ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	lpxlist
	lpylist
	blocklist
	linelist
	colwidths
	rowheights
	newrow
	newcol
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon bang
	(princ "\nChon bang de xuat: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Khoi tao danh sach
			(setq textlist '() lpxlist '() lpylist '() blocklist '() linelist '())

			; Xu ly thong tin bang
			(E5-TABLEINFO otcontents)
			(setq lpxlist (vl-sort lpxlist '<) lpylist (vl-sort lpylist '>))

			; Xuat ra Excel
			(E5-EXPORT-TO-EXCEL xlcells startrow startcol)

			; Ve khung neu can
			(if (= *E6-frame* "Y")
				(E5-ADD-BORDERS xlcells startrow startcol (+ startrow (length lpylist) -1) (+ startcol (length lpxlist) -1))
			)

			; Nhay chuot den cuoi bang
			(setq newrow (+ startrow (length lpylist) (atoi *E6-jump*)))
			(MOVE-CURSOR xlcells newrow startcol)
			(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
		)
		(princ "\nKhong chon duoc bang!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; Ham phan tich thong tin bang cho E5
(defun E5-TABLEINFO ( ss / n entlist)
	(setq n 0)
	(repeat (sslength ss)
		(setq entlist (entget (ssname ss n)))
		(cond
			((member (cdr (assoc 0 entlist)) '("LINE" "POLYLINE"))
				(E5-GETLINEPTS entlist)(setq linelist (cons (ssname ss n) linelist)))
			((member (cdr (assoc 0 entlist)) '("TEXT" "MTEXT"))
				(setq textlist (cons (ssname ss n) textlist)))
			((member (cdr (assoc 0 entlist)) '("INSERT"))
				(setq blocklist (cons (ssname ss n) blocklist)))
		)
		(setq n (1+ n))
	)
)

; Ham lay diem line cho E5
(defun E5-GETLINEPTS ( entlist / pt1 pt2)
	(setq pt1 (cdr (assoc 10 entlist)) pt2 (cdr (assoc 11 entlist)))
	(if (not (member (car pt1) lpxlist))(setq lpxlist (cons (car pt1) lpxlist)))
	(if (not (member (car pt2) lpxlist))(setq lpxlist (cons (car pt2) lpxlist)))
	(if (not (member (cadr pt1) lpylist))(setq lpylist (cons (cadr pt1) lpylist)))
	(if (not (member (cadr pt2) lpylist))(setq lpylist (cons (cadr pt2) lpylist)))
)

; Ham xuat bang ra Excel cho E5
(defun E5-EXPORT-TO-EXCEL ( xlcells startrow startcol / r c)
	; Khoi tao bien tinfo va binfo
	(setq tinfo '() binfo '())

	; Xu ly text va block
	(mapcar '(lambda (txt)(E5-GETTXTINFO (entget txt))) textlist)
	(mapcar '(lambda (blk)(E5-GETBLOCKINFO blk)) blocklist)

	; Dat text vao Excel tu vi tri con tro
	(mapcar '(lambda (x / r c)
			(setq r (+ startrow (cadr (assoc "Position" x)))
				  c (+ startcol (caddr (assoc "Position" x))))
			(SETCELLTEXT xlcells r c (cdr (assoc "Content" x)))
		)
		tinfo
	)

	; Dat block vao Excel
	(mapcar '(lambda (x / r c)
			(setq r (+ startrow (cadr (assoc "Position" x)))
				  c (+ startcol (caddr (assoc "Position" x))))
			(SETCELLTEXT xlcells r c (cdr (assoc "Content" x)))
		)
		binfo
	)
)

; Ham lay thong tin text cho E5
(defun E5-GETTXTINFO ( entlist / txtpt txtcontent r c)
	(setq txtpt (cdr (assoc 10 entlist)))
	(setq txtcontent (cdr (assoc 1 entlist)))
	; Lam sach MTEXT neu can
	(if (= (cdr (assoc 0 entlist)) "MTEXT")
		(setq txtcontent (CLEAN-MTEXT txtcontent))
		(setq txtcontent (CONVERT-SPECIAL-SYMBOLS txtcontent))
	)
	(setq r (E5-GETROWINDEX (cadr txtpt)) c (E5-GETCOLINDEX (car txtpt)))
	(setq tinfo (cons (list (cons "Position" (list 0 r c)) (cons "Content" txtcontent)) tinfo))
)

; Ham lay thong tin block cho E5
(defun E5-GETBLOCKINFO ( blkent / blkpt blkcontent r c entlist)
	(setq entlist (entget blkent))
	(setq blkpt (cdr (assoc 10 entlist)))
	(setq blkcontent (cdr (assoc 2 entlist)))
	(setq r (E5-GETROWINDEX (cadr blkpt)) c (E5-GETCOLINDEX (car blkpt)))
	(setq binfo (cons (list (cons "Position" (list 0 r c)) (cons "Content" blkcontent)) binfo))
)

; Ham lay chi so hang
(defun E5-GETROWINDEX ( yval / i found)
	(setq i 0 found nil)
	(while (and (< i (length lpylist)) (not found))
		(if (< (abs (- yval (nth i lpylist))) (atof *E6-tolerance*))
			(setq found i)
			(setq i (+ i 1))
		)
	)
	(if found found 0)
)

; Ham lay chi so cot
(defun E5-GETCOLINDEX ( xval / i found)
	(setq i 0 found nil)
	(while (and (< i (length lpxlist)) (not found))
		(if (< (abs (- xval (nth i lpxlist))) (atof *E6-tolerance*))
			(setq found i)
			(setq i (+ i 1))
		)
	)
	(if found found 0)
)

; Ham ve khung cho E5
(defun E5-ADD-BORDERS ( xlcells startrow startcol endrow endcol / range worksheet xlapp startaddr endaddr result)
	; Thu cach 1: Su dung Range truc tiep
	(setq result (vl-catch-all-apply '(lambda ()
		(setq startaddr (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
		(setq endaddr (strcat (COLUMN-NUMBER-TO-LETTER endcol) (itoa endrow)))
		(setq worksheet (vlax-get-property xlcells "Parent"))
		(setq range (vlax-get-property worksheet "Range" (strcat startaddr ":" endaddr)))
		(vlax-put-property (vlax-get-property range "Borders") "LineStyle" 1)
	)))

	; Neu that bai, thu cach 2
	(if (vl-catch-all-error-p result)
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				(setq xlapp (vlax-get-object "Excel.Application"))
				(setq worksheet (vlax-get-property xlapp "ActiveSheet"))
				(setq range (vlax-get-property worksheet "Range" (strcat startaddr ":" endaddr)))
				(vlax-put-property (vlax-get-property range "Borders") "LineStyle" 1)
			)))
		)
	)
)

; E6-OLD - Thiet lap toan cuc (tu E6 cu)
(defun C:E6-OLD ( / dcl_id result)
	; Tao file DCL tam thoi
	(setq dcl_file (vl-filename-mktemp "e6_settings" nil ".dcl"))
	(setq dcl_content
		(strcat
			"e6_settings : dialog {\n"
			"  label = \"Thiet lap E6 - Cau hinh toan cuc\";\n"
			"  : row {\n"
			"    : column {\n"
			"      : edit_box {\n"
			"        key = \"handle\";\n"
			"        label = \"Handle (Y/N):\";\n"
			"        edit_width = 10;\n"
			"      }\n"
			"      : edit_box {\n"
			"        key = \"frame\";\n"
			"        label = \"Frame (Y/N):\";\n"
			"        edit_width = 10;\n"
			"      }\n"
			"      : edit_box {\n"
			"        key = \"symbol\";\n"
			"        label = \"Symbol:\";\n"
			"        edit_width = 10;\n"
			"      }\n"
			"    }\n"
			"    : column {\n"
			"      : edit_box {\n"
			"        key = \"factor\";\n"
			"        label = \"Factor:\";\n"
			"        edit_width = 10;\n"
			"      }\n"
			"      : edit_box {\n"
			"        key = \"jump\";\n"
			"        label = \"Jump:\";\n"
			"        edit_width = 10;\n"
			"      }\n"
			"      : edit_box {\n"
			"        key = \"tolerance\";\n"
			"        label = \"Tolerance:\";\n"
			"        edit_width = 10;\n"
			"      }\n"
			"    }\n"
			"  }\n"
			"  : edit_box {\n"
			"    key = \"number\";\n"
			"    label = \"Number (Y/N):\";\n"
			"    edit_width = 10;\n"
			"  }\n"
			"  : row {\n"
			"    : button {\n"
			"      key = \"accept\";\n"
			"      label = \"OK\";\n"
			"      is_default = true;\n"
			"    }\n"
			"    : button {\n"
			"      key = \"cancel\";\n"
			"      label = \"Cancel\";\n"
			"      is_cancel = true;\n"
			"    }\n"
			"  }\n"
			"}\n"
		)
	)

	; Ghi file DCL
	(setq dcl_handle (open dcl_file "w"))
	(write-line dcl_content dcl_handle)
	(close dcl_handle)

	; Load va hien thi dialog
	(setq dcl_id (load_dialog dcl_file))
	(if (not (new_dialog "e6_settings" dcl_id))
		(progn
			(princ "\nLoi: Khong the tao dialog!")
			(unload_dialog dcl_id)
			(vl-file-delete dcl_file)
		)
		(progn
			; Dat gia tri mac dinh
			(set_tile "handle" *E6-handle*)
			(set_tile "frame" *E6-frame*)
			(set_tile "symbol" *E6-symbol*)
			(set_tile "factor" *E6-factor*)
			(set_tile "jump" *E6-jump*)
			(set_tile "tolerance" *E6-tolerance*)
			(set_tile "number" *E6-number*)

			; Xu ly su kien
			(action_tile "accept" "(setq result \"OK\")(done_dialog)")
			(action_tile "cancel" "(setq result \"CANCEL\")(done_dialog)")

			; Hien thi dialog
			(start_dialog)

			; Xu ly ket qua
			(if (= result "OK")
				(progn
					; Luu cac gia tri moi
					(setq *E6-handle* (get_tile "handle"))
					(setq *E6-frame* (get_tile "frame"))
					(setq *E6-symbol* (get_tile "symbol"))
					(setq *E6-factor* (get_tile "factor"))
					(setq *E6-jump* (get_tile "jump"))
					(setq *E6-tolerance* (get_tile "tolerance"))
					(setq *E6-number* (get_tile "number"))

					; Hien thi thong bao
					(princ "\n=== THIET LAP DA LUU ===")
					(princ (strcat "\nHandle: " *E6-handle*))
					(princ (strcat "\nFrame: " *E6-frame*))
					(princ (strcat "\nSymbol: " *E6-symbol*))
					(princ (strcat "\nFactor: " *E6-factor*))
					(princ (strcat "\nJump: " *E6-jump*))
					(princ (strcat "\nTolerance: " *E6-tolerance*))
					(princ (strcat "\nNumber: " *E6-number*))
				)
				(princ "\nDa huy thiet lap.")
			)

			; Don dep
			(unload_dialog dcl_id)
			(vl-file-delete dcl_file)
		)
	)
	(princ)
)

; TE-OLD - Xuat table CAD sang Excel (tu TE cu)
(defun C:TE-OLD ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	table-ent
	table-obj
	rows
	cols
	row
	col
	cell-text
	newrow
	handlelist
	commenttext
	dwgpath
	dwgname
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon table CAD
	(princ "\nChon table CAD de xuat sang Excel: ")
	(setq table-ent (car (entsel)))

	(if (and table-ent
			 (setq table-obj (vlax-ename->vla-object table-ent))
			 (= (vlax-get table-obj 'ObjectName) "AcDbTable"))
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Lay kich thuoc table
			(setq rows (vla-get-Rows table-obj)
				  cols (vla-get-Columns table-obj))

			(princ (strcat "\nTable co " (itoa rows) " hang va " (itoa cols) " cot"))

			; Xuat du lieu table sang Excel
			(setq row 0)
			(while (< row rows)
				(setq col 0)
				(while (< col cols)
					; Lay noi dung cell
					(setq cell-text (vl-catch-all-apply '(lambda ()
						(vla-GetText table-obj row col)
					)))

					; Neu lay duoc text, xu ly MTEXT format va ghi vao Excel
					(if (and (not (vl-catch-all-error-p cell-text))
							 (> (strlen cell-text) 0))
						(progn
							; Xu ly MTEXT format neu can - dung ham chuyen doi dac biet cho table
							(setq cell-text (CLEAN-TABLE-TEXT cell-text))
							; Ghi vao Excel
							(SETCELLTEXT xlcells (+ startrow row) (+ startcol col) cell-text)
						)
					)
					(setq col (+ col 1))
				)
				(setq row (+ row 1))
			)

			; Ghi handle neu can
			(if (= *E6-handle* "Y")
				(progn
					; Tao selection set chua table
					(setq table-ss (ssadd))
					(ssadd table-ent table-ss)
					(setq handlelist (GET-HANDLES table-ss))
					(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))

					; Ghi comment vao cell dau tien cua table
					(setq result (vl-catch-all-apply '(lambda ()
						(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
						(WRITE-COMMENT-TO-CELL targetcell commenttext)
					)))
					(if (vl-catch-all-error-p result)
						(progn
							; Thu cach khac
							(setq result (vl-catch-all-apply '(lambda ()
								(setq celladdress (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
								(setq worksheet (vlax-get-property xlcells "Parent"))
								(setq targetcell (vlax-get-property worksheet "Range" celladdress))
								(WRITE-COMMENT-TO-CELL targetcell commenttext)
							)))
						)
					)
				)
			)

			; Ve khung neu can
			(if (= *E6-frame* "Y")
				(TE-ADD-BORDERS xlcells startrow startcol (+ startrow rows -1) (+ startcol cols -1))
			)

			; Nhay chuot xuong duoi table
			(setq newrow (+ startrow rows (atoi *E6-jump*)))
			(MOVE-CURSOR xlcells newrow startcol)
			(princ (strcat "\nHoan thanh! Da xuat " (itoa rows) " hang x " (itoa cols) " cot. Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
		)
		(princ "\nDoi tuong duoc chon khong phai la table CAD!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; Ham ve khung cho table
(defun TE-ADD-BORDERS ( xlcells startrow startcol endrow endcol / range worksheet xlapp startaddr endaddr result)
	; Thu cach 1: Su dung Range truc tiep
	(setq result (vl-catch-all-apply '(lambda ()
		(setq startaddr (strcat (COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
		(setq endaddr (strcat (COLUMN-NUMBER-TO-LETTER endcol) (itoa endrow)))
		(setq worksheet (vlax-get-property xlcells "Parent"))
		(setq range (vlax-get-property worksheet "Range" (strcat startaddr ":" endaddr)))
		(vlax-put-property (vlax-get-property range "Borders") "LineStyle" 1)
	)))

	; Neu that bai, thu cach 2
	(if (vl-catch-all-error-p result)
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				(setq xlapp (vlax-get-object "Excel.Application"))
				(setq worksheet (vlax-get-property xlapp "ActiveSheet"))
				(setq range (vlax-get-property worksheet "Range" (strcat startaddr ":" endaddr)))
				(vlax-put-property (vlax-get-property range "Borders") "LineStyle" 1)
			)))
		)
	)
)

; CTE - Cad To Excel (phien ban don gian)
(defun C:CTE ( /
	CheckActiveCell
	CheckRun
	ListCoordinateX
	ListCoordinateY
	ListDataTable
	ListVlaObjectText
	ToleranceValue
	VlaDrawingCurrent)

	(vl-load-com)
	(setq VlaDrawingCurrent (vla-get-activedocument (vlax-get-acad-object)))
	(vla-startundomark VlaDrawingCurrent)
	(setq ToleranceValue 1e-8)

	; Kiem tra Excel co mo khong
	(setq CheckActiveCell (CTE-CHECK-EXCEL))
	(if CheckActiveCell
		(progn
			(princ "\nChon cac doi tuong text/mtext de xuat sang Excel:")
			; Chon doi tuong
			(setq ListVlaObjectText (CTE-SELECT-OBJECTS))
			(if ListVlaObjectText
				(progn
					; Xu ly va xuat ra Excel
					(setq ListDataTable (CTE-PROCESS-OBJECTS ListVlaObjectText ToleranceValue))
					(if ListDataTable
						(progn
							(CTE-EXPORT-TO-EXCEL ListDataTable)
							(princ "\nHoan thanh xuat du lieu sang Excel!")
						)
						(princ "\nKhong co du lieu de xuat!")
					)
				)
				(princ "\nKhong chon duoc doi tuong!")
			)
		)
		(princ "\nLoi: Excel chua duoc mo hoac khong the ket noi!")
	)

	(vla-endundomark VlaDrawingCurrent)
	(princ)
)

; Ham kiem tra Excel
(defun CTE-CHECK-EXCEL ( / xlapp result)
	(setq result (vl-catch-all-apply '(lambda ()
		(setq xlapp (vlax-get-object "Excel.Application"))
		T
	)))
	(not (vl-catch-all-error-p result))
)

; Ham chon doi tuong
(defun CTE-SELECT-OBJECTS ( / ss obj-list i ent obj)
	(setq ss (ssget '((0 . "TEXT,MTEXT"))))
	(if ss
		(progn
			(setq obj-list '() i 0)
			(repeat (sslength ss)
				(setq ent (ssname ss i))
				(setq obj (vlax-ename->vla-object ent))
				(setq obj-list (cons obj obj-list))
				(setq i (+ i 1))
			)
			(reverse obj-list)
		)
		nil
	)
)

; Ham xu ly doi tuong
(defun CTE-PROCESS-OBJECTS ( obj-list tolerance / data-list obj text-content insert-pt x y)
	(setq data-list '())
	(foreach obj obj-list
		(setq text-content (vl-catch-all-apply '(lambda ()
			(if (= (vla-get-objectname obj) "AcDbMText")
				(CLEAN-MTEXT (vla-get-textstring obj))
				(CONVERT-SPECIAL-SYMBOLS (vla-get-textstring obj))
			)
		)))

		(setq insert-pt (vl-catch-all-apply '(lambda ()
			(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
		)))

		(if (and (not (vl-catch-all-error-p text-content))
				 (not (vl-catch-all-error-p insert-pt))
				 (> (strlen text-content) 0))
			(progn
				(setq x (car insert-pt) y (cadr insert-pt))
				(setq data-list (cons (list text-content x y) data-list))
			)
		)
	)
	(reverse data-list)
)

; Ham xuat ra Excel
(defun CTE-EXPORT-TO-EXCEL ( data-list / excel-data xlapp xlcells startrow startcol
							 sorted-data grid-data row col content)
	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Sap xep du lieu theo toa do
	(setq sorted-data (vl-sort data-list '(lambda (a b)
		(if (equal (caddr a) (caddr b) 50.0)
			(< (cadr a) (cadr b))
			(> (caddr a) (caddr b))
		)
	)))

	; Tao luoi ao va xuat
	(setq grid-data (CTE-CREATE-GRID sorted-data))
	(setq row 0)
	(foreach grid-row grid-data
		(setq col 0)
		(foreach cell-content grid-row
			(if (and cell-content (/= cell-content ""))
				(SETCELLTEXT xlcells (+ startrow row) (+ startcol col) cell-content)
			)
			(setq col (+ col 1))
		)
		(setq row (+ row 1))
	)

	; Nhay chuot
	(setq newrow (+ startrow row (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nChuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
)

; Ham tao luoi ao
(defun CTE-CREATE-GRID ( data-list / x-coords y-coords unique-x unique-y grid-data)
	; Thu thap toa do
	(setq x-coords '() y-coords '())
	(foreach item data-list
		(setq x-coords (cons (cadr item) x-coords))
		(setq y-coords (cons (caddr item) y-coords))
	)

	; Lay gia tri duy nhat
	(setq unique-x (vl-sort (E5-REMOVE-DUPLICATES x-coords 50.0) '<))
	(setq unique-y (vl-sort (E5-REMOVE-DUPLICATES y-coords 50.0) '>))

	; Tao luoi
	(setq grid-data '())
	(foreach y-val unique-y
		(setq row-data '())
		(foreach x-val unique-x
			(setq content "")
			(foreach item data-list
				(if (and (< (abs (- (cadr item) x-val)) 50.0)
						 (< (abs (- (caddr item) y-val)) 50.0))
					(setq content (car item))
				)
			)
			(setq row-data (append row-data (list content)))
		)
		(setq grid-data (append grid-data (list row-data)))
	)
	grid-data
)

; ETC - Excel To Cad (phien ban don gian)
(defun C:ETC ( / xlapp xlcells active-cell start-row start-col
			   data-list row col cell-value text-height insert-pt)

	(vl-load-com)

	; Kiem tra Excel
	(if (not (setq xlapp (vlax-get-object "Excel.Application")))
		(progn
			(princ "\nLoi: Excel chua duoc mo!")
			(exit)
		)
	)

	; Lay thong tin Excel
	(setq xlcells (vlax-get-property (vlax-get-property xlapp "ActiveSheet") "Cells"))
	(setq active-cell (vlax-get-property xlapp "ActiveCell"))
	(setq start-row (vlax-get-property active-cell "Row"))
	(setq start-col (vlax-get-property active-cell "Column"))

	; Nhap chieu cao text
	(or *last-text-height* (setq *last-text-height* "2.5"))
	(setq text-height-str (getstring (strcat "\nNhap chieu cao text <" *last-text-height* ">: ")))
	(if (or (not text-height-str) (= text-height-str ""))
		(setq text-height-str *last-text-height*)
		(setq *last-text-height* text-height-str)
	)
	(setq text-height (atof text-height-str))

	; Nhap diem chen
	(setq insert-pt (getpoint "\nChon diem chen table trong CAD: "))
	(if (not insert-pt)
		(setq insert-pt '(0.0 0.0 0.0))
	)

	; Doc du lieu tu Excel
	(setq data-list (ETC-READ-EXCEL-DATA xlapp start-row start-col))

	; Tao text trong CAD
	(if data-list
		(progn
			(ETC-CREATE-CAD-TEXT data-list insert-pt text-height)
			(princ "\nHoan thanh tao text trong CAD!")
		)
		(princ "\nKhong co du lieu de tao!")
	)

	(princ)
)

; Ham doc du lieu tu Excel
(defun ETC-READ-EXCEL-DATA ( xlapp start-row start-col / data-list row col cell-value max-row max-col)
	(setq data-list '() max-row (+ start-row 20) max-col (+ start-col 10))
	(setq row start-row)
	(while (< row max-row)
		(setq col start-col)
		(setq row-data '())
		(while (< col max-col)
			(setq cell-value (vl-catch-all-apply '(lambda ()
				(vlax-variant-value (vlax-get-property
					(vlax-get-property (vlax-get-property xlapp "ActiveSheet") "Cells")
					"Item" row col))
			)))
			(if (vl-catch-all-error-p cell-value)
				(setq cell-value "")
				(setq cell-value (vl-princ-to-string cell-value))
			)
			(setq row-data (append row-data (list cell-value)))
			(setq col (+ col 1))
		)
		(setq data-list (append data-list (list row-data)))
		(setq row (+ row 1))
	)
	data-list
)

; Ham tao text trong CAD
(defun ETC-CREATE-CAD-TEXT ( data-list insert-pt text-height / row-spacing col-spacing
							 row col text-content text-pt text-obj)
	(setq row-spacing (* text-height 1.5) col-spacing (* text-height 8))
	(setq row 0)
	(foreach row-data data-list
		(setq col 0)
		(foreach cell-content row-data
			(if (and cell-content (/= cell-content "") (/= cell-content "0"))
				(progn
					(setq text-pt (list
						(+ (car insert-pt) (* col col-spacing))
						(- (cadr insert-pt) (* row row-spacing))
						(caddr insert-pt)
					))
					(setq text-obj (vla-addtext
						(vla-get-modelspace (vla-get-activedocument (vlax-get-acad-object)))
						cell-content
						(vlax-3d-point text-pt)
						text-height
					))
				)
			)
			(setq col (+ col 1))
		)
		(setq row (+ row 1))
	)
)

; ===================================================================
; KET THUC FILE
; ===================================================================

(princ "\n╔══════════════════════════════════════════════════════════════╗")
(princ "\n║           Z-CAD2EXCEL-E1_V3 - PHIEN BAN TICH HOP            ║")
(princ "\n║                     Tac gia: Zit Đại Ka                     ║")
(princ "\n║                   Ngay: 20/12/2024                          ║")
(princ "\n╠══════════════════════════════════════════════════════════════╣")
(princ "\n║ HE THONG LENH MOI:                                           ║")
(princ "\n║ === NHOM E1: XUAT CELL ===                                   ║")
(princ "\n║  E1  - Xuat text/dim vao cell (co he so factor)             ║")
(princ "\n║  E11 - Xuat text/dim voi he so nhan them                    ║")
(princ "\n║  E12 - Xuat text/dim (text giu nguyen + dim co factor)      ║")
(princ "\n║  E14 - Dat nhanh factor=1                                   ║")
(princ "\n║  E15 - Dat nhanh factor=0.001                               ║")
(princ "\n║                                                              ║")
(princ "\n║ === NHOM E2: XUAT COL, ROW, ARRAY ===                       ║")
(princ "\n║  E2  - Xuat theo 3 che do (Col/Row/Array)                   ║")
(princ "\n║  E21 - Xuat nhanh ra cot                                    ║")
(princ "\n║  E22 - Xuat nhanh ra hang                                   ║")
(princ "\n║  E23 - Xuat nhanh ra mang                                   ║")
(princ "\n║  E24 - Bat nhanh Number (Y)                                 ║")
(princ "\n║  E25 - Tat nhanh Number (N)                                 ║")
(princ "\n║                                                              ║")
(princ "\n║ === NHOM E3: GHI HANDLE ===                                  ║")
(princ "\n║  E3  - Chi ghi handle vao comment                           ║")
(princ "\n║  E31 - Mo file CAD theo comment                             ║")
(princ "\n║  E32 - Zoom va highlight doi tuong theo handle              ║")
(princ "\n║  E33 - Zoom va select doi tuong theo handle                 ║")
(princ "\n║  E34 - Bat nhanh handle (Y)                                 ║")
(princ "\n║  E35 - Tat nhanh handle (N)                                 ║")
(princ "\n║                                                              ║")
(princ "\n║ === NHOM E4: XUAT TABLE ===                                  ║")
(princ "\n║  E4  - Xuat bang theo line                                  ║")
(princ "\n║  E41 - Xuat bang theo line (giu format) - CTE               ║")
(princ "\n║  E42 - Xuat bang theo Table - TE                            ║")
(princ "\n║  E43 - Xuat bang tu Excel qua CAD - ETC                     ║")
(princ "\n║  E44 - Cong tac Frame (Y/N)                                 ║")
(princ "\n║                                                              ║")
(princ "\n║ === THIET LAP ===                                            ║")
(princ "\n║  E0  - Mo bang thiet lap                                    ║")
(princ "\n╚══════════════════════════════════════════════════════════════╝")
(princ "\n*** File da duoc load thanh cong! ***")
(princ "\nExcel phai duoc mo truoc khi su dung cac lenh.")
(princ)
