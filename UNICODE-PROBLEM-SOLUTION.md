# VẤN ĐỀ UNICODE TRONG AUTOCAD MTEXT

**Ng<PERSON>y phát hiện**: 19/12/2024  
**Vấn đề**: Font Arial không hỗ trợ đầy đủ Unicode tiếng Việt  
**Giải pháp**: Xử lý Unicode escape sequences

## 🚨 **MÔ TẢ VẤN ĐỀ**

### Hiện tượng
- **Gõ**: "Tổng" → Hiển thị bình thường trong AutoCAD
- **Contents**: "T\U+1ED4NG" → AutoCAD lưu dưới dạng Unicode escape
- **GetText**: "T" → Chỉ lấy ký tự đầu tiên, bỏ qua phần \U+

### Ví dụ cụ thể
```
Input trong AutoCAD: "Tổng"
Contents property:   "T\U+1ED4NG"
GetText result:      "T"
Mong muốn:          "Tổng"
```

```
Input trong AutoCAD: "Số lượng"  
Contents property:   "S\U+1ED1 l\U+01B0\U+1EE3ng"
GetText result:      "S"
Mong muốn:          "Số lượng"
```

## 🔍 **NGUYÊN NHÂN**

### 1. **Font Arial và Unicode**
- Font Arial không hỗ trợ đầy đủ ký tự tiếng Việt
- AutoCAD phải sử dụng Unicode escape sequences
- Format: `\U+[HEX_CODE]` cho mỗi ký tự có dấu

### 2. **Cơ chế AutoCAD**
- **Hiển thị**: AutoCAD render đúng ký tự từ Unicode
- **Lưu trữ**: Contents property chứa escape sequences
- **GetText**: Dừng tại ký tự `\`, không parse Unicode

### 3. **Các ký tự bị ảnh hưởng**
```
ố → \U+1ED1    ợ → \U+1EE3    ư → \U+01B0
Ổ → \U+1ED4    Ơ → \U+01A0    Ư → \U+01AF
ả → \U+1EA3    ạ → \U+1EA1    đ → \U+0111
ẻ → \U+1EBB    ẹ → \U+1EB9    Đ → \U+0110
```

## 🛠️ **GIẢI PHÁP**

### 1. **Phát hiện Unicode escape**
```autolisp
(setq has-unicode (vl-string-search "\\U+" text-string))
```

### 2. **Bảng chuyển đổi**
```autolisp
(setq unicode-table (list
    (list "\\U+1ED4" "Ổ")  ; O circumflex hook above
    (list "\\U+1ED1" "ố")  ; o circumflex acute
    (list "\\U+1EE3" "ợ")  ; o horn dot below
    (list "\\U+01B0" "ư")  ; u with horn
    ; ... thêm các ký tự khác
))
```

### 3. **Thuật toán chuyển đổi**
```autolisp
(foreach unicode-pair unicode-table
    (setq escape-seq (nth 0 unicode-pair))
    (setq vietnamese-char (nth 1 unicode-pair))
    (while (vl-string-search escape-seq result)
        (setq result (vl-string-subst vietnamese-char escape-seq result))
    )
)
```

### 4. **Tích hợp vào CLEAN-TABLE-TEXT**
```autolisp
; Bước 1: Xử lý Unicode escape sequences
(if (vl-string-search "\\U+" result)
    (setq result (CONVERT-UNICODE-ESCAPES result))
)

; Bước 2: Xử lý format codes bình thường
; Bước 3: Xử lý ký hiệu đặc biệt
; Bước 4: Trim
```

## 📊 **KẾT QUẢ SAU KHI SỬA**

| Input Contents | Trước | Sau |
|----------------|-------|-----|
| `T\U+1ED4NG` | `T` ❌ | `Tổng` ✅ |
| `S\U+1ED1 l\U+01B0\U+1EE3ng` | `S` ❌ | `Số lượng` ✅ |
| `\U+0110\U+1EA6U \U+0110O pH` | `\` ❌ | `ĐẦU ĐO pH` ✅ |
| `ATTRIBUTE` | `ATTRIBUTE` ✅ | `ATTRIBUTE` ✅ |

## 🔄 **CÁCH TEST**

### 1. **Test với file riêng**
```
Command: APPLOAD
[Chọn UNICODE-FIX.lsp]

Command: TEST-UNICODE
```

### 2. **Test với input thực tế**
```
Command: TEST-UNICODE-INPUT
Nhap text co Unicode escape: T\U+1ED4NG
```

### 3. **Test với lệnh TE**
```
Command: APPLOAD
[Chọn TE-TableExport.lsp đã cập nhật]

Command: TE
[Chọn table có ký tự tiếng Việt]
```

## 🎯 **PHÒNG NGỪA**

### 1. **Khuyến nghị font**
- **Tránh**: Arial (không hỗ trợ đầy đủ Unicode tiếng Việt)
- **Dùng**: Times New Roman, Calibri, hoặc font Unicode đầy đủ

### 2. **Cài đặt AutoCAD**
- Sử dụng font hỗ trợ Unicode đầy đủ
- Kiểm tra encoding khi import/export

### 3. **Mở rộng bảng Unicode**
- Thêm các ký tự mới khi gặp
- Cập nhật `INIT-UNICODE-TABLE` định kỳ

## 📁 **FILES LIÊN QUAN**

- **`UNICODE-FIX.lsp`** - File test và nghiên cứu
- **`TE-TableExport.lsp`** - Đã tích hợp xử lý Unicode
- **`Z-Cad2Excel-E1-ET.lsp`** - Đã tích hợp xử lý Unicode

## 🎉 **KẾT LUẬN**

✅ **Đã giải quyết** vấn đề Unicode escape sequences  
✅ **Tương thích ngược** với format codes cũ  
✅ **Hiệu suất tốt** - chỉ xử lý khi cần  
✅ **Dễ mở rộng** - thêm ký tự mới dễ dàng  

---

*Vấn đề được phát hiện và giải quyết nhờ phân tích chi tiết của người dùng*
