-------------------------------------------------------------------------------------------------------------------
; CAD to Excel - Tong hop 4 lenh E1, E2, E3, E4
; E1: Copy text (khong vien)
; E2: Copy text (co vien)
; E3: Copy text + nhay chuot 3 dong
; E4: Copy text + nhay chuot tuy chinh
; Tac gia: Toi uu hoa va tich hop
-------------------------------------------------------------------------------------------------------------------

; Ham chung cho tat ca cac lenh E1, E2, E3, E4
(defun CAD-TO-EXCEL-MAIN ( mode jump-rows /
	ActDoc
	*Space*
	otcontents
	lpxlist
	lpylist
	textlist
	blocklist
	linelist
	colwidths
	rowheights
	tinfo
	binfo
	xlapp
	xlbooks
	xlbook
	xlsheets
	xlsheet
	xlcells
	xlselection
	startrow
	startcol
	numrows
	numcols
	endrow
	endcol
	newrow
	celladdress
	targetrange
	tablerange
	ecol
	urange
	user-input
	oerror)

	-------------------------------------------------------------------------------------------------------------------

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(setvar 'nomutt 0)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Xu ly input cho E4 (nhap so dong tuy chinh)
	(if (= mode "E4")
		(progn
			(or *E4-jump-rows* (setq *E4-jump-rows* 1))
			(initget 6)
			(setq user-input (getint (strcat "\nNhap so dong can nhay xuong <" (itoa *E4-jump-rows*) ">: ")))
			(if user-input
				(setq *E4-jump-rows* user-input jump-rows user-input)
				(setq jump-rows *E4-jump-rows*)
			)
			(princ (strcat "\nSo dong se nhay: " (itoa jump-rows)))
		)
	)

	; Chon cac doi tuong
	(princ "\nChon cac doi tuong tao thanh bang (Line, Polyline, Text, MText): ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Xu ly thong tin
			(tableinfo otcontents)
			(setq lpxlist (vl-sort lpxlist '<) lpylist (vl-sort lpylist '>))
			(mapcar '(lambda (txt)(gettxtinfo (entget txt))) textlist)
			(mapcar '(lambda (blk)(getblockinfo blk)) blocklist)
			(setq colwidths (mapcar '(lambda (x)(- (nth (1+ (vl-position x lpxlist)) lpxlist) x))(reverse (cdr (reverse lpxlist))))
				rowheights (mapcar '(lambda (x)(- x (nth (1+ (vl-position x lpylist)) lpylist)))(reverse(cdr (reverse lpylist)))))

			; Kiem tra Excel co dang mo khong va lay vi tri con tro
			(if (setq xlapp (vlax-get-object "Excel.Application"))
				(progn
					; Su dung Excel dang mo va lay vi tri con tro hien tai
					(setq xlsheet (vlax-get-property xlapp "ActiveSheet")
						xlcells (vlax-get-property xlsheet 'Cells)
						xlselection (vlax-get-property xlapp "Selection")
						startrow (vlax-get-property xlselection "Row")
						startcol (vlax-get-property xlselection "Column"))
					(vlax-put-property xlapp "Visible" :vlax-true)
				)
				(progn
					; Tao Excel moi
					(setq xlapp (vlax-get-or-create-object "Excel.Application")
						xlbooks (vlax-get-property xlapp 'Workbooks)
						xlbook (vlax-invoke-method xlbooks 'Add)
						xlsheets (vlax-get-property xlbook 'Sheets)
						xlsheet (vlax-get-property xlsheets 'Item 1)
						xlcells (vlax-get-property xlsheet 'Cells)
						startrow 1
						startcol 1)
					(vlax-put-property xlapp "Visible" :vlax-true)
					(vlax-invoke-method xlsheet "Activate")
				)
			)

			(setq ecol (conexcelcolumn))

			; Dat text vao Excel tu vi tri con tro
			(mapcar '(lambda (x / r c)
					(setq r (+ startrow (cadr (assoc "Position" x)))
						  c (+ startcol (caddr (assoc "Position" x))))
					(setcelltext xlcells r c (cdr (assoc "Content" x)))
				)
				tinfo
			)

			; Dat thong tin block vao Excel tu vi tri con tro
			(mapcar '(lambda (x / r c bstring)
					(setq r (+ startrow (cadr (assoc "Position" x)))
						  c (+ startcol (caddr (assoc "Position" x))))
					(setq bstring "")
					(if (cdr (assoc "Attributes" x))
						(progn
						(mapcar
						'(lambda (y )
						(setq bstring (strcat ":"(cdr y) bstring)))
						(cdr (assoc "Attributes" x)))
						(setcelltext xlcells r c (strcat "Block:"(cdr (assoc "Name" x)) bstring))
						)
					)
				)
				binfo
			)

			; Xu ly theo mode
			(cond
				; E2: Ve vien cho bang
				((= mode "E2")
					(if (and tinfo (> (length lpxlist) 1) (> (length lpylist) 1))
						(progn
							(setq numrows (- (length lpylist) 1)
								  numcols (- (length lpxlist) 1)
								  endrow (+ startrow numrows -1)
								  endcol (+ startcol numcols -1))
							(setq tablerange (vlax-get-property xlsheet "Range"
								(strcat (nth (- startcol 1) ecol) (itoa startrow) ":"
										(nth (- endcol 1) ecol) (itoa endrow))))
							(vlax-put-property tablerange 'HorizontalAlignment -4108)
							(setgridlines xlapp tablerange)
						)
					)
				)

				; E3 va E4: Nhay chuot
				((or (= mode "E3") (= mode "E4"))
					; Tinh kich thuoc bang va vi tri cuoi
					(if (and tinfo (> (length lpxlist) 1) (> (length lpylist) 1))
						(progn
							(setq numrows (- (length lpylist) 1)
								  numcols (- (length lpxlist) 1)
								  endrow (+ startrow numrows -1))
							; Tinh vi tri moi cho con tro
							(setq newrow (+ endrow jump-rows))
						)
						(progn
							; Neu khong co du lieu bang
							(setq newrow (+ startrow jump-rows))
						)
					)

					; Tu dong nhay con tro Excel den vi tri moi
					(vl-catch-all-apply '(lambda ()
						(setq celladdress (strcat (nth (- startcol 1) ecol) (itoa newrow)))
						(setq targetrange (vlax-get-property xlsheet "Range" celladdress))
						(vlax-invoke-method targetrange "Select")
						(vlax-put-property xlapp "ActiveCell" targetrange)
						(vlax-release-object targetrange)
					))

					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " celladdress))
				)

				; E1: Khong lam gi them
				(T
					(princ "\nHoan thanh!")
				)
			)
		)
		(princ "\nKhong chon duoc doi tuong nao!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

-------------------------------------------------------------------------------------------------------------------
; E1 - Copy text (khong vien)
(defun C:E1 ( / )
	(CAD-TO-EXCEL-MAIN "E1" 0)
)

-------------------------------------------------------------------------------------------------------------------
; E2 - Copy text (co vien)
(defun C:E2 ( / )
	(CAD-TO-EXCEL-MAIN "E2" 0)
)

-------------------------------------------------------------------------------------------------------------------
; E3 - Copy text + nhay chuot 3 dong
(defun C:E3 ( / )
	(CAD-TO-EXCEL-MAIN "E3" 3)
)

-------------------------------------------------------------------------------------------------------------------
; E4 - Copy text + nhay chuot tuy chinh
(defun C:E4 ( / )
	(CAD-TO-EXCEL-MAIN "E4" 0)
)

-------------------------------------------------------------------------------------------------------------------
; E5 - Xuat text theo cot/hang/o
(defun C:E5 ( /
	ActDoc
	*Space*
	otcontents
	textlist
	sortedtextlist
	xlapp
	xlbooks
	xlbook
	xlsheets
	xlsheet
	xlcells
	xlselection
	startrow
	startcol
	ecol
	mode
	separator
	result-text
	newrow
	newcol
	celladdress
	targetrange
	heso
	use-formula
	oerror)

	-------------------------------------------------------------------------------------------------------------------

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(setvar 'nomutt 0)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Chon cac text/mtext/dimension
	(princ "\nChon cac text/mtext/dimension (click hoac quet): ")
	(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

	(if otcontents
		(progn
			; Chuyen selection set thanh list text
			(setq textlist (E5-CONVERT-SS-TO-TEXTLIST otcontents))
			; Luu textlist voi toa do cho che do MANG
			(setq *E5-textlist-with-pos* textlist)

			; Hien menu chon che do
			(setq mode (E5-SHOW-MODE-MENU))

			(if mode
				(progn
					; Sap xep text theo che do
					(setq sortedtextlist (E5-SORT-TEXT-LIST textlist mode))

					; Xu ly separator cho che do "O"
					(if (= mode "O")
						(progn
							(or *E5-separator* (setq *E5-separator* "+"))
							(setq separator (getstring (strcat "\nNhap ky tu noi text <" *E5-separator* ">: ")))
							(if (= separator "")
								(setq separator *E5-separator*)
								(setq *E5-separator* separator)
							)


							; Neu separator la "+", yeu cau nhap he so
							(if (= separator "+")
								(progn
									(or *E5-heso* (setq *E5-heso* "1"))
									(setq heso (getstring (strcat "\nNhap he so <" *E5-heso* ">: ")))
									(if (= heso "")
										(setq heso *E5-heso*)
										(setq *E5-heso* heso)
									)

									(setq use-formula T)
								)
								(setq use-formula nil)
							)
						)
					)

					; Kiem tra Excel co dang mo khong
					(if (setq xlapp (vlax-get-object "Excel.Application"))
						(progn
							; Su dung Excel dang mo
							(setq xlsheet (vlax-get-property xlapp "ActiveSheet")
								xlcells (vlax-get-property xlsheet 'Cells)
								xlselection (vlax-get-property xlapp "Selection")
								startrow (vlax-get-property xlselection "Row")
								startcol (vlax-get-property xlselection "Column"))
							(vlax-put-property xlapp "Visible" :vlax-true)
						)
						(progn
							; Tao Excel moi
							(setq xlapp (vlax-get-or-create-object "Excel.Application")
								xlbooks (vlax-get-property xlapp 'Workbooks)
								xlbook (vlax-invoke-method xlbooks 'Add)
								xlsheets (vlax-get-property xlbook 'Sheets)
								xlsheet (vlax-get-property xlsheets 'Item 1)
								xlcells (vlax-get-property xlsheet 'Cells)
								startrow 1
								startcol 1)
							(vlax-put-property xlapp "Visible" :vlax-true)
							(vlax-invoke-method xlsheet "Activate")
						)
					)

					(setq ecol (conexcelcolumn))

					; Xuat text theo che do
					(cond
						; Che do COT: xuat xuong duoi
						((= mode "COT")
							(setq newrow startrow)
							(foreach txt sortedtextlist
								(setcelltext xlcells newrow startcol txt)
								(setq newrow (+ newrow 1))
							)
							(setq newrow newrow newcol startcol)
						)

						; Che do HANG: xuat sang phai
						((= mode "HANG")
							(setq newcol startcol)
							(foreach txt sortedtextlist
								(setcelltext xlcells startrow newcol txt)
								(setq newcol (+ newcol 1))
							)
							(setq newrow startrow newcol newcol)
						)

						; Che do O: noi text vao 1 o
						((= mode "O")
							(if use-formula
								; Tao cong thuc Excel
								(progn
									(setq result-text (E5-CREATE-FORMULA sortedtextlist heso))
									(setcelltext xlcells startrow startcol result-text)
								)
								; Noi text binh thuong
								(progn
									(setq result-text (E5-JOIN-TEXT sortedtextlist separator))
									(setcelltext xlcells startrow startcol result-text)
								)
							)
							(setq newrow (+ startrow 1) newcol startcol)
						)

						; Che do MANG: xuat theo mang
						((= mode "MANG")
							(setq newrow startrow newcol startcol)
							(E5-EXPORT-AS-ARRAY sortedtextlist xlcells startrow startcol)
							(setq newrow (+ startrow (E5-GET-ARRAY-ROWS sortedtextlist)) newcol startcol)
						)
					)

					; Tu dong nhay con tro Excel den vi tri moi
					(vl-catch-all-apply '(lambda ()
						(setq celladdress (strcat (nth (- newcol 1) ecol) (itoa newrow)))
						(setq targetrange (vlax-get-property xlsheet "Range" celladdress))
						(vlax-invoke-method targetrange "Select")
						(vlax-put-property xlapp "ActiveCell" targetrange)
						(vlax-release-object targetrange)
					))

					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " celladdress))
				)
				(princ "\nDa huy lenh!")
			)
		)
		(princ "\nKhong chon duoc text/mtext nao!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

-------------------------------------------------------------------------------------------------------------------
; E6 - Ghi handle cac doi tuong vao comment Excel
(defun C:E6 ( /
	ActDoc
	*Space*
	otcontents
	xlapp
	xlsheet
	xlcells
	xlselection
	currentcell
	comment
	dwgpath
	dwgname
	handlelist
	commenttext
	oerror)

	-------------------------------------------------------------------------------------------------------------------

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(setvar 'nomutt 0)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Kiem tra Excel co dang mo khong
	(if (setq xlapp (vlax-get-object "Excel.Application"))
		(progn
			; Su dung Excel dang mo
			(setq xlsheet (vlax-get-property xlapp "ActiveSheet")
				xlcells (vlax-get-property xlsheet 'Cells)
				xlselection (vlax-get-property xlapp "Selection")
				currentcell (vlax-get-property xlapp "ActiveCell"))
			(vlax-put-property xlapp "Visible" :vlax-true)
		)
		(progn
			(princ "\nLoi: Khong tim thay Excel dang mo!")
			(princ "\nVui long mo Excel truoc khi chay lenh E6")
			(exit)
		)
	)

	; Chon cac doi tuong trong CAD
	(princ "\nChon cac doi tuong de ghi handle vao comment Excel: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Lay duong dan file CAD hien tai
			(setq dwgpath (vla-get-fullname ActDoc))
			(setq dwgname (vl-filename-base dwgpath))

			; Lay handle cua tat ca doi tuong
			(setq handlelist (E6-GET-HANDLES otcontents))

			; Tao noi dung comment
			(setq commenttext (E6-CREATE-COMMENT-TEXT handlelist dwgpath dwgname))

			; Ghi comment vao cell Excel
			(E6-WRITE-COMMENT-TO-CELL currentcell commenttext)

			(princ (strcat "\nHoan thanh! Da ghi " (itoa (length handlelist)) " handle vao comment"))
		)
		(princ "\nKhong chon duoc doi tuong nao!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

-------------------------------------------------------------------------------------------------------------------
; E7 - Ghi text/dim vao o va handle vao comment dong thoi
(defun C:E7 ( /
	ActDoc
	*Space*
	otcontents
	xlapp
	xlsheet
	xlcells
	xlselection
	currentcell
	dwgpath
	dwgname
	handlelist
	commenttext
	celltext
	oerror)

	-------------------------------------------------------------------------------------------------------------------

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(setvar 'nomutt 0)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Kiem tra Excel co dang mo khong
	(if (setq xlapp (vlax-get-object "Excel.Application"))
		(progn
			(setq xlsheet (vlax-get-property xlapp "ActiveSheet")
				xlcells (vlax-get-property xlsheet 'Cells)
				xlselection (vlax-get-property xlapp "Selection")
				currentcell (vlax-get-property xlapp "ActiveCell"))
			(vlax-put-property xlapp "Visible" :vlax-true)
		)
		(progn
			(princ "\nLoi: Khong tim thay Excel dang mo!")
			(princ "\nVui long mo Excel truoc khi chay lenh E7")
			(exit)
		)
	)

	; Chon cac doi tuong trong CAD
	(princ "\nChon cac text/mtext/dimension: ")
	(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

	(if otcontents
		(progn
			(setq dwgpath (vla-get-fullname ActDoc)
				dwgname (vl-filename-base dwgpath))

			; Xu ly noi dung cell va handle
			(setq celltext (E7-CREATE-CELL-TEXT otcontents))
			(setq handlelist (E6-GET-HANDLES otcontents))
			(setq commenttext (E6-CREATE-COMMENT-TEXT handlelist dwgpath dwgname))

			; Ghi vao Excel
			(E7-WRITE-TO-EXCEL currentcell celltext commenttext)

			(princ (strcat "\nHoan thanh! Da ghi " (itoa (sslength otcontents)) " doi tuong"))
		)
		(princ "\nKhong chon duoc doi tuong nao!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

-------------------------------------------------------------------------------------------------------------------
; Ham tao noi dung cell cho E7 - su dung thiet lap ET
(defun E7-CREATE-CELL-TEXT ( ss / n ent entobj objname txtcontent measurement textlist dimlist result)
	(setq n 0 textlist '() dimlist '())

	; Phan loai text va dimension
	(repeat (sslength ss)
		(setq ent (ssname ss n)
			  entobj (vlax-ename->vla-object ent)
			  objname (vla-get-objectname entobj))

		(cond
			; TEXT va MTEXT
			((or (= objname "AcDbText") (= objname "AcDbMText"))
				(setq txtcontent (vla-get-textstring entobj))
				; Xu ly MText - loai bo format
				(if (= objname "AcDbMText")
					(progn
						(setq txtcontent (vl-string-subst "" "\\P" txtcontent))
						(setq txtcontent (vl-string-subst "" "\\p" txtcontent))
						(while (vl-string-search "\\" txtcontent)
							(setq txtcontent (vl-string-subst "" (substr txtcontent (vl-string-search "\\" txtcontent) 2) txtcontent))
						)
					)
				)
				(setq textlist (append textlist (list txtcontent)))
			)
			; DIMENSION
			((or (= objname "AcDbAlignedDimension")
				 (= objname "AcDbRotatedDimension")
				 (= objname "AcDbRadialDimension")
				 (= objname "AcDbDiametricDimension")
				 (= objname "AcDbAngularDimension")
				 (wcmatch objname "*Dimension*"))
				(setq measurement (vla-get-measurement entobj))
				(setq dimlist (append dimlist (list (itoa (fix (+ measurement 0.5))))))
			)
		)
		(setq n (+ n 1))
	)

	; Tao ket qua su dung thiet lap ET
	(setq result "")

	; Xu ly text - noi bang symbol tu thiet lap
	(if textlist
		(progn
			(setq result (car textlist))
			(foreach txt (cdr textlist)
				(setq result (strcat result *ET-symbol* txt))
			)
		)
	)

	; Xu ly dimension
	(if dimlist
		(progn
			; Neu da co text, them symbol
			(if (> (strlen result) 0)
				(setq result (strcat result *ET-symbol*))
			)
			; Neu symbol la "+", tao cong thuc voi factor
			(if (= *ET-symbol* "+")
				(progn
					(setq result (strcat result "=(" (car dimlist)))
					(foreach dim (cdr dimlist)
						(setq result (strcat result "+" dim))
					)
					(setq result (strcat result ")*" *ET-factor*))
				)
				; Neu khong phai "+", noi binh thuong
				(progn
					(setq result (strcat result (car dimlist)))
					(foreach dim (cdr dimlist)
						(setq result (strcat result *ET-symbol* dim))
					)
				)
			)
		)
	)

	result
)

-------------------------------------------------------------------------------------------------------------------
; Ham ghi vao Excel cho E7
(defun E7-WRITE-TO-EXCEL ( cell celltext commenttext / result cellrange worksheet targetrange)
	; Ghi noi dung vao cell
	(if (and celltext (> (strlen celltext) 0))
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				; Thu cach 1: Su dung Value2
				(vlax-put-property cell "Value2" celltext)
			)))
			(if (vl-catch-all-error-p result)
				(progn
					; Thu cach 2: Su dung Formula
					(setq result (vl-catch-all-apply '(lambda ()
						(vlax-put-property cell "Formula" celltext)
					)))
					(if (vl-catch-all-error-p result)
						(progn
							; Thu cach 3: Su dung Range
							(setq result (vl-catch-all-apply '(lambda ()
								(setq cellrange (vlax-get-property cell "Address"))
								(setq worksheet (vlax-get-property cell "Worksheet"))
								(setq targetrange (vlax-get-property worksheet "Range" cellrange))
								(vlax-put-property targetrange "Value" celltext)
							)))
						)
					)
				)
			)
		)
	)

	; Ghi comment
	(if (and commenttext (> (strlen commenttext) 0))
		(progn
			(setq result (vl-catch-all-apply '(lambda ()
				(E6-WRITE-COMMENT-TO-CELL cell commenttext)
			)))
		)
	)
)

-------------------------------------------------------------------------------------------------------------------
; ET - Lenh tong hop toi uu (thay the E1-E7)
(defun C:ET ( /
	ActDoc
	*Space*
	otcontents
	xlapp
	xlsheet
	xlcells
	xlselection
	currentcell
	startrow
	startcol
	mode
	option
	result
	oerror)

	-------------------------------------------------------------------------------------------------------------------

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(setvar 'nomutt 0)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Khoi tao cac bien global mac dinh
	(ET-INIT-DEFAULTS)

	; Kiem tra Excel
	(if (not (ET-CHECK-EXCEL))
		(exit)
	)

	; Chon doi tuong
	(princ "\nChon cac text/mtext/dimension: ")
	(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

	(if otcontents
		(progn
			; Hien thi menu chinh
			(setq mode (ET-SHOW-MAIN-MENU))

			; Xu ly theo mode
			(if mode
				(progn
					; Hien thi menu thiet lap
					(ET-SHOW-SETTINGS-MENU)

					; Thuc hien xuat du lieu
					(ET-EXECUTE-MODE mode otcontents)
				)
				(princ "\nDa huy lenh!")
			)
		)
		(princ "\nKhong chon duoc doi tuong nao!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

-------------------------------------------------------------------------------------------------------------------
; Ham khoi tao cac gia tri mac dinh
(defun ET-INIT-DEFAULTS ( / )
	; Khoi tao cac bien global neu chua co
	(or *ET-jump* (setq *ET-jump* 0))
	(or *ET-symbol* (setq *ET-symbol* ";"))
	(or *ET-factor* (setq *ET-factor* "1"))
	(or *ET-mode* (setq *ET-mode* "Value")) ; Mac dinh la Value (E7)
	(or *ET-tolerance* (setq *ET-tolerance* 50.0)) ; Sai so cho phep cho mang
)

-------------------------------------------------------------------------------------------------------------------
; Ham kiem tra Excel
(defun ET-CHECK-EXCEL ( / )
	(if (setq xlapp (vlax-get-object "Excel.Application"))
		(progn
			(setq xlsheet (vlax-get-property xlapp "ActiveSheet")
				xlcells (vlax-get-property xlsheet 'Cells)
				xlselection (vlax-get-property xlapp "Selection")
				currentcell (vlax-get-property xlapp "ActiveCell")
				startrow (vlax-get-property xlselection "Row")
				startcol (vlax-get-property xlselection "Column"))
			(vlax-put-property xlapp "Visible" :vlax-true)
			T
		)
		(progn
			(princ "\nLoi: Khong tim thay Excel dang mo!")
			(princ "\nVui long mo Excel truoc khi chay lenh ET")
			nil
		)
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham hien thi menu chinh
(defun ET-SHOW-MAIN-MENU ( / choice)
	(princ (strcat "\nXuat du lieu [Table/Col/Row/Cell/Array/Handle/Value] <" *ET-mode* ">: "))
	(initget "Table Col Row Cell Array Handle Value T C R Ce A H V")
	(setq choice (getkword ""))

	; Xu ly lua chon
	(cond
		((or (= choice "Table") (= choice "T")) (setq *ET-mode* "Table"))
		((or (= choice "Col") (= choice "C")) (setq *ET-mode* "Col"))
		((or (= choice "Row") (= choice "R")) (setq *ET-mode* "Row"))
		((or (= choice "Cell") (= choice "Ce")) (setq *ET-mode* "Cell"))
		((or (= choice "Array") (= choice "A")) (setq *ET-mode* "Array"))
		((or (= choice "Handle") (= choice "H")) (setq *ET-mode* "Handle"))
		((or (= choice "Value") (= choice "V")) (setq *ET-mode* "Value"))
		((= choice nil) *ET-mode*) ; Su dung gia tri mac dinh
		(T nil) ; Huy lenh
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham hien thi menu thiet lap
(defun ET-SHOW-SETTINGS-MENU ( / choice input continue)
	(setq continue T)
	(while continue
		(princ (strcat "\nCac thiet lap [Jump/Symbol/Factor/Tolerance] <tiep tuc>: "))
		(initget "Jump Symbol Factor Tolerance J S F T")
		(setq choice (getkword ""))

		(cond
			; Jump - So dong nhay
			((or (= choice "Jump") (= choice "J"))
				(setq input (getint (strcat "\nNhap so dong nhay <" (itoa *ET-jump*) ">: ")))
				(if input (setq *ET-jump* input))
				(princ (strcat "\nSo dong nhay: " (itoa *ET-jump*)))
			)
			; Symbol - Ky tu noi
			((or (= choice "Symbol") (= choice "S"))
				(setq input (getstring (strcat "\nNhap ky tu noi <" *ET-symbol* ">: ")))
				(if (and input (> (strlen input) 0)) (setq *ET-symbol* input))
				(princ (strcat "\nKy tu noi: \"" *ET-symbol* "\""))
			)
			; Factor - He so
			((or (= choice "Factor") (= choice "F"))
				(setq input (getstring (strcat "\nNhap he so <" *ET-factor* ">: ")))
				(if (and input (> (strlen input) 0)) (setq *ET-factor* input))
				(princ (strcat "\nHe so: " *ET-factor*))
			)
			; Tolerance - Sai so cho phep cho mang
			((or (= choice "Tolerance") (= choice "T"))
				(setq input (getreal (strcat "\nNhap sai so cho phep cho mang <" (rtos *ET-tolerance* 2 1) ">: ")))
				(if input (setq *ET-tolerance* input))
				(princ (strcat "\nSai so cho phep: " (rtos *ET-tolerance* 2 1)))
			)
			; Tiep tuc hoac Enter
			((= choice nil) (setq continue nil))
			; Khac
			(T (princ "\nLua chon khong hop le!"))
		)
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham thuc hien theo mode
(defun ET-EXECUTE-MODE ( mode ss / )
	(cond
		; Table - tuong duong E1
		((= mode "Table")
			(ET-EXECUTE-TABLE ss)
		)
		; Col - tuong duong E5-1
		((= mode "Col")
			(ET-EXECUTE-COL ss)
		)
		; Row - tuong duong E5-2
		((= mode "Row")
			(ET-EXECUTE-ROW ss)
		)
		; Cell - tuong duong E5-3
		((= mode "Cell")
			(ET-EXECUTE-CELL ss)
		)
		; Array - tuong duong E5-4
		((= mode "Array")
			(ET-EXECUTE-ARRAY ss)
		)
		; Handle - tuong duong E6
		((= mode "Handle")
			(ET-EXECUTE-HANDLE ss)
		)
		; Value - tuong duong E7
		((= mode "Value")
			(ET-EXECUTE-VALUE ss)
		)
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham thuc hien Table mode (E1)
(defun ET-EXECUTE-TABLE ( ss / textlist newrow newcol)
	; Chuyen doi selection set thanh text list
	(setq textlist (E5-CONVERT-SS-TO-TEXTLIST ss))

	; Xuat text theo hang ngang
	(setq newcol startcol)
	(foreach txt textlist
		(setcelltext xlcells startrow newcol (car txt))
		(setq newcol (+ newcol 1))
	)

	; Nhay chuot
	(setq newrow (+ startrow *ET-jump*) newcol newcol)
	(ET-MOVE-CURSOR newrow newcol)
)

-------------------------------------------------------------------------------------------------------------------
; Ham thuc hien Col mode (E5-1)
(defun ET-EXECUTE-COL ( ss / textlist sortedtextlist newrow newcol ecol celladdress targetrange)
	; Chuyen doi va sap xep
	(setq textlist (E5-CONVERT-SS-TO-TEXTLIST ss))
	(setq sortedtextlist (ET-SORT-FOR-COL textlist))

	; Xuat theo cot
	(setq newrow startrow)
	(foreach txt sortedtextlist
		(setcelltext xlcells newrow startcol (car txt))
		(setq newrow (+ newrow 1))
	)

	; Nhay chuot
	(setq newrow (+ newrow *ET-jump*) newcol startcol)
	(ET-MOVE-CURSOR newrow newcol)
)

-------------------------------------------------------------------------------------------------------------------
; Ham thuc hien Row mode (E5-2)
(defun ET-EXECUTE-ROW ( ss / textlist sortedtextlist newrow newcol)
	(setq textlist (E5-CONVERT-SS-TO-TEXTLIST ss))
	(setq sortedtextlist (ET-SORT-FOR-ROW textlist))

	; Xuat theo hang
	(setq newcol startcol)
	(foreach txt sortedtextlist
		(setcelltext xlcells startrow newcol (car txt))
		(setq newcol (+ newcol 1))
	)

	; Nhay chuot
	(setq newrow (+ startrow *ET-jump*) newcol newcol)
	(ET-MOVE-CURSOR newrow newcol)
)

-------------------------------------------------------------------------------------------------------------------
; Ham thuc hien Cell mode (E5-3)
(defun ET-EXECUTE-CELL ( ss / textlist result-text newrow newcol)
	(setq textlist (E5-CONVERT-SS-TO-TEXTLIST ss))

	; Noi text bang symbol hoac tao cong thuc
	(if (= *ET-symbol* "+")
		; Tao cong thuc voi factor
		(setq result-text (ET-CREATE-FORMULA-WITH-FACTOR textlist *ET-factor*))
		; Noi text binh thuong
		(setq result-text (ET-JOIN-TEXT-WITH-SYMBOL textlist *ET-symbol*))
	)

	; Ghi vao cell
	(setcelltext xlcells startrow startcol result-text)

	; Nhay chuot
	(setq newrow (+ startrow 1 *ET-jump*) newcol startcol)
	(ET-MOVE-CURSOR newrow newcol)
)

-------------------------------------------------------------------------------------------------------------------
; Ham thuc hien Array mode (E5-4)
(defun ET-EXECUTE-ARRAY ( ss / textlist sorted-textlist unique-rows unique-cols current-row current-col txt-item txt-pos txt-content maxrow)
	(setq textlist (E5-CONVERT-SS-TO-TEXTLIST ss))

	; Luu textlist voi toa do cho che do MANG (giong nhu E5)
	(setq *E5-textlist-with-pos* textlist)

	; Su dung ham E5-EXPORT-AS-ARRAY da hoat dong tot
	(E5-EXPORT-AS-ARRAY textlist xlcells startrow startcol)

	; Tinh so hang da xuat
	(setq maxrow (+ startrow (E5-GET-ARRAY-ROWS textlist)))

	; Nhay chuot
	(setq newrow (+ maxrow *ET-jump*))
	(ET-MOVE-CURSOR newrow startcol)
)

-------------------------------------------------------------------------------------------------------------------
; Ham thuc hien Handle mode (E6)
(defun ET-EXECUTE-HANDLE ( ss / handlelist commenttext dwgpath dwgname)
	(setq dwgpath (vla-get-fullname ActDoc)
		  dwgname (vl-filename-base dwgpath))
	(setq handlelist (E6-GET-HANDLES ss))
	(setq commenttext (E6-CREATE-COMMENT-TEXT handlelist dwgpath dwgname))

	; Ghi comment
	(E6-WRITE-COMMENT-TO-CELL currentcell commenttext)

	; Nhay chuot
	(ET-MOVE-CURSOR (+ startrow *ET-jump*) startcol)
)

-------------------------------------------------------------------------------------------------------------------
; Ham thuc hien Value mode (E7)
(defun ET-EXECUTE-VALUE ( ss / celltext handlelist commenttext dwgpath dwgname)
	(setq dwgpath (vla-get-fullname ActDoc)
		  dwgname (vl-filename-base dwgpath))

	; Tao noi dung cell va comment
	(setq celltext (E7-CREATE-CELL-TEXT ss))
	(setq handlelist (E6-GET-HANDLES ss))
	(setq commenttext (E6-CREATE-COMMENT-TEXT handlelist dwgpath dwgname))

	; Ghi vao Excel
	(E7-WRITE-TO-EXCEL currentcell celltext commenttext)

	; Nhay chuot
	(ET-MOVE-CURSOR (+ startrow *ET-jump*) startcol)
)

-------------------------------------------------------------------------------------------------------------------
; Ham ho tro - Di chuyen con tro Excel
(defun ET-MOVE-CURSOR ( row col / targetcell)
	(vl-catch-all-apply '(lambda ()
		(setq targetcell (vlax-get-property xlcells "Item" row col))
		(vlax-invoke-method targetcell "Select")
	))
)

-------------------------------------------------------------------------------------------------------------------
; Ham ho tro - Sap xep cho Col mode
(defun ET-SORT-FOR-COL ( textlist / )
	; Sap xep theo Y giam dan (top to bottom)
	(vl-sort textlist '(lambda (a b) (> (caddr (cadr a)) (caddr (cadr b)))))
)

-------------------------------------------------------------------------------------------------------------------
; Ham ho tro - Sap xep cho Row mode
(defun ET-SORT-FOR-ROW ( textlist / )
	; Sap xep theo X giam dan (right to left)
	(vl-sort textlist '(lambda (a b) (> (cadr (cadr a)) (cadr (cadr b)))))
)

-------------------------------------------------------------------------------------------------------------------
; Ham ho tro - Noi text bang symbol
(defun ET-JOIN-TEXT-WITH-SYMBOL ( textlist symbol / result)
	(setq result "")
	(if textlist
		(progn
			(setq result (car (car textlist)))
			(foreach item (cdr textlist)
				(setq result (strcat result symbol (car item)))
			)
		)
	)
	result
)

-------------------------------------------------------------------------------------------------------------------
; Ham ho tro - Tao cong thuc voi factor
(defun ET-CREATE-FORMULA-WITH-FACTOR ( textlist factor / result)
	(setq result "")
	(if textlist
		(progn
			(setq result (strcat "=(" (car (car textlist))))
			(foreach item (cdr textlist)
				(setq result (strcat result "+" (car item)))
			)
			(setq result (strcat result ")*" factor))
		)
	)
	result
)

-------------------------------------------------------------------------------------------------------------------
; Ham ho tro - Lay so hang cua array
(defun E5-GET-ARRAY-ROWS ( textlist / maxrow minrow)
	(if textlist
		(progn
			(setq maxrow (caddr (cadr (car textlist)))
				  minrow maxrow)
			(foreach item textlist
				(setq maxrow (max maxrow (caddr (cadr item))))
				(setq minrow (min minrow (caddr (cadr item))))
			)
			(fix (/ (- maxrow minrow) 100)) ; Gia su moi dong cach 100 unit
		)
		1
	)
)



-------------------------------------------------------------------------------------------------------------------
; Ham lay handle cua cac doi tuong cho E6
(defun E6-GET-HANDLES ( ss / n ent handlelist entobj handle)
	(setq n 0 handlelist '())
	(repeat (sslength ss)
		(setq ent (ssname ss n))
		(setq entobj (vlax-ename->vla-object ent))
		(setq handle (vla-get-handle entobj))
		(setq handlelist (append handlelist (list handle)))
		(setq n (+ n 1))
	)
	handlelist
)

-------------------------------------------------------------------------------------------------------------------
; Ham tao noi dung comment cho E6
(defun E6-CREATE-COMMENT-TEXT ( handlelist dwgpath dwgname / commenttext filename handletext)
	; Tao phan handle
	(setq handletext "")
	(if handlelist
		(progn
			(setq handletext (car handlelist))
			(foreach handle (cdr handlelist)
				(setq handletext (strcat handletext "; " handle))
			)
			; Them dau ";" vao handle cuoi cung
			(setq handletext (strcat handletext ";"))
		)
	)

	; Tao noi dung comment - khong co khoang trang truoc handle dau
	(setq commenttext (strcat handletext " \nFileCad: " dwgpath))

	commenttext
)

-------------------------------------------------------------------------------------------------------------------
; Ham ghi comment vao cell Excel cho E6 - toi uu
(defun E6-WRITE-COMMENT-TO-CELL ( cell commenttext / comment)
	(vl-catch-all-apply '(lambda ()
		; Xoa comment cu neu co
		(setq comment (vlax-get-property cell "Comment"))
		(if comment (vlax-invoke-method comment "Delete"))

		; Them comment moi don gian
		(if (and commenttext (> (strlen commenttext) 0))
			(vlax-invoke-method cell "AddComment" commenttext)
		)
	))
)

-------------------------------------------------------------------------------------------------------------------
; Ham chuyen selection set thanh list text cho E5 (ho tro dimension)
(defun E5-CONVERT-SS-TO-TEXTLIST ( ss / n ent textlist txtobj txtcontent txtpos objname measurement)
	(setq n 0 textlist '())
	(repeat (sslength ss)
		(setq ent (ssname ss n))
		(setq txtobj (vlax-ename->vla-object ent))
		(setq objname (vla-get-objectname txtobj))

		; Xu ly theo loai doi tuong
		(cond
			; TEXT
			((= objname "AcDbText")
				(setq txtcontent (vla-get-textstring txtobj))
				(setq txtpos (vl-catch-all-apply '(lambda ()
					(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
				)))
				(if (vl-catch-all-error-p txtpos)
					(setq txtpos (list 0.0 0.0 0.0))
				)
			)
			; MTEXT
			((= objname "AcDbMText")
				(setq txtcontent (vla-get-textstring txtobj))
				(setq txtpos (vl-catch-all-apply '(lambda ()
					(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj)))
				)))
				(if (vl-catch-all-error-p txtpos)
					(setq txtpos (list 0.0 0.0 0.0))
				)
				; Loai bo cac ky tu dinh dang MText
				(setq txtcontent (vl-string-subst "" "\\P" txtcontent))
				(setq txtcontent (vl-string-subst "" "\\p" txtcontent))
				(while (vl-string-search "\\" txtcontent)
					(setq txtcontent (vl-string-subst "" (substr txtcontent (vl-string-search "\\" txtcontent) 2) txtcontent))
				)
			)
			; DIMENSION (tat ca cac loai)
			((or (= objname "AcDbAlignedDimension")
				 (= objname "AcDbRotatedDimension")
				 (= objname "AcDbRadialDimension")
				 (= objname "AcDbDiametricDimension")
				 (= objname "AcDbAngularDimension")
				 (wcmatch objname "*Dimension*"))
				; Lay gia tri Measurement va lam tron 0 chu so thap phan
				(setq measurement (vla-get-measurement txtobj))
				(setq txtcontent (itoa (fix (+ measurement 0.5)))) ; Lam tron 0 chu so thap phan
				(setq txtpos (vl-catch-all-apply '(lambda ()
					(vlax-safearray->list (vlax-variant-value (vla-get-textposition txtobj)))
				)))
				(if (vl-catch-all-error-p txtpos)
					(setq txtpos (list 0.0 0.0 0.0))
				)
			)
			; Loai khac - bo qua
			(T
				(setq txtcontent nil txtpos nil)
			)
		)

		; Them vao list neu co noi dung
		(if (and txtcontent txtpos)
			(setq textlist (append textlist (list (list txtcontent txtpos))))
		)
		(setq n (+ n 1))
	)
	textlist
)

-------------------------------------------------------------------------------------------------------------------
; Ham hien menu chon che do cho E5
(defun E5-SHOW-MODE-MENU ( / choice)
	(princ "\n=== CHON CHE DO XUAT ===")
	(princ "\n1. Cot (xuat xuong duoi)")
	(princ "\n2. Hang (xuat sang phai)")
	(princ "\n3. O (noi text vao 1 o)")
	(princ "\n4. Mang (xuat theo mang)")
	(initget "1 2 3 4 Cot Hang O Mang")
	(setq choice (getkword "\nChon che do [1/2/3/4] hoac [Cot/Hang/O/Mang]: "))
	(cond
		((or (= choice "1") (= choice "Cot")) "COT")
		((or (= choice "2") (= choice "Hang")) "HANG")
		((or (= choice "3") (= choice "O")) "O")
		((or (= choice "4") (= choice "Mang")) "MANG")
		(T nil)
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham sap xep text list cho E5 - logic chinh xac
(defun E5-SORT-TEXT-LIST ( textlist mode / tolerance xa ya xb yb)
	; Su dung bien global tolerance hoac gia tri mac dinh
	(setq tolerance (if *ET-tolerance* *ET-tolerance* 50.0))

	(cond
		; Che do COT: tu trai sang phai, cung cot thi tu tren xuong
		((= mode "COT")
			(setq textlist (vl-sort textlist '(lambda (a b)
				(setq xa (car (cadr a)) ya (cadr (cadr a))
					  xb (car (cadr b)) yb (cadr (cadr b)))
				; Neu cung cot (X gan bang nhau), sap xep theo Y (tren xuong)
				(if (< (abs (- xa xb)) tolerance)
					(> ya yb) ; Cung cot: tu tren xuong (Y giam dan)
					(< xa xb) ; Khac cot: tu trai sang phai (X tang dan)
				)
			)))
		)
		; Che do HANG: tu tren xuong, cung hang thi tu trai sang phai
		((= mode "HANG")
			(setq textlist (vl-sort textlist '(lambda (a b)
				(setq xa (car (cadr a)) ya (cadr (cadr a))
					  xb (car (cadr b)) yb (cadr (cadr b)))
				; Neu cung hang (Y gan bang nhau), sap xep theo X (trai phai)
				(if (< (abs (- ya yb)) tolerance)
					(< xa xb) ; Cung hang: tu trai sang phai (X tang dan)
					(> ya yb) ; Khac hang: tu tren xuong (Y giam dan)
				)
			)))
		)
		; Che do O: giu nguyen thu tu chon (khong sap xep)
		((= mode "O")
			textlist
		)
		; Che do MANG: sap xep theo mang (tren xuong truoc, trai phai sau)
		((= mode "MANG")
			(setq textlist (vl-sort textlist '(lambda (a b)
				(setq xa (car (cadr a)) ya (cadr (cadr a))
					  xb (car (cadr b)) yb (cadr (cadr b)))
				; Uu tien Y truoc (tren xuong), neu cung hang thi X (trai phai)
				(if (< (abs (- ya yb)) tolerance)
					(< xa xb) ; Cung hang: tu trai sang phai (X tang dan)
					(> ya yb) ; Khac hang: tu tren xuong (Y giam dan)
				)
			)))
		)
	)
	; Tra ve chi noi dung text, bo toa do
	(mapcar 'car textlist)
)



-------------------------------------------------------------------------------------------------------------------
; Ham noi text cho E5
(defun E5-JOIN-TEXT ( textlist separator / result)
	(setq result "")
	(if textlist
		(progn
			(setq result (car textlist))
			(foreach txt (cdr textlist)
				(setq result (strcat result separator txt))
			)
		)
	)
	result
)

-------------------------------------------------------------------------------------------------------------------
; Ham tao cong thuc Excel cho E5
(defun E5-CREATE-FORMULA ( textlist heso / formula)
	(setq formula "=(")
	(if textlist
		(progn
			; Them text dau tien
			(setq formula (strcat formula (car textlist)))
			; Them cac text con lai voi dau "+"
			(foreach txt (cdr textlist)
				(setq formula (strcat formula "+" txt))
			)
			; Them he so
			(setq formula (strcat formula ")*" heso))
		)
		(setq formula (strcat formula "0)*" heso))
	)
	formula
)

-------------------------------------------------------------------------------------------------------------------
; Ham xuat text theo mang cho E5 - su dung logic giong G1
(defun E5-EXPORT-AS-ARRAY ( textlist xlcells startrow startcol /
	textlist-with-pos tolerance all-x all-y unique-x unique-y
	grid-map txt-item txt-pos txt-content grid-row grid-col
	excel-row excel-col cell-texts)

	; Lay lai textlist voi toa do tu ham chinh
	(setq textlist-with-pos (E5-GET-TEXTLIST-WITH-POS))
	(setq tolerance (if *ET-tolerance* *ET-tolerance* 50.0))

	; Thu thap tat ca toa do X va Y
	(setq all-x '() all-y '())
	(foreach txt textlist-with-pos
		(setq txt-pos (cadr txt))
		(setq all-x (cons (car txt-pos) all-x))
		(setq all-y (cons (cadr txt-pos) all-y))
	)

	; Tim cac toa do X va Y duy nhat - su dung ham cua G1
	(setq unique-x (G1-GET-UNIQUE-COORDS all-x tolerance))
	(setq unique-y (G1-GET-UNIQUE-COORDS all-y tolerance))

	; Sap xep X tang dan, Y giam dan
	(setq unique-x (vl-sort unique-x '<))
	(setq unique-y (vl-sort unique-y '>))

	; Tao ban do luoi
	(setq grid-map '())

	; Gan moi text vao o luoi gan nhat
	(foreach txt textlist-with-pos
		(setq txt-pos (cadr txt)
			  txt-content (car txt))

		; Tim chi so cot va hang gan nhat
		(setq grid-col (E5-FIND-NEAREST-GRID-LINE (car txt-pos) unique-x))
		(setq grid-row (E5-FIND-NEAREST-GRID-LINE (cadr txt-pos) unique-y))

		; Them text vao o luoi
		(setq grid-key (strcat (itoa grid-row) "," (itoa grid-col)))
		(if (assoc grid-key grid-map)
			; O luoi da co text - them vao danh sach
			(setq grid-map (subst
				(cons grid-key (append (cdr (assoc grid-key grid-map)) (list txt-content)))
				(assoc grid-key grid-map)
				grid-map))
			; O luoi chua co text - tao moi
			(setq grid-map (cons (cons grid-key (list txt-content)) grid-map))
		)
	)

	; Xuat luoi ra Excel
	(setq excel-row startrow)
	(setq grid-row 0)
	(while (< grid-row (length unique-y))
		(setq excel-col startcol)
		(setq grid-col 0)
		(while (< grid-col (length unique-x))
			(setq grid-key (strcat (itoa grid-row) "," (itoa grid-col)))
			(setq cell-texts (cdr (assoc grid-key grid-map)))
			(if cell-texts
				; Neu co nhieu text trong cung o, noi lai bang khoang trang
				(setcelltext xlcells excel-row excel-col (E5-JOIN-CELL-TEXTS cell-texts " "))
			)
			(setq grid-col (+ grid-col 1)
				  excel-col (+ excel-col 1))
		)
		(setq grid-row (+ grid-row 1)
			  excel-row (+ excel-row 1))
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham tim cac toa do duy nhat trong danh sach (nhom theo tolerance)
(defun E5-GET-UNIQUE-COORDS ( coord-list tolerance / unique-coords coord)
	(setq unique-coords '())
	(foreach coord coord-list
		; Kiem tra xem co toa do nao gan bang trong tolerance khong
		(if (not (vl-some '(lambda (existing-coord)
			(< (abs (- coord existing-coord)) tolerance)) unique-coords))
			; Neu khong co, them vao danh sach
			(setq unique-coords (cons coord unique-coords))
		)
	)
	; Tra ve danh sach da sap xep
	(vl-sort unique-coords '<)
)

-------------------------------------------------------------------------------------------------------------------
; Ham tim chi so duong luoi gan nhat
(defun E5-FIND-NEAREST-GRID-LINE ( coord grid-lines / best-index best-distance distance index)
	(setq best-index 0 best-distance 999999.0 index 0)
	(foreach grid-coord grid-lines
		(setq distance (abs (- coord grid-coord)))
		(if (< distance best-distance)
			(progn
				(setq best-distance distance)
				(setq best-index index)
			)
		)
		(setq index (+ index 1))
	)
	best-index
)

-------------------------------------------------------------------------------------------------------------------
; Ham noi cac text trong cung cell
(defun E5-JOIN-CELL-TEXTS ( text-list separator / result)
	(setq result "")
	(if text-list
		(progn
			(setq result (car text-list))
			(foreach txt (cdr text-list)
				(setq result (strcat result separator txt))
			)
		)
	)
	result
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay so hang cua mang - su dung logic G1
(defun E5-GET-ARRAY-ROWS ( textlist / textlist-with-pos tolerance all-y unique-y)
	(setq textlist-with-pos (E5-GET-TEXTLIST-WITH-POS))
	(setq tolerance (if *ET-tolerance* *ET-tolerance* 50.0))

	; Thu thap tat ca toa do Y
	(setq all-y '())
	(foreach txt textlist-with-pos
		(setq txt-pos (cadr txt))
		(setq all-y (cons (cadr txt-pos) all-y))
	)

	; Tim cac toa do Y duy nhat - su dung ham G1
	(setq unique-y (G1-GET-UNIQUE-COORDS all-y tolerance))

	; Tra ve so hang
	(length unique-y)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay cac hang duy nhat
(defun E5-GET-UNIQUE-ROWS ( textlist-with-pos / unique-rows y-pos tolerance)
	(setq unique-rows '()
		  tolerance (if *ET-tolerance* *ET-tolerance* 50.0))
	(foreach txt textlist-with-pos
		(setq y-pos (cadr (cadr txt)))
		(if (not (vl-some '(lambda (existing-y) (< (abs (- y-pos existing-y)) tolerance)) unique-rows))
			(setq unique-rows (append unique-rows (list y-pos)))
		)
	)
	(vl-sort unique-rows '>)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay cac cot duy nhat
(defun E5-GET-UNIQUE-COLS ( textlist-with-pos / unique-cols x-pos tolerance)
	(setq unique-cols '()
		  tolerance (if *ET-tolerance* *ET-tolerance* 50.0))
	(foreach txt textlist-with-pos
		(setq x-pos (car (cadr txt)))
		(if (not (vl-some '(lambda (existing-x) (< (abs (- x-pos existing-x)) tolerance)) unique-cols))
			(setq unique-cols (append unique-cols (list x-pos)))
		)
	)
	(vl-sort unique-cols '<)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay textlist voi toa do (bien global tam thoi)
(defun E5-GET-TEXTLIST-WITH-POS ( / )
	*E5-textlist-with-pos*
)

-------------------------------------------------------------------------------------------------------------------
; Ham phan tich thong tin bang
(defun tableinfo ( ss  / n entlist)
	(setq n 0)
	(repeat (sslength ss)
		(setq entlist (entget (ssname ss n)))
		(cond ((member (cdr (assoc 0 entlist)) '("LINE" "POLYLINE"))
			(getlinepts entlist)(setq linelist (cons (ssname ss n) linelist)))
			((member (cdr (assoc 0 entlist)) '("TEXT" "MTEXT"))
			(setq textlist (cons (ssname ss n) textlist)))
			((member (cdr (assoc 0 entlist)) '("INSERT"))
			(setq blocklist (cons (ssname ss n) blocklist)))
		)
		(setq n (1+ n))
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay diem cua line va polyline
(defun getlinepts (alist / x  xpt ypt)
  (foreach x alist
     (if (member (car x) '(10 11))
         (progn
           (if (not (vl-position (setq xpt (atof (rtos (car (trans (cdr x) 0 1)) 2 2))) lpxlist))
               (setq lpxlist (cons xpt lpxlist)))
           (if (not (vl-position (setq ypt (atof (rtos (cadr (trans (cdr x) 0 1)) 2 2))) lpylist))
               (setq lpylist (cons ypt lpylist)))
         )
      )
   )
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay thong tin text va vi tri cell
(defun gettxtinfo (alist / x vlaobj pos rpos cpos expos txt)
	(setq txt (cdr (assoc -1 alist)))
	(setq vlaobj (vlax-ename->vla-object txt)
			pos (trans (midp vlaobj) 0 1);Midpoint
			rpos (1- (vl-position (cadr pos)(vl-sort (cons (cadr pos) lpylist) '>)));Row Position
			cpos (1- (vl-position (car pos) (vl-sort (cons (car pos) lpxlist) '<))));Column Position
	(if (setq expos (vl-position (list rpos cpos) (mapcar '(lambda (x)(cdr (assoc "Position" x))) tinfo)));if cell is taken
		(setq tinfo
			(replace tinfo expos
				(replace
					(nth expos tinfo)
					2
					(cons "Content"
						(if (> (cadr pos) (cdr (assoc "Order" (nth expos tinfo))));in order according to y position
							(strcat (vla-fieldcode vlaobj) " " (cdr (assoc "Content" (nth expos tinfo))))
							(strcat (cdr (assoc "Content" (nth expos tinfo))) " " (vla-fieldcode vlaobj))
						)
					)
				)
			)
		)
		(setq tinfo
			(cons
				(list
					(Cons "Order" (cadr pos))
					(Cons "Position" (list rpos cpos));Position
					(Cons "Content" (vla-fieldcode vlaobj));Content
					(Cons "Height" (vla-get-height vlaobj))
					(Cons "Rotation" (vla-get-rotation vlaobj))
					(Cons "StyleName" (vla-get-StyleName vlaobj))
					(Cons "TrueColor"
						(if (= (vla-get-colorindex (vla-get-truecolor vlaobj)) 256)
							(vla-get-truecolor (vla-item (vla-get-layers ActDoc) (vla-get-layer vlaobj)))
							(vla-get-truecolor vlaobj)
						)
					)
				)
				tinfo
			)
		)
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay thong tin block va vi tri cell
(defun getblockinfo (obj / pos rpos cpos bname objid bobj attid)
	(if (= (type obj) 'ename) (setq obj (vlax-ename->vla-object obj)))
	(setq pos (trans (midp obj) 0 1)
		rpos (1- (vl-position (cadr pos) (vl-sort (cons (cadr pos) lpylist) '>)));Row Position
		cpos (1- (vl-position (car pos) (vl-sort (cons (car pos) lpxlist) '<)));Column Position
		bname (vla-get-name obj);Block Name
		bobj (vla-item (vla-get-blocks ActDoc) bname));Block Vla Object
	(vlax-for i bobj ; Foreach item in block
		(if (eq (vla-get-objectname i) "AcDbAttributeDefinition");If item is an attribute
			(setq attid (append attid (list (vla-get-objectid i))));List Attribute Id
		)
	)
	(setq objid (vla-get-objectid bobj));Block Object Id
	(setq binfo
		(cons
			(list
				(Cons "Name" bname)
				(Cons "Position" (list rpos cpos))
				(Cons "ObjID" objid)
				(if (= (vla-get-hasattributes obj) :vlax-true)
					(Cons "Attributes"
						(reverse
							(mapcar
								'(lambda (x y) (cons y (vla-get-textstring x)))
								(vlax-safearray->list (variant-value (vla-getattributes obj)))
								attid
							)
						)
					)
				)
				(Cons "Scale" (vla-get-xscalefactor obj))
			)
			binfo
		)
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham replace cua Charles Alan Butler
(defun replace (lst i itm)
  (setq i (1+ i))
  (mapcar
    '(lambda (x)
      (if (zerop (setq i (1- i))) itm x)
    )
    lst
  )
)

-------------------------------------------------------------------------------------------------------------------
; Ham tim diem giua cua object
(defun midp (obj / ObjLl ObjUr)
	(vla-GetBoundingBox obj 'ObjLl 'ObjUr)
	(mapcar '(lambda (a b) (/ (+ a b) 2.0))
		(safearray-value ObjLl)
		(safearray-value ObjUr)
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham dat text vao cell Excel
(defun setcelltext(cells row column value)
  (vl-catch-all-apply
    'vlax-put-property
    (list cells 'Item row column
	 (vlax-make-variant
	   (vl-princ-to-string value) 8)))
  )

-------------------------------------------------------------------------------------------------------------------
; Ham ve vien cho bang Excel
(defun setgridlines(xlapp range)
  ; select the range:
  (vlax-invoke-method range 'Select)
  ; get excel application selection property:
  (setq range (vlax-get-property xlapp 'Selection))
  ; get selection borders
  (setq borders (vlax-get-property range 'Borders))
  ; iterate through all edges of the selection
  (setq cnt 0)
    (vlax-for a	 borders
      (setq cnt (1+ cnt))
      (vl-catch-all-apply
	(function
	  (lambda ()
	    (progn
	      (if (< cnt 5)
		(progn
		  (vlax-put-property
		    a
		    'LineStyle
		    (vlax-make-variant 1 3)); single line style
		  (vlax-put-property
		    a
		    'Weight
		    (vlax-make-variant 2 3));  lines
		  (vlax-put-property
		    a
		    'ColorIndex
		    (vlax-make-variant 1 5))); color black

		;; turn off the diagonal lines:
		(vlax-put-property a 'LineStyle (vlax-make-variant -4142 3))
		)
	      )
	    )
	  )
	)
      )
  (princ)
  )

-------------------------------------------------------------------------------------------------------------------
; Ham tao danh sach cot Excel
(defun conexcelcolumn (/ a b list1)
  (setq a 65)
  (setq list1 nil)
  (repeat 26
    (setq list1 (append
		  list1
		  (list (chr a))
		)
    )
    (setq a (1+ a))
  )
  (setq a 65)
  (repeat 26
    (setq b 65)
    (repeat 26
      (setq list1 (append
		    list1
		    (list (strcat (chr a) (chr b)))
		  )
      )
      (setq b (1+ b))
    )
    (setq a (1+ a))
  )
  list1
)

-------------------------------------------------------------------------------------------------------------------
; E8 - Ghi gia tri so vao o + handle vao comment
(defun C:E8 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	i
	ent
	obj
	textcontent
	numvalue
	textpos
	handle
	newrow
	targetcell
	dwgpath
	dwgname
	celltext
	handlelist
	commenttext
	numstr
	result
	activecell
	cellrange
	oerror)

	-------------------------------------------------------------------------------------------------------------------

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(setvar 'nomutt 0)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Khoi tao thiet lap mac dinh
	(or *ET-symbol* (setq *ET-symbol* ";"))
	(or *ET-factor* (setq *ET-factor* "1"))

	; Ket noi Excel
	(if (setq xlapp (vlax-get-object "Excel.Application"))
		(progn
			(setq xlcells (vlax-get-property (vlax-get-property xlapp "ActiveSheet") "Cells"))
			(setq startrow (vlax-get-property (vlax-get-property xlapp "ActiveCell") "Row"))
			(setq startcol (vlax-get-property (vlax-get-property xlapp "ActiveCell") "Column"))

			; Hien thi menu thiet lap
			(E8-SHOW-SETTINGS-MENU)

			; Chon cac text/mtext/dimension
			(princ "\nChon cac text/mtext/dimension de lay gia tri so: ")
			(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

			(if otcontents
				(progn
					(setq dwgpath (vla-get-fullname ActDoc)
						  dwgname (vl-filename-base dwgpath))

					; Xu ly noi dung cell va handle - su dung logic cua E7
					(setq celltext (E8-CREATE-CELL-TEXT otcontents))

					(setq handlelist (E6-GET-HANDLES otcontents))
					(setq commenttext (E6-CREATE-COMMENT-TEXT handlelist dwgpath dwgname))

					; Ghi vao Excel - su dung setcelltext da hoat dong
					(if celltext
						(progn
							; Ghi cell bang setcelltext (da hoat dong tot)
							(setcelltext xlcells startrow startcol celltext)

							; Ghi comment - thu nhieu cach
							(setq result (vl-catch-all-apply '(lambda ()
								; Cach 1: Su dung cell vua ghi
								(setq activecell (vlax-get-property xlcells "Item" startrow startcol))
								(E6-WRITE-COMMENT-TO-CELL activecell commenttext)
							)))
							(if (vl-catch-all-error-p result)
								(progn
									; Cach 2: Su dung ActiveCell
									(setq result (vl-catch-all-apply '(lambda ()
										(setq activecell (vlax-get-property xlapp "ActiveCell"))
										(E6-WRITE-COMMENT-TO-CELL activecell commenttext)
									)))
									(if (vl-catch-all-error-p result)
										(progn
											; Cach 3: Su dung Range
											(setq result (vl-catch-all-apply '(lambda ()
												(setq cellrange (strcat (E6-COLUMN-NUMBER-TO-LETTER startcol) (itoa startrow)))
												(setq activecell (vlax-get-property (vlax-get-property xlapp "ActiveSheet") "Range" cellrange))
												(E6-WRITE-COMMENT-TO-CELL activecell commenttext)
											)))
										)
									)
								)
							)
						)
						(princ "\nKhong co gia tri de ghi")
					)

					; Nhay chuot xuong 3 dong
					(setq newrow (+ startrow 3))
					(vl-catch-all-apply '(lambda ()
						(setq targetcell (vlax-get-property xlcells "Item" newrow startcol))
						(vlax-invoke-method targetcell "Select")
						(vlax-put-property xlapp "ActiveCell" targetcell)
					))
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong chon duoc text/mtext nao!")
			)
		)
		(princ "\nKhong tim thay Excel dang chay!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

-------------------------------------------------------------------------------------------------------------------
; Ham tao noi dung cell cho E8 - xu ly nhieu gia tri nhu E5-3
(defun E8-CREATE-CELL-TEXT ( ss / n ent entobj objname txtcontent measurement numvalue numlist result symbol factor)
	(setq n 0 numlist '())

	; Lay thiet lap tu ET hoac mac dinh
	(setq symbol (if *ET-symbol* *ET-symbol* ";"))
	(setq factor (if *ET-factor* *ET-factor* "1"))

	; Duyet qua tat ca doi tuong
	(repeat (sslength ss)
		(setq ent (ssname ss n)
			  entobj (vlax-ename->vla-object ent)
			  objname (vla-get-objectname entobj)
			  numvalue nil)

		(cond
			; TEXT va MTEXT - trich xuat so
			((or (= objname "AcDbText") (= objname "AcDbMText"))
				(setq txtcontent (vla-get-textstring entobj))
				; Trich xuat so truc tiep tu text goc
				(setq numvalue (E8-EXTRACT-NUMBER txtcontent))
			)
			; DIMENSION - lay measurement truc tiep
			((or (= objname "AcDbAlignedDimension")
				 (= objname "AcDbRotatedDimension")
				 (= objname "AcDbRadialDimension")
				 (= objname "AcDbDiametricDimension")
				 (= objname "AcDbAngularDimension")
				 (wcmatch objname "*Dimension*"))
				(setq numvalue (vla-get-measurement entobj))
			)
		)

		; Thu thap tat ca gia tri so
		(if numvalue
			(setq numlist (append numlist (list (rtos numvalue 2 2))))
		)

		(setq n (+ n 1))
	)

	; Tao ket qua theo symbol
	(if numlist
		(progn
			; Neu symbol la "+", tao cong thuc Excel
			(if (= symbol "+")
				(progn
					(setq result (strcat "=(" (car numlist)))
					(foreach num (cdr numlist)
						(setq result (strcat result "+" num))
					)
					(setq result (strcat result ")*" factor))
				)
				; Neu khong phai "+", noi binh thuong
				(progn
					(setq result (car numlist))
					(foreach num (cdr numlist)
						(setq result (strcat result symbol num))
					)
				)
			)
		)
		(setq result nil)
	)

	; Tra ve chuoi ket qua (khong phai so)
	result
)



-------------------------------------------------------------------------------------------------------------------
; Ham trich xuat so tu chuoi text cho E8 - cai tien
(defun E8-EXTRACT-NUMBER ( text-string / cleaned-text num-string result)
	; Kiem tra input la chuoi
	(if (and text-string (= (type text-string) 'STR) (> (strlen text-string) 0))
		(progn
			; Loai bo cac ky tu dac biet cua MTEXT
			(setq cleaned-text (E8-CLEAN-MTEXT text-string))
			(princ (strcat "\nText sau khi lam sach: \"" cleaned-text "\""))

			; Tim so trong chuoi
			(setq num-string (E8-FIND-NUMBER-IN-STRING cleaned-text))
			(princ (strcat "\nSo tim thay: \"" (if num-string num-string "nil") "\""))

			; Chuyen doi thanh so
			(if (and num-string (> (strlen num-string) 0))
				(progn
					; Chuyen dau phay thanh dau cham cho Excel
					(setq num-string (vl-string-subst "." "," num-string))
					; Chuyen doi thanh so
					(setq result (atof num-string))
					(princ (strcat "\nGia tri so: " (rtos result 2 2)))
					; Tra ve gia tri so (ke ca 0)
					result
				)
				nil
			)
		)
		; Neu input khong phai chuoi, tra ve nil
		nil
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lam sach MTEXT (loai bo cac ma dieu khien)
(defun E8-CLEAN-MTEXT ( text-string / result i char)
	(setq result "" i 1)
	(while (<= i (strlen text-string))
		(setq char (substr text-string i 1))
		(cond
			; Bo qua cac ma dieu khien MTEXT bat dau bang \
			((= char "\\")
				; Tim ky tu ; hoac } de ket thuc ma dieu khien
				(while (and (<= i (strlen text-string))
						   (not (member (substr text-string i 1) '(";" "}"))))
					(setq i (+ i 1))
				)
			)
			; Bo qua cac ky tu dac biet
			((member char '("{" "}"))
				; Bo qua
			)
			; Giu lai cac ky tu binh thuong
			(T
				(setq result (strcat result char))
			)
		)
		(setq i (+ i 1))
	)
	result
)

-------------------------------------------------------------------------------------------------------------------
; Ham tim so trong chuoi - cai tien
(defun E8-FIND-NUMBER-IN-STRING ( text-string / result i char in-number current-number has-decimal)
	(setq result "" i 1 in-number nil current-number "" has-decimal nil)

	(while (<= i (strlen text-string))
		(setq char (substr text-string i 1))
		(cond
			; Ky tu so
			((and (>= (ascii char) 48) (<= (ascii char) 57))
				(setq in-number T)
				(setq current-number (strcat current-number char))
			)
			; Dau thap phan (cham hoac phay) - chi cho phep 1 lan
			((and in-number (member char '("." ",")) (not has-decimal))
				(setq current-number (strcat current-number char))
				(setq has-decimal T)
			)
			; Ky tu khac - ket thuc so neu da co so
			(T
				(if (and in-number (> (strlen current-number) 0))
					(progn
						; Kiem tra xem co phai so hop le khong
						(if (or (and (= (strlen current-number) 1) (not has-decimal))
								(and (> (strlen current-number) 1)
									 (not (member (substr current-number (strlen current-number) 1) '("." ",")))))
							(progn
								(setq result current-number)
								(setq i (strlen text-string)) ; Thoat vong lap
							)
							(progn
								(setq in-number nil)
								(setq current-number "")
								(setq has-decimal nil)
							)
						)
					)
					(progn
						(setq in-number nil)
						(setq current-number "")
						(setq has-decimal nil)
					)
				)
			)
		)
		(setq i (+ i 1))
	)

	; Neu ket thuc chuoi ma van dang trong so
	(if (and in-number (> (strlen current-number) 0))
		; Kiem tra xem co phai so hop le khong (khong ket thuc bang dau thap phan)
		(if (not (member (substr current-number (strlen current-number) 1) '("." ",")))
			(setq result current-number)
		)
	)

	result
)







-------------------------------------------------------------------------------------------------------------------
; Ham tim cac toa do duy nhat cho G1
(defun G1-GET-UNIQUE-COORDS ( coord-list tolerance / unique-coords coord)
	(setq unique-coords '())
	(foreach coord coord-list
		; Kiem tra xem co toa do nao gan bang trong tolerance khong
		(if (not (vl-some '(lambda (existing-coord)
			(< (abs (- coord existing-coord)) tolerance)) unique-coords))
			; Neu khong co, them vao danh sach
			(setq unique-coords (cons coord unique-coords))
		)
	)
	; Tra ve danh sach da sap xep
	(vl-sort unique-coords '<)
)



-------------------------------------------------------------------------------------------------------------------
; Ham hien thi menu thiet lap cho E8
(defun E8-SHOW-SETTINGS-MENU ( / choice input continue)
	(princ (strcat "\nThiet lap hien tai - Ky tu noi: \"" *ET-symbol* "\", He so: " *ET-factor*))
	(setq continue T)
	(while continue
		(princ (strcat "\nThiet lap [Symbol/Factor] <tiep tuc>: "))
		(initget "Symbol Factor S F")
		(setq choice (getkword ""))

		(cond
			; Symbol - Ky tu noi
			((or (= choice "Symbol") (= choice "S"))
				(setq input (getstring (strcat "\nNhap ky tu noi <" *ET-symbol* ">: ")))
				(if (and input (> (strlen input) 0)) (setq *ET-symbol* input))
				(princ (strcat "\nKy tu noi: \"" *ET-symbol* "\""))
				(if (= *ET-symbol* "+")
					(princ "\nLuu y: Ky tu '+' se tao cong thuc Excel voi he so")
				)
			)
			; Factor - He so
			((or (= choice "Factor") (= choice "F"))
				(setq input (getstring (strcat "\nNhap he so <" *ET-factor* ">: ")))
				(if (and input (> (strlen input) 0)) (setq *ET-factor* input))
				(princ (strcat "\nHe so: " *ET-factor*))
			)
			; Tiep tuc hoac Enter
			((= choice nil) (setq continue nil))
			; Khac
			(T (princ "\nLua chon khong hop le!"))
		)
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ket thuc file CAD to Excel - Tong hop E1-E8 + ET toi uu
(princ "\nCAD to Excel da duoc load thanh cong!")
(princ "\nCo 10 lenh co san:")
(princ "\n  E1 - Copy text (khong vien)")
(princ "\n  E2 - Copy text (co vien)")
(princ "\n  E3 - Copy text + nhay chuot 3 dong")
(princ "\n  E4 - Copy text + nhay chuot tuy chinh")
(princ "\n  E5 - Xuat text theo cot/hang/o/mang")
(princ "\n  E6 - Ghi handle doi tuong vao comment Excel")
(princ "\n  E7 - Ghi text/dim vao o + handle vao comment")
(princ "\n  E8 - Ghi gia tri so vao o + handle vao comment (co thiet lap ky tu noi va he so)")
(princ "\n  ET - Lenh tong hop toi uu (thay the E1-E7)")
(princ "\n  G1 - Ve luoi giua cac text (ho tro debug)")
(princ "\nLuu y: Mo Excel truoc khi chay lenh de dat du lieu vao vi tri con tro.")
(princ "\nLenh ET moi co cac che do:")
(princ "\n  Table/T - Copy text (E1)")
(princ "\n  Col/C - Xuat theo cot (E5-1)")
(princ "\n  Row/R - Xuat theo hang (E5-2)")
(princ "\n  Cell/Ce - Noi vao 1 o (E5-3)")
(princ "\n  Array/A - Xuat theo mang (E5-4)")
(princ "\n  Handle/H - Ghi handle (E6)")
(princ "\n  Value/V - Ghi cell + comment (E7)")
(princ "\nCac thiet lap:")
(princ "\n  Jump/J - So dong nhay")
(princ "\n  Symbol/S - Ky tu noi")
(princ "\n  Factor/F - He so nhan")
(princ "\n  Tolerance/T - Sai so cho phep cho mang (mac dinh: 50.0)")
(princ "\nLenh G1:")
(princ "\n  Ve luoi ao giua cac text de kiem tra phan chia hang/cot")
(princ "\n  Duong doc (X) mau do, duong ngang (Y) mau xanh la")
(princ "\n  Su dung tolerance de nhom cac text gan nhau")
(princ)
