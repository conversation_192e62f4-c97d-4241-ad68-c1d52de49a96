; ===================================================================
; CAD TO EXCEL - PHIEN BAN MOI TINH GON
; Tac gia: Toi uu tu phien ban cu
; Ngay: 2024
; ===================================================================

; Bien toan cuc luu thiet lap
(or *E6-handle* (setq *E6-handle* "Y"))
(or *E6-frame* (setq *E6-frame* "N"))
(or *E6-symbol* (setq *E6-symbol* ";"))
(or *E6-factor* (setq *E6-factor* "1"))
(or *E6-jump* (setq *E6-jump* "3"))
(or *E6-tolerance* (setq *E6-tolerance* "50"))

; ===================================================================
; HAM PHU TRO CHUNG
; ===================================================================

; Ham ket noi Excel
(defun CONNECT-EXCEL ( / xlapp xlcells startrow startcol)
	(if (not (setq xlapp (vlax-get-object "Excel.Application")))
		(progn
			(princ "\nKhong tim thay Excel. Dang khoi dong Excel...")
			(setq xlapp (vlax-create-object "Excel.Application"))
		)
	)
	(vlax-put-property xlapp "Visible" :vlax-true)
	(setq xlcells (vlax-get-property (vlax-get-property xlapp "ActiveSheet") "Cells"))
	(setq startrow (vlax-get-property (vlax-get-property xlapp "ActiveCell") "Row"))
	(setq startcol (vlax-get-property (vlax-get-property xlapp "ActiveCell") "Column"))
	(list xlapp xlcells startrow startcol)
)

; Ham ghi cell text
(defun SETCELLTEXT ( xlcells row col text / result)
	(setq result (vl-catch-all-apply '(lambda ()
		(vlax-put-property (vlax-get-property xlcells "Item" row col) "Value" text)
	)))
	(if (vl-catch-all-error-p result)
		(princ (strcat "\nLoi ghi cell: " (vl-catch-all-error-message result)))
	)
)

; Ham lay handle cua doi tuong
(defun GET-HANDLES ( ss / i handlelist ent)
	(setq handlelist '() i 0)
	(repeat (sslength ss)
		(setq ent (ssname ss i))
		(setq handlelist (cons (vla-get-handle (vlax-ename->vla-object ent)) handlelist))
		(setq i (+ i 1))
	)
	(reverse handlelist)
)

; Ham tao comment text
(defun CREATE-COMMENT-TEXT ( handlelist dwgpath dwgname / commenttext)
	(setq commenttext "")
	(foreach handle handlelist
		(setq commenttext (strcat commenttext handle "; "))
	)
	(setq commenttext (strcat commenttext "\nFileCad: " dwgpath))
	commenttext
)

; Ham ghi comment vao cell
(defun WRITE-COMMENT-TO-CELL ( cell commenttext / result comments comment)
	(setq result (vl-catch-all-apply '(lambda ()
		(setq comments (vlax-get-property cell "Comment"))
		(if comments
			(vlax-invoke-method comments "Delete")
		)
		(setq comment (vlax-invoke-method cell "AddComment" commenttext))
		(vlax-put-property comment "Visible" :vlax-false)
	)))
	(if (vl-catch-all-error-p result)
		(princ (strcat "\nLoi ghi comment: " (vl-catch-all-error-message result)))
	)
)

; Ham chuyen so cot thanh chu cai
(defun COLUMN-NUMBER-TO-LETTER ( col / result)
	(setq result "")
	(while (> col 0)
		(setq col (- col 1))
		(setq result (strcat (chr (+ 65 (rem col 26))) result))
		(setq col (/ col 26))
	)
	result
)

; Ham trich xuat so tu text
(defun EXTRACT-NUMBER ( text-string / cleaned-text i char in-number current-number result)
	(if (and text-string (= (type text-string) 'STR) (> (strlen text-string) 0))
		(progn
			; Loai bo cac ky tu dac biet cua MTEXT
			(setq cleaned-text text-string)
			(setq cleaned-text (vl-string-subst "" "\\P" cleaned-text))
			(setq cleaned-text (vl-string-subst "" "\\p" cleaned-text))
			(while (vl-string-search "\\" cleaned-text)
				(setq cleaned-text (vl-string-subst "" (substr cleaned-text (vl-string-search "\\" cleaned-text) 2) cleaned-text))
			)
			
			; Tim so trong chuoi
			(setq result "" i 1 in-number nil current-number "")
			(while (<= i (strlen cleaned-text))
				(setq char (substr cleaned-text i 1))
				(cond
					; Ky tu so
					((and (>= (ascii char) 48) (<= (ascii char) 57))
						(setq in-number T)
						(setq current-number (strcat current-number char))
					)
					; Dau thap phan
					((and in-number (member char '("." ",")))
						(setq current-number (strcat current-number char))
					)
					; Ky tu khac - ket thuc so
					(T
						(if (and in-number (> (strlen current-number) 0))
							(progn
								(setq result current-number)
								(setq i (strlen cleaned-text)) ; Thoat vong lap
							)
							(progn
								(setq in-number nil)
								(setq current-number "")
							)
						)
					)
				)
				(setq i (+ i 1))
			)
			
			; Neu ket thuc chuoi ma van dang trong so
			(if (and in-number (> (strlen current-number) 0))
				(setq result current-number)
			)
			
			; Chuyen doi thanh so
			(if (and result (> (strlen result) 0))
				(progn
					(setq result (vl-string-subst "." "," result))
					(atof result)
				)
				nil
			)
		)
		nil
	)
)

; Ham chuyen selection set thanh danh sach text
(defun CONVERT-SS-TO-TEXTLIST ( ss / i ent obj objname txtcontent txtpos measurement result)
	(setq result '() i 0)
	(repeat (sslength ss)
		(setq ent (ssname ss i)
			  obj (vlax-ename->vla-object ent)
			  objname (vla-get-objectname obj))
		
		(cond
			; TEXT
			((= objname "AcDbText")
				(setq txtcontent (vla-get-textstring obj))
				(setq txtpos (vl-catch-all-apply '(lambda ()
					(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
				)))
				(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
			)
			; MTEXT
			((= objname "AcDbMText")
				(setq txtcontent (vla-get-textstring obj))
				(setq txtpos (vl-catch-all-apply '(lambda ()
					(vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint obj)))
				)))
				(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
			)
			; DIMENSION
			((or (= objname "AcDbAlignedDimension")
				 (= objname "AcDbRotatedDimension")
				 (= objname "AcDbRadialDimension")
				 (= objname "AcDbDiametricDimension")
				 (= objname "AcDbAngularDimension")
				 (wcmatch objname "*Dimension*"))
				(setq measurement (vla-get-measurement obj))
				(setq txtcontent (rtos measurement 2 2))
				(setq txtpos (vl-catch-all-apply '(lambda ()
					(vlax-safearray->list (vlax-variant-value (vla-get-textposition obj)))
				)))
				(if (vl-catch-all-error-p txtpos) (setq txtpos (list 0.0 0.0 0.0)))
			)
		)
		
		(setq result (cons (list txtcontent txtpos objname) result))
		(setq i (+ i 1))
	)
	(reverse result)
)

; Ham nhay chuot Excel
(defun MOVE-CURSOR ( xlcells row col)
	(vl-catch-all-apply '(lambda ()
		(setq targetcell (vlax-get-property xlcells "Item" row col))
		(vlax-invoke-method targetcell "Select")
	))
)

; ===================================================================
; E1 - XUAT TEXT/DIM VAO CELL (THAY CHO E8 CU)
; ===================================================================

(defun C:E1 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	numlist
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong
	(princ "\nChon text/mtext/dimension: ")
	(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))
			(setq numlist '())
			(setq factor (atof *E6-factor*))

			; Thu thap gia tri so
			(foreach txt textlist
				(setq objname (caddr txt))
				(cond
					; TEXT va MTEXT - trich xuat so
					((or (= objname "AcDbText") (= objname "AcDbMText"))
						(setq numvalue (EXTRACT-NUMBER (car txt)))
						(if numvalue
							(setq numlist (cons (* numvalue factor) numlist))
						)
					)
					; DIMENSION - lay measurement
					((wcmatch objname "*Dimension*")
						(setq numvalue (atof (car txt)))
						(if numvalue
							(setq numlist (cons (* numvalue factor) numlist))
						)
					)
				)
			)

			(setq numlist (reverse numlist))

			; Tao noi dung cell
			(cond
				; 1 gia tri
				((= (length numlist) 1)
					(setq result-text (rtos (car numlist) 2 2))
				)
				; 2 gia tri
				((= (length numlist) 2)
					(setq result-text (strcat (rtos (car numlist) 2 2) *E6-symbol* (rtos (cadr numlist) 2 2)))
				)
				; Nhieu hon 2 gia tri
				((> (length numlist) 2)
					(if (= *E6-symbol* "+")
						; Tao cong thuc Excel
						(progn
							(setq result-text (strcat "=" (rtos (car numlist) 2 2)))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text "+" (rtos num 2 2)))
							)
						)
						; Noi binh thuong
						(progn
							(setq result-text (rtos (car numlist) 2 2))
							(foreach num (cdr numlist)
								(setq result-text (strcat result-text *E6-symbol* (rtos num 2 2)))
							)
						)
					)
				)
				; Khong co gia tri
				(T (setq result-text ""))
			)

			; Ghi vao Excel
			(if (and result-text (> (strlen result-text) 0))
				(progn
					(SETCELLTEXT xlcells startrow startcol result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
							(WRITE-COMMENT-TO-CELL targetcell commenttext)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong co gia tri de ghi")
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; E2 - XUAT TEXT/DIM VAO CELL (THAY CHO E7 CU)
; ===================================================================

(defun C:E2 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	numlist
	textonly-list
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong
	(princ "\nChon text/mtext/dimension: ")
	(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))
			(setq numlist '())
			(setq textonly-list '())
			(setq factor (atof *E6-factor*))

			; Phan loai text va dimension
			(foreach txt textlist
				(setq objname (caddr txt))
				(cond
					; TEXT va MTEXT - giu nguyen text
					((or (= objname "AcDbText") (= objname "AcDbMText"))
						(setq textonly-list (cons (car txt) textonly-list))
					)
					; DIMENSION - nhan voi he so
					((wcmatch objname "*Dimension*")
						(setq numvalue (atof (car txt)))
						(if numvalue
							(setq numlist (cons (* numvalue factor) numlist))
						)
					)
				)
			)

			(setq numlist (reverse numlist))
			(setq textonly-list (reverse textonly-list))

			; Tao noi dung cell - uu tien text truoc, sau do dimension
			(setq result-text "")

			; Xu ly text
			(if textonly-list
				(progn
					(setq result-text (car textonly-list))
					(foreach txt (cdr textonly-list)
						(setq result-text (strcat result-text *E6-symbol* txt))
					)
				)
			)

			; Xu ly dimension
			(if numlist
				(progn
					(if (> (strlen result-text) 0)
						(setq result-text (strcat result-text *E6-symbol*))
					)

					(cond
						; 1 dimension
						((= (length numlist) 1)
							(setq result-text (strcat result-text (rtos (car numlist) 2 2)))
						)
						; Nhieu dimension
						((> (length numlist) 1)
							(if (= *E6-symbol* "+")
								; Tao cong thuc Excel cho dimension
								(progn
									(setq dim-formula (strcat "=" (rtos (car numlist) 2 2)))
									(foreach num (cdr numlist)
										(setq dim-formula (strcat dim-formula "+" (rtos num 2 2)))
									)
									(setq result-text (strcat result-text dim-formula))
								)
								; Noi binh thuong
								(progn
									(setq result-text (strcat result-text (rtos (car numlist) 2 2)))
									(foreach num (cdr numlist)
										(setq result-text (strcat result-text *E6-symbol* (rtos num 2 2)))
									)
								)
							)
						)
					)
				)
			)

			; Ghi vao Excel
			(if (and result-text (> (strlen result-text) 0))
				(progn
					(SETCELLTEXT xlcells startrow startcol result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
							(WRITE-COMMENT-TO-CELL targetcell commenttext)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong co gia tri de ghi")
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; E3 - CHI GHI HANDLE VAO COMMENT (THAY CHO E6 CU)
; ===================================================================

(defun C:E3 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	handlelist
	commenttext
	dwgpath
	dwgname
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong
	(princ "\nChon doi tuong de ghi handle: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Lay thong tin file CAD
			(setq dwgpath (vla-get-fullname ActDoc)
				  dwgname (vl-filename-base dwgpath))

			; Lay handle va tao comment
			(setq handlelist (GET-HANDLES otcontents))
			(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
			(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
			(WRITE-COMMENT-TO-CELL targetcell commenttext)

			; Nhay chuot
			(setq newrow (+ startrow (atoi *E6-jump*)))
			(MOVE-CURSOR xlcells newrow startcol)
			(princ (strcat "\nHoan thanh! Da ghi " (itoa (length handlelist)) " handle vao comment"))
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; E4 - XUAT TABLE VAO EXCEL (THAY CHO E1 CU)
; ===================================================================

(defun C:E4 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	lpxlist
	lpylist
	blocklist
	newrow
	newcol
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon bang
	(princ "\nChon bang de xuat: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Khoi tao danh sach
			(setq textlist '() lpxlist '() lpylist '() blocklist '())
			(setq *txtinfo* '() *blkinfo* '()) ; Khoi tao bien global

			; Xu ly thong tin
			(E4-TABLEINFO otcontents)
			(setq lpxlist (vl-sort lpxlist '<) lpylist (vl-sort lpylist '>))
			(mapcar '(lambda (txt)(E4-GETTXTINFO (entget txt))) textlist)
			(mapcar '(lambda (blk)(E4-GETBLOCKINFO blk)) blocklist)

			; Xuat ra Excel
			(E4-EXPORT-TO-EXCEL xlcells startrow startcol)

			; Nhay chuot den cuoi bang
			(setq newrow (+ startrow (length lpylist) (atoi *E6-jump*)))
			(MOVE-CURSOR xlcells newrow startcol)
			(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
		)
		(princ "\nKhong chon duoc bang!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; HAM PHU TRO CHO E4
; ===================================================================

; Ham xu ly thong tin bang cho E4
(defun E4-TABLEINFO ( ss / i ent entdata enttype)
	(setq i 0)
	(repeat (sslength ss)
		(setq ent (ssname ss i)
			  entdata (entget ent)
			  enttype (cdr (assoc 0 entdata)))
		(cond
			((= enttype "TEXT") (setq textlist (cons ent textlist)))
			((= enttype "MTEXT") (setq textlist (cons ent textlist)))
			((= enttype "LINE")
				(setq lpxlist (cons (car (cdr (assoc 10 entdata))) lpxlist))
				(setq lpxlist (cons (car (cdr (assoc 11 entdata))) lpxlist))
				(setq lpylist (cons (cadr (cdr (assoc 10 entdata))) lpylist))
				(setq lpylist (cons (cadr (cdr (assoc 11 entdata))) lpylist))
			)
			((= enttype "INSERT") (setq blocklist (cons ent blocklist)))
		)
		(setq i (+ i 1))
	)
)

; Ham lay thong tin text cho E4
(defun E4-GETTXTINFO ( entdata / txtpos txtcontent)
	(setq txtpos (cdr (assoc 10 entdata))
		  txtcontent (cdr (assoc 1 entdata)))
	(setq *txtinfo* (cons (list txtcontent txtpos) *txtinfo*))
)

; Ham lay thong tin block cho E4
(defun E4-GETBLOCKINFO ( ent / entdata blkpos blkname)
	(setq entdata (entget ent)
		  blkpos (cdr (assoc 10 entdata))
		  blkname (cdr (assoc 2 entdata)))
	(setq *blkinfo* (cons (list blkname blkpos) *blkinfo*))
)

; Ham xuat bang ra Excel cho E4
(defun E4-EXPORT-TO-EXCEL ( xlcells startrow startcol / row col txtitem txtpos txtcontent)
	(setq row startrow col startcol)
	(setq *txtinfo* '()) ; Khoi tao bien global
	(foreach ypos lpylist
		(setq col startcol)
		(foreach xpos lpxlist
			; Tim text gan vi tri nay nhat
			(setq txtitem (E4-FIND-TEXT-AT-POSITION xpos ypos))
			(if txtitem
				(SETCELLTEXT xlcells row col (car txtitem))
			)
			(setq col (+ col 1))
		)
		(setq row (+ row 1))
	)

	; Dong khung neu can
	(if (= *E6-frame* "Y")
		(E4-ADD-BORDERS xlcells startrow startcol (+ startrow (length lpylist) -1) (+ startcol (length lpxlist) -1))
	)
)

; Ham tim text tai vi tri
(defun E4-FIND-TEXT-AT-POSITION ( x y / best-txt min-dist txtitem txtpos dist)
	(setq best-txt nil min-dist 1e10)
	(foreach txtitem *txtinfo*
		(setq txtpos (cadr txtitem))
		(setq dist (sqrt (+ (expt (- x (car txtpos)) 2) (expt (- y (cadr txtpos)) 2))))
		(if (< dist min-dist)
			(progn
				(setq min-dist dist)
				(setq best-txt txtitem)
			)
		)
	)
	best-txt
)

; Ham dong khung cho E4
(defun E4-ADD-BORDERS ( xlcells startrow startcol endrow endcol / range)
	(vl-catch-all-apply '(lambda ()
		(setq range (vlax-get-property
			(vlax-get-property xlcells "Parent")
			"Range"
			(vlax-get-property (vlax-get-property xlcells "Item" startrow startcol) "Address")
			(vlax-get-property (vlax-get-property xlcells "Item" endrow endcol) "Address")))
		(vlax-put-property (vlax-get-property range "Borders") "LineStyle" 1)
	))
)

; ===================================================================
; E5 - XUAT THEO 3 CHE DO (COL/ROW/ARRAY)
; ===================================================================

(defun C:E5 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	mode
	newrow
	newcol
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong
	(princ "\nChon text/mtext/dimension: ")
	(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

	(if otcontents
		(progn
			; Chuyen doi selection set
			(setq textlist (CONVERT-SS-TO-TEXTLIST otcontents))

			; Hien thi menu
			(princ "\nChon che do xuat:")
			(princ "\n1. Cot")
			(princ "\n2. Hang")
			(princ "\n3. Mang")
			(setq mode (getint "\nNhap lua chon [1-3]: "))

			(cond
				; Che do cot
				((= mode 1)
					(E5-EXPORT-COL textlist xlcells startrow startcol)
					(setq newrow (+ startrow (length textlist) (atoi *E6-jump*)) newcol startcol)
				)
				; Che do hang
				((= mode 2)
					(E5-EXPORT-ROW textlist xlcells startrow startcol)
					(setq newrow (+ startrow (atoi *E6-jump*)) newcol (+ startcol (length textlist)))
				)
				; Che do mang
				((= mode 3)
					(E5-EXPORT-ARRAY textlist xlcells startrow startcol)
					(setq newrow (+ startrow (E5-GET-ARRAY-ROWS textlist) (atoi *E6-jump*)) newcol startcol)
				)
				; Lua chon khong hop le
				(T (princ "\nLua chon khong hop le!"))
			)

			; Nhay chuot
			(if (and newrow newcol)
				(progn
					(MOVE-CURSOR xlcells newrow newcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa newcol)))
				)
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; HAM PHU TRO CHO E5
; ===================================================================

; Ham xuat theo cot
(defun E5-EXPORT-COL ( textlist xlcells startrow startcol / sorted-textlist row)
	(setq sorted-textlist (vl-sort textlist '(lambda (a b) (> (cadr (cadr a)) (cadr (cadr b))))))
	(setq row startrow)
	(foreach txt sorted-textlist
		(SETCELLTEXT xlcells row startcol (car txt))
		(setq row (+ row 1))
	)
)

; Ham xuat theo hang
(defun E5-EXPORT-ROW ( textlist xlcells startrow startcol / sorted-textlist col)
	(setq sorted-textlist (vl-sort textlist '(lambda (a b) (< (car (cadr a)) (car (cadr b))))))
	(setq col startcol)
	(foreach txt sorted-textlist
		(SETCELLTEXT xlcells startrow col (car txt))
		(setq col (+ col 1))
	)
)

; Ham xuat theo mang
(defun E5-EXPORT-ARRAY ( textlist xlcells startrow startcol / unique-x unique-y tolerance grid-row grid-col excel-row excel-col)
	(setq tolerance (atof *E6-tolerance*))

	; Thu thap toa do X va Y
	(setq all-x '() all-y '())
	(foreach txt textlist
		(setq txt-pos (cadr txt))
		(setq all-x (cons (car txt-pos) all-x))
		(setq all-y (cons (cadr txt-pos) all-y))
	)

	; Tim cac toa do duy nhat
	(setq unique-x (E5-GET-UNIQUE-COORDS all-x tolerance))
	(setq unique-y (E5-GET-UNIQUE-COORDS all-y tolerance))
	(setq unique-x (vl-sort unique-x '<))
	(setq unique-y (vl-sort unique-y '>))

	; Xuat theo luoi
	(setq grid-row 0 excel-row startrow)
	(foreach y-coord unique-y
		(setq grid-col 0 excel-col startcol)
		(foreach x-coord unique-x
			(setq txt-at-pos (E5-FIND-TEXT-AT-GRID x-coord y-coord textlist tolerance))
			(if txt-at-pos
				(SETCELLTEXT xlcells excel-row excel-col (car txt-at-pos))
			)
			(setq grid-col (+ grid-col 1) excel-col (+ excel-col 1))
		)
		(setq grid-row (+ grid-row 1) excel-row (+ excel-row 1))
	)
)

; Ham lay so hang cua mang
(defun E5-GET-ARRAY-ROWS ( textlist / unique-y tolerance all-y)
	(setq tolerance (atof *E6-tolerance*))
	(setq all-y '())
	(foreach txt textlist
		(setq all-y (cons (cadr (cadr txt)) all-y))
	)
	(setq unique-y (E5-GET-UNIQUE-COORDS all-y tolerance))
	(length unique-y)
)

; Ham lay cac toa do duy nhat
(defun E5-GET-UNIQUE-COORDS ( coord-list tolerance / unique-coords coord)
	(setq unique-coords '())
	(foreach coord coord-list
		(if (not (E5-COORD-EXISTS coord unique-coords tolerance))
			(setq unique-coords (cons coord unique-coords))
		)
	)
	unique-coords
)

; Ham kiem tra toa do da ton tai
(defun E5-COORD-EXISTS ( coord coord-list tolerance / exists)
	(setq exists nil)
	(foreach existing-coord coord-list
		(if (< (abs (- coord existing-coord)) tolerance)
			(setq exists T)
		)
	)
	exists
)

; Ham tim text tai vi tri luoi
(defun E5-FIND-TEXT-AT-GRID ( x y textlist tolerance / best-txt min-dist txt txt-pos dist)
	(setq best-txt nil min-dist 1e10)
	(foreach txt textlist
		(setq txt-pos (cadr txt))
		(setq dist (sqrt (+ (expt (- x (car txt-pos)) 2) (expt (- y (cadr txt-pos)) 2))))
		(if (and (< dist min-dist) (< dist tolerance))
			(progn
				(setq min-dist dist)
				(setq best-txt txt)
			)
		)
	)
	best-txt
)

; ===================================================================
; E6 - SETTINGS (THIET LAP TOAN CUC)
; ===================================================================

(defun C:E6 ( / choice input continue)
	(princ "\n=== THIET LAP CAD TO EXCEL ===")
	(princ (strcat "\nHandle: " *E6-handle*))
	(princ (strcat "\nFrame: " *E6-frame*))
	(princ (strcat "\nSymbol: " *E6-symbol*))
	(princ (strcat "\nFactor: " *E6-factor*))
	(princ (strcat "\nJump: " *E6-jump*))
	(princ (strcat "\nTolerance: " *E6-tolerance*))

	(setq continue T)
	(while continue
		(princ "\n\nThiet lap [Handle/Frame/Symbol/Factor/Jump/Tolerance] <thoat>: ")
		(initget "Handle Frame Symbol Factor Jump Tolerance H F S Fa J T")
		(setq choice (getkword ""))

		(cond
			; Handle
			((or (= choice "Handle") (= choice "H"))
				(initget "Y N")
				(setq input (getkword (strcat "\nGhi handle vao comment [Y/N] <" *E6-handle* ">: ")))
				(if input (setq *E6-handle* input))
				(princ (strcat "\nHandle: " *E6-handle*))
			)
			; Frame
			((or (= choice "Frame") (= choice "F"))
				(initget "Y N")
				(setq input (getkword (strcat "\nDong khung [Y/N] <" *E6-frame* ">: ")))
				(if input (setq *E6-frame* input))
				(princ (strcat "\nFrame: " *E6-frame*))
			)
			; Symbol
			((or (= choice "Symbol") (= choice "S"))
				(setq input (getstring (strcat "\nKy tu noi <" *E6-symbol* ">: ")))
				(if (and input (> (strlen input) 0)) (setq *E6-symbol* input))
				(princ (strcat "\nSymbol: " *E6-symbol*))
				(if (= *E6-symbol* "+")
					(princ "\nLuu y: Ky tu '+' se tao cong thuc Excel")
				)
			)
			; Factor
			((or (= choice "Factor") (= choice "Fa"))
				(setq input (getstring (strcat "\nHe so <" *E6-factor* ">: ")))
				(if (and input (> (strlen input) 0)) (setq *E6-factor* input))
				(princ (strcat "\nFactor: " *E6-factor*))
			)
			; Jump
			((or (= choice "Jump") (= choice "J"))
				(setq input (getstring (strcat "\nSo o nhay <" *E6-jump* ">: ")))
				(if (and input (> (strlen input) 0)) (setq *E6-jump* input))
				(princ (strcat "\nJump: " *E6-jump*))
			)
			; Tolerance
			((or (= choice "Tolerance") (= choice "T"))
				(setq input (getstring (strcat "\nSai so cho phep <" *E6-tolerance* ">: ")))
				(if (and input (> (strlen input) 0)) (setq *E6-tolerance* input))
				(princ (strcat "\nTolerance: " *E6-tolerance*))
			)
			; Thoat
			((= choice nil) (setq continue nil))
			; Khac
			(T (princ "\nLua chon khong hop le!"))
		)
	)
	(princ "\n=== DA LUU THIET LAP ===")
	(princ)
)

; ===================================================================
; ET - LENH TONG HOP TOI UU
; ===================================================================

(defun C:ET ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	mode
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	; Chon doi tuong
	(princ "\nChon doi tuong: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Hien thi menu chinh
			(princ "\n=== CAD TO EXCEL ===")
			(princ "\nChon che do:")
			(princ "\n1. Table - Xuat bang")
			(princ "\n2. Col - Xuat theo cot")
			(princ "\n3. Row - Xuat theo hang")
			(princ "\n4. Cell - Xuat vao 1 o")
			(princ "\n5. Array - Xuat theo mang")
			(princ "\n6. Handle - Chi ghi handle")
			(princ "\n7. Value - Ghi gia tri + handle")
			(setq mode (getint "\nNhap lua chon [1-7]: "))

			(cond
				; Table mode
				((= mode 1) (ET-EXECUTE-TABLE otcontents xlcells startrow startcol))
				; Col mode
				((= mode 2) (ET-EXECUTE-COL otcontents xlcells startrow startcol))
				; Row mode
				((= mode 3) (ET-EXECUTE-ROW otcontents xlcells startrow startcol))
				; Cell mode
				((= mode 4) (ET-EXECUTE-CELL otcontents xlcells startrow startcol))
				; Array mode
				((= mode 5) (ET-EXECUTE-ARRAY otcontents xlcells startrow startcol))
				; Handle mode
				((= mode 6) (ET-EXECUTE-HANDLE otcontents xlcells startrow startcol))
				; Value mode
				((= mode 7) (ET-EXECUTE-VALUE otcontents xlcells startrow startcol))
				; Lua chon khong hop le
				(T (princ "\nLua chon khong hop le!"))
			)
		)
		(princ "\nKhong chon duoc doi tuong!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; ===================================================================
; HAM PHU TRO CHO ET
; ===================================================================

; Ham thuc hien Table mode
(defun ET-EXECUTE-TABLE ( ss xlcells startrow startcol / textlist newrow)
	(setq textlist (CONVERT-SS-TO-TEXTLIST ss))
	(setq newcol startcol)
	(foreach txt textlist
		(SETCELLTEXT xlcells startrow newcol (car txt))
		(setq newcol (+ newcol 1))
	)
	(setq newrow (+ startrow (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
)

; Ham thuc hien Col mode
(defun ET-EXECUTE-COL ( ss xlcells startrow startcol / textlist sorted-textlist newrow)
	(setq textlist (CONVERT-SS-TO-TEXTLIST ss))
	(setq sorted-textlist (vl-sort textlist '(lambda (a b) (> (cadr (cadr a)) (cadr (cadr b))))))
	(setq newrow startrow)
	(foreach txt sorted-textlist
		(SETCELLTEXT xlcells newrow startcol (car txt))
		(setq newrow (+ newrow 1))
	)
	(setq newrow (+ newrow (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
)

; Ham thuc hien Row mode
(defun ET-EXECUTE-ROW ( ss xlcells startrow startcol / textlist sorted-textlist newcol newrow)
	(setq textlist (CONVERT-SS-TO-TEXTLIST ss))
	(setq sorted-textlist (vl-sort textlist '(lambda (a b) (< (car (cadr a)) (car (cadr b))))))
	(setq newcol startcol)
	(foreach txt sorted-textlist
		(SETCELLTEXT xlcells startrow newcol (car txt))
		(setq newcol (+ newcol 1))
	)
	(setq newrow (+ startrow (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow newcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa newcol)))
)

; Ham thuc hien Cell mode
(defun ET-EXECUTE-CELL ( ss xlcells startrow startcol / textlist result-text newrow)
	(setq textlist (CONVERT-SS-TO-TEXTLIST ss))
	(if (= *E6-symbol* "+")
		(setq result-text (ET-CREATE-FORMULA textlist))
		(setq result-text (ET-JOIN-TEXT textlist))
	)
	(SETCELLTEXT xlcells startrow startcol result-text)
	(setq newrow (+ startrow 1 (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
)

; Ham thuc hien Array mode
(defun ET-EXECUTE-ARRAY ( ss xlcells startrow startcol / textlist newrow)
	(setq textlist (CONVERT-SS-TO-TEXTLIST ss))
	(E5-EXPORT-ARRAY textlist xlcells startrow startcol)
	(setq newrow (+ startrow (E5-GET-ARRAY-ROWS textlist) (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
)

; Ham thuc hien Handle mode
(defun ET-EXECUTE-HANDLE ( ss xlcells startrow startcol / handlelist commenttext dwgpath dwgname newrow)
	(setq dwgpath (vla-get-fullname ActDoc)
		  dwgname (vl-filename-base dwgpath))
	(setq handlelist (GET-HANDLES ss))
	(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
	(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
	(WRITE-COMMENT-TO-CELL targetcell commenttext)
	(setq newrow (+ startrow (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Da ghi " (itoa (length handlelist)) " handle"))
)

; Ham thuc hien Value mode
(defun ET-EXECUTE-VALUE ( ss xlcells startrow startcol / celltext handlelist commenttext dwgpath dwgname newrow)
	(setq dwgpath (vla-get-fullname ActDoc)
		  dwgname (vl-filename-base dwgpath))
	(setq celltext (ET-CREATE-CELL-TEXT ss))
	(setq handlelist (GET-HANDLES ss))
	(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))

	(SETCELLTEXT xlcells startrow startcol celltext)
	(if (= *E6-handle* "Y")
		(progn
			(setq targetcell (vlax-get-property xlcells "Item" startrow startcol))
			(WRITE-COMMENT-TO-CELL targetcell commenttext)
		)
	)
	(setq newrow (+ startrow (atoi *E6-jump*)))
	(MOVE-CURSOR xlcells newrow startcol)
	(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
)

; Ham tao cong thuc cho ET
(defun ET-CREATE-FORMULA ( textlist / result)
	(setq result (strcat "=" (car (car textlist))))
	(foreach txt (cdr textlist)
		(setq result (strcat result "+" (car txt)))
	)
	result
)

; Ham noi text cho ET
(defun ET-JOIN-TEXT ( textlist / result)
	(setq result (car (car textlist)))
	(foreach txt (cdr textlist)
		(setq result (strcat result *E6-symbol* (car txt)))
	)
	result
)

; Ham tao noi dung cell cho ET
(defun ET-CREATE-CELL-TEXT ( ss / textlist result)
	(setq textlist (CONVERT-SS-TO-TEXTLIST ss))
	(if (= *E6-symbol* "+")
		(setq result (ET-CREATE-FORMULA textlist))
		(setq result (ET-JOIN-TEXT textlist))
	)
	result
)

; ===================================================================
; KET THUC FILE
; ===================================================================

(princ "\n=== CAD TO EXCEL - PHIEN BAN MOI ===")
(princ "\nCac lenh:")
(princ "\n  E1 - Xuat text/dim vao cell (co he so)")
(princ "\n  E2 - Xuat text/dim vao cell (text giu nguyen)")
(princ "\n  E3 - Chi ghi handle vao comment")
(princ "\n  E4 - Xuat table vao Excel")
(princ "\n  E5 - Xuat theo 3 che do (Col/Row/Array)")
(princ "\n  E6 - Thiet lap toan cuc")
(princ "\n  ET - Lenh tong hop toi uu")
(princ "\n=== LOADED ===")
(princ)
