; Fix Unicode escape sequences trong AutoCAD MTEXT
; Xu ly truong hop: T\U+1ED4NG → Tổng, S\U+1ED1 l\U+01B0\U+1EE3ng → <PERSON><PERSON> lượng

; <PERSON> chuyen doi Unicode escape sequences sang ky tu tieng Viet
(defun INIT-UNICODE-TABLE ( / unicode-table)
	(setq unicode-table (list
		; Cac ky tu co dau thuong gap
		(list "\\U+1EA3" "ả")  ; a hook above
		(list "\\U+1EA1" "ạ")  ; a dot below
		(list "\\U+1EA5" "ấ")  ; a circumflex acute
		(list "\\U+1EA7" "ầ")  ; a circumflex grave
		(list "\\U+1EA9" "ẩ")  ; a circumflex hook above
		(list "\\U+1EAB" "ẫ")  ; a circumflex tilde
		(list "\\U+1EAD" "ậ")  ; a circumflex dot below
		(list "\\U+1EAF" "ắ")  ; a breve acute
		(list "\\U+1EB1" "ằ")  ; a breve grave
		(list "\\U+1EB3" "ẳ")  ; a breve hook above
		(list "\\U+1EB5" "ẵ")  ; a breve tilde
		(list "\\U+1EB7" "ặ")  ; a breve dot below
		
		(list "\\U+1EBB" "ẻ")  ; e hook above
		(list "\\U+1EB9" "ẹ")  ; e dot below
		(list "\\U+1EBD" "ẽ")  ; e tilde
		(list "\\U+1EBF" "ế")  ; e circumflex acute
		(list "\\U+1EC1" "ề")  ; e circumflex grave
		(list "\\U+1EC3" "ể")  ; e circumflex hook above
		(list "\\U+1EC5" "ễ")  ; e circumflex tilde
		(list "\\U+1EC7" "ệ")  ; e circumflex dot below
		
		(list "\\U+1EC9" "ỉ")  ; i hook above
		(list "\\U+1ECB" "ị")  ; i dot below
		
		(list "\\U+1ECD" "ọ")  ; o dot below
		(list "\\U+1ECF" "ỏ")  ; o hook above
		(list "\\U+1ED1" "ố")  ; o circumflex acute
		(list "\\U+1ED3" "ồ")  ; o circumflex grave
		(list "\\U+1ED5" "ổ")  ; o circumflex hook above
		(list "\\U+1ED7" "ỗ")  ; o circumflex tilde
		(list "\\U+1ED9" "ộ")  ; o circumflex dot below
		(list "\\U+1EDB" "ớ")  ; o horn acute
		(list "\\U+1EDD" "ờ")  ; o horn grave
		(list "\\U+1EDF" "ở")  ; o horn hook above
		(list "\\U+1EE1" "ỡ")  ; o horn tilde
		(list "\\U+1EE3" "ợ")  ; o horn dot below
		
		(list "\\U+1EE5" "ụ")  ; u dot below
		(list "\\U+1EE7" "ủ")  ; u hook above
		(list "\\U+1EE9" "ứ")  ; u horn acute
		(list "\\U+1EEB" "ừ")  ; u horn grave
		(list "\\U+1EED" "ử")  ; u horn hook above
		(list "\\U+1EEF" "ữ")  ; u horn tilde
		(list "\\U+1EF1" "ự")  ; u horn dot below
		
		(list "\\U+1EF3" "ỳ")  ; y grave
		(list "\\U+1EF5" "ỵ")  ; y dot below
		(list "\\U+1EF7" "ỷ")  ; y hook above
		(list "\\U+1EF9" "ỹ")  ; y tilde
		
		; Cac ky tu hoa
		(list "\\U+1EA2" "Ả")  ; A hook above
		(list "\\U+1EA0" "Ạ")  ; A dot below
		(list "\\U+1EA4" "Ấ")  ; A circumflex acute
		(list "\\U+1EA6" "Ầ")  ; A circumflex grave
		(list "\\U+1EA8" "Ẩ")  ; A circumflex hook above
		(list "\\U+1EAA" "Ẫ")  ; A circumflex tilde
		(list "\\U+1EAC" "Ậ")  ; A circumflex dot below
		(list "\\U+1EAE" "Ắ")  ; A breve acute
		(list "\\U+1EB0" "Ằ")  ; A breve grave
		(list "\\U+1EB2" "Ẳ")  ; A breve hook above
		(list "\\U+1EB4" "Ẵ")  ; A breve tilde
		(list "\\U+1EB6" "Ặ")  ; A breve dot below
		
		(list "\\U+1EBA" "Ẻ")  ; E hook above
		(list "\\U+1EB8" "Ẹ")  ; E dot below
		(list "\\U+1EBC" "Ẽ")  ; E tilde
		(list "\\U+1EBE" "Ế")  ; E circumflex acute
		(list "\\U+1EC0" "Ề")  ; E circumflex grave
		(list "\\U+1EC2" "Ể")  ; E circumflex hook above
		(list "\\U+1EC4" "Ễ")  ; E circumflex tilde
		(list "\\U+1EC6" "Ệ")  ; E circumflex dot below
		
		(list "\\U+1EC8" "Ỉ")  ; I hook above
		(list "\\U+1ECA" "Ị")  ; I dot below
		
		(list "\\U+1ECC" "Ọ")  ; O dot below
		(list "\\U+1ECE" "Ỏ")  ; O hook above
		(list "\\U+1ED0" "Ố")  ; O circumflex acute
		(list "\\U+1ED2" "Ồ")  ; O circumflex grave
		(list "\\U+1ED4" "Ổ")  ; O circumflex hook above
		(list "\\U+1ED6" "Ỗ")  ; O circumflex tilde
		(list "\\U+1ED8" "Ộ")  ; O circumflex dot below
		(list "\\U+1EDA" "Ớ")  ; O horn acute
		(list "\\U+1EDC" "Ờ")  ; O horn grave
		(list "\\U+1EDE" "Ở")  ; O horn hook above
		(list "\\U+1EE0" "Ỡ")  ; O horn tilde
		(list "\\U+1EE2" "Ợ")  ; O horn dot below
		
		(list "\\U+1EE4" "Ụ")  ; U dot below
		(list "\\U+1EE6" "Ủ")  ; U hook above
		(list "\\U+1EE8" "Ứ")  ; U horn acute
		(list "\\U+1EEA" "Ừ")  ; U horn grave
		(list "\\U+1EEC" "Ử")  ; U horn hook above
		(list "\\U+1EEE" "Ữ")  ; U horn tilde
		(list "\\U+1EF0" "Ự")  ; U horn dot below
		
		(list "\\U+1EF2" "Ỳ")  ; Y grave
		(list "\\U+1EF4" "Ỵ")  ; Y dot below
		(list "\\U+1EF6" "Ỷ")  ; Y hook above
		(list "\\U+1EF8" "Ỹ")  ; Y tilde
		
		; Cac ky tu dac biet khac
		(list "\\U+0110" "Đ")  ; D with stroke
		(list "\\U+0111" "đ")  ; d with stroke
		(list "\\U+01A0" "Ơ")  ; O with horn
		(list "\\U+01A1" "ơ")  ; o with horn
		(list "\\U+01AF" "Ư")  ; U with horn
		(list "\\U+01B0" "ư")  ; u with horn
	))
	unicode-table
)

; Ham chuyen doi Unicode escape sequences
(defun CONVERT-UNICODE-ESCAPES (text-string / result unicode-table i escape-seq vietnamese-char)
	(setq result text-string)
	(setq unicode-table (INIT-UNICODE-TABLE))
	
	; Duyet qua tung Unicode escape sequence trong bang
	(foreach unicode-pair unicode-table
		(setq escape-seq (nth 0 unicode-pair))
		(setq vietnamese-char (nth 1 unicode-pair))
		
		; Thay the tat ca cac escape sequence trong text
		(while (vl-string-search escape-seq result)
			(setq result (vl-string-subst vietnamese-char escape-seq result))
		)
	)
	
	result
)

; Ham xu ly text table co Unicode escape sequences
(defun CLEAN-TABLE-TEXT-WITH-UNICODE (text-string / result)
	(if (and text-string (= (type text-string) 'STR) (> (strlen text-string) 0))
		(progn
			(setq result text-string)
			
			; Buoc 1: Chuyen doi Unicode escape sequences
			(setq result (CONVERT-UNICODE-ESCAPES result))
			
			; Buoc 2: Xu ly format codes binh thuong
			(setq result (SIMPLE-REMOVE-FORMAT-CODES result))
			
			; Buoc 3: Loai bo dau ngoac nhon
			(setq result (vl-string-subst "" "{" result))
			(setq result (vl-string-subst "" "}" result))
			
			; Buoc 4: Chuyen doi ky hieu dac biet
			(setq result (vl-string-subst "Ø" "%%c" result))
			(setq result (vl-string-subst "Ø" "%%C" result))
			(setq result (vl-string-subst "±" "%%p" result))
			(setq result (vl-string-subst "±" "%%P" result))
			(setq result (vl-string-subst "°" "%%d" result))
			(setq result (vl-string-subst "°" "%%D" result))
			
			; Buoc 5: Trim
			(while (and (> (strlen result) 0) (= (substr result 1 1) " "))
				(setq result (substr result 2))
			)
			(while (and (> (strlen result) 0) (= (substr result (strlen result) 1) " "))
				(setq result (substr result 1 (1- (strlen result))))
			)
			
			result
		)
		""
	)
)

; Ham SIMPLE-REMOVE-FORMAT-CODES (copy tu file chinh)
(defun SIMPLE-REMOVE-FORMAT-CODES (text-string / result start-pos end-pos after-semicolon)
	(setq result text-string)
	
	; Loai bo pattern: \[letter][number]; (co the co spaces sau semicolon)
	(while (setq start-pos (vl-string-search "\\" result))
		; Tim semicolon sau backslash
		(setq end-pos (vl-string-search ";" result start-pos))
		
		(if end-pos
			(progn
				; Tim vi tri sau semicolon va spaces
				(setq after-semicolon (+ end-pos 1))
				
				; Bo qua cac spaces sau semicolon
				(while (and (< after-semicolon (strlen result))
							(= (substr result (+ after-semicolon 1) 1) " "))
					(setq after-semicolon (+ after-semicolon 1))
				)
				
				; Loai bo tu backslash den het spaces
				(setq result (strcat 
					(substr result 1 start-pos)
					(substr result (+ after-semicolon 1))
				))
			)
			; Neu khong co semicolon, loai bo het tu backslash
			(setq result (substr result 1 start-pos))
		)
	)
	
	result
)

; Ham test Unicode conversion
(defun C:TEST-UNICODE ( / test-cases i input output)
	(princ "\n=== TEST UNICODE CONVERSION ===")
	
	; Test cases tu hinh anh
	(setq test-cases (list
		"T\\U+1ED4NG"
		"S\\U+1ED1 l\\U+01B0\\U+1EE3ng"
		"\\U+0110\\U+1EA6U \\U+0110O pH"
		"ATTRIBUTE"
		"Block"
	))
	
	(setq i 0)
	(foreach test-case test-cases
		(setq i (+ i 1))
		(setq input test-case)
		(setq output (CLEAN-TABLE-TEXT-WITH-UNICODE input))
		(princ (strcat "\nTest " (itoa i) ":"))
		(princ (strcat "\nInput:  \"" input "\""))
		(princ (strcat "\nOutput: \"" output "\""))
		(princ "\n---")
	)
	
	(princ "\n=== TEST COMPLETED ===")
	(princ)
)

; Ham test voi input tu user
(defun C:TEST-UNICODE-INPUT ( / user-input result)
	(princ "\n=== TEST UNICODE INPUT ===")
	
	(setq user-input (getstring T "\nNhap text co Unicode escape: "))
	(if user-input
		(progn
			(setq result (CLEAN-TABLE-TEXT-WITH-UNICODE user-input))
			(princ (strcat "\nInput:  \"" user-input "\""))
			(princ (strcat "\nOutput: \"" result "\""))
		)
		(princ "\nKhong nhap gi!")
	)
	(princ)
)

(princ "\n=== UNICODE FIX LOADED ===")
(princ "\nXu ly Unicode escape sequences trong AutoCAD MTEXT")
(princ "\nVi du: T\\U+1ED4NG → Tổng")
(princ "\nVi du: S\\U+1ED1 l\\U+01B0\\U+1EE3ng → Số lượng")
(princ "\nCac lenh:")
(princ "\n- TEST-UNICODE: Test voi cac truong hop mau")
(princ "\n- TEST-UNICODE-INPUT: Test voi input tu ban")
(princ)
