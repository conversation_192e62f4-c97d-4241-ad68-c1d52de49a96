Public Sub ZoomToHandleFromCellOrComment()
    Dim cellValue As String
    Dim commentText As String
    Dim handleText As String
    Dim handleArray() As String
    Dim cleanHandle As String
    Dim i As Integer
    Dim foundCount As Integer
    Dim oBlkRef As Object
    Dim filePathPos As Long
    
    On Error Resume Next
    
    ' Kiem tra xem cell co comment hay khong
    If Not ActiveCell.Comment Is Nothing Then
        commentText = ActiveCell.Comment.text
        
        ' Neu co FileCad trong comment, chi lay phan handle phia truoc
        filePathPos = InStr(1, commentText, "FileCad:", vbTextCompare)
        If filePathPos > 0 Then
            handleText = Trim(Left(commentText, filePathPos - 1))
        Else
            handleText = Trim(commentText)
        End If
    Else
        handleText = ""
    End If
    
    ' Neu comment khong co handle, lay tu cell
    If handleText = "" Then
        handleText = Trim(ActiveCell.Value)
        
        ' Neu co FileCad trong cell, chi lay phan handle phia truoc
        filePathPos = InStr(1, handleText, "FileCad:", vbTextCompare)
        If filePathPos > 0 Then
            handleText = Trim(Left(handleText, filePathPos - 1))
        End If
    End If
    
    ' Kiem tra xem co gia tri hay khong
    If handleText = "" Then
        MsgBox "Khong tim thay ma handle trong o hoac comment.", vbInformation
        Exit Sub
    End If
    
    ' Ket noi toi AutoCAD
    Call ConnectAutocad
    
    If Err.Number <> 0 Then
        MsgBox "Loi ket noi den AutoCAD: " & Err.Description, vbExclamation
        Exit Sub
    End If
    
    If AcadApp Is Nothing Then
        MsgBox "Khong the ket noi den AutoCAD. AcadApp la Nothing.", vbExclamation
        Exit Sub
    End If
    
    If AcadDoc Is Nothing Then
        MsgBox "Khong the ket noi den AutoCAD Document. AcadDoc la Nothing.", vbExclamation
        Exit Sub
    End If
    
    ' Kich hoat cua so AutoCAD
    AppActivate AcadApp.Caption
    
    ' Tach chuoi handle neu co nhieu handle
    handleArray = Split(handleText, ";")
    
    ' Tao mang handle cho ZoomToObjects
    ReDim ArHandle(1 To UBound(handleArray) + 1, 1 To 1)
    foundCount = 0
    
    ' Kiem tra tung handle va them vao mang
    For i = 0 To UBound(handleArray)
        ' Lam sach handle (loai bo khoang trang va dau ')
        cleanHandle = Trim(handleArray(i))
        
        If Left(cleanHandle, 1) = "'" Then
            cleanHandle = Mid(cleanHandle, 2)
        End If
        cleanHandle = Trim(cleanHandle)
        
        ' Bo qua neu handle trong
        If cleanHandle = "" Then
            GoTo NextIteration
        End If
        
        ' Them vao mang
        ArHandle(i + 1, 1) = cleanHandle
        
        ' Kiem tra xem handle co hop le khong
        Err.Clear
        Set oBlkRef = AcadDoc.HandleToObject(cleanHandle)
        
        If Err.Number = 0 Then
            foundCount = foundCount + 1
        End If
        
NextIteration:
    Next i
    
    ' Neu co doi tuong duoc tim thay
    If foundCount > 0 Then
        ' Tao mang moi chi voi handles hop le
        ReDim Preserve ArHandle(1 To foundCount, 1 To 1)
        
        ' Thu zoom bang cach truc tiep voi tung doi tuong
        For i = 1 To foundCount
            Err.Clear
            Set oBlkRef = AcadDoc.HandleToObject(ArHandle(i, 1))
            
            If Err.Number = 0 Then
                ' Lay bounding box va zoom
                Dim minPoint As Variant, maxPoint As Variant
                oBlkRef.GetBoundingBox minPoint, maxPoint
                
                ' Danh dau doi tuong
                oBlkRef.Highlight True
                
                ' Zoom den doi tuong
                AcadDoc.Application.ZoomWindow minPoint, maxPoint
                AcadApp.ZoomScaled 0.8, acZoomScaledRelative
            End If
        Next i
        
        AcadDoc.Utility.Prompt vbCrLf & "Da zoom den " & foundCount & " doi tuong"
    Else
        MsgBox "Khong tim thay doi tuong nao voi cac handle da cung cap.", vbExclamation
    End If
End Sub

Public Sub ZoomToHandleFromCellOrComment_ClickHandler(control As IRibbonControl)
    Call ZoomToHandleFromCellOrComment
End Sub
