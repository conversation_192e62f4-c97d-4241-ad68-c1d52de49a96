***************************************************************

(defun C:tq(/ lst fn fw i j d)

(princ "\nChon cac Text/Mtext/Dimension can xuat ra file...")

(setq lst (mapcar 'entget (acet-ss-to-list (ssget '((0 . "*TEXT,DIMENSION,*LINE")))))

     fn (getfiled "Chon file de save" "" "csv" 1)

     fw (open fn "w") i 0 j 0 d 0)

(foreach n lst

(princ

  (cond

   ((wcmatch (cdadr n) "*TEXT")(strcat (acet-dxf 1 n) ";Text" (itoa (setq i (1+ i))) "\n"))

   ((= (cdadr n) "DIMENSION")(strcat (if (= (acet-dxf 1 n) "")(rtos (acet-dxf 42 n))(acet-dxf 1 n))  ";Dim" (itoa (setq j (1+ j))) "\n"))
   ((wcmatch (cdadr n) "*LINE") (strcat (rtos (vlax-curve-getdistatparam (cdar n) (vlax-curve-getendparam  (cdar n))) ) ";*Line" (itoa (setq d (1+ d))) "\n"))

  )

   fw

  )

  )


(close fw)
    (command "._ai_editcustfile" fn))

***************************************************************

(defun c:tkt(/ ent h height i len0 lst msp pt row ss str str0 str_len tblobj width0 width1); thong ke text
;;  By : Gia Bach, Copyrightｩ December 2010                    ;;
;;  Contact : gia_bach @  www.CadViet.com                      ;;
  (defun TxtWidth (val msp / txt minp maxp)
    (vla-getBoundingBox (setq txt (vla-AddText msp val (vlax-3d-point '(0 0 0)) 1)) 'minp 'maxp)
    (vla-Erase txt)
    (-(car(vlax-safearray->list maxp))(car(vlax-safearray->list minp)))  )
  ;main
  (if (> (atof (substr (getvar "ACADVER") 1 4)) 16.0)
    (progn
      (vl-load-com)
      (princ "\nChon cac Text de thong ke :")
      (if (setq ss (ssget(list (cons 0 "*TEXT"))))
	(progn
	  (setq i -1 len0 8)
	  (while (setq ent (ssname ss (setq i (1+ i))))
	    (setq str(cdr(assoc 1 (entget ent ))))
	    (if (> (setq str_len (strlen str)) len0)
	      (setq str0 str len0 str_len) )
	    (if (not (assoc str lst))
	      (setq lst (cons (cons str 1) lst))
	      (setq lst (subst (cons str (1+ (cdr (assoc str lst))))
			       (assoc str lst) lst)))	    )
	  (setq lst (vl-sort lst '(lambda (x y) (< (car x) (car y))))
		msp (vla-get-modelspace (vla-get-activedocument (vlax-get-acad-object))))
	  (or *h* (setq *h* 175))
	  (initget 6)
	  (setq h (getreal (strcat "\nChieu cao chu <" (rtos *h*) "> :")))
	  (if h (setq *h* h) (setq h *h*) )
	  (setq width0 (* 3 h(TxtWidth "STT" msp))
		height (* 2 h))
	  (if str0
	    (setq width1 (* 1.2 h(TxtWidth (strcase str0) msp)))
	    (setq width1 (* 2 h(TxtWidth "Gia tri" msp))))
	  (if (> h 3)
	    (setq width0 (* (fix (/ width0 10))10)
		  width1 (* (fix (/ width1 10))10)
		  height (* (fix (/ height 5))5)))
	  ; Tinh tong so luong
	  (setq total-count (apply '+ (mapcar 'cdr lst)))
	  (setq pt (getpoint "\nDiem dat Bang :")
		TblObj (vla-addtable msp (vlax-3d-point pt) (+ (length lst) 3) 3 height width1))
	  (vla-put-regeneratetablesuppressed TblObj :vlax-true)
	  (vla-put-vertcellmargin TblObj (* 0.25 h))
	  (vla-put-horzcellmargin TblObj (* 0.75 h))
	  (vla-SetColumnWidth TblObj 0 width0)
	  (vla-SetColumnWidth TblObj 2 (* 2 h(TxtWidth "So luong" msp)))
	  (mapcar '(lambda (x)(vla-setTextHeight TblObj x h))
		  (list acTitleRow acHeaderRow acDataRow) )
	  (mapcar '(lambda (x)(vla-setAlignment TblObj x 8))
		  (list acTitleRow acHeaderRow acDataRow))
	  (vl-catch-all-error-p (vl-catch-all-apply (function(lambda () (vla-MergeCells TblObj 0 0 0 2)) )))
	  (vla-setText TblObj 0 0 "Bang thong ke")
	  (vla-setText TblObj 1 0 "STT")
	  (vla-setText TblObj 1 1 "Gia tri")
	  (vla-setText TblObj 1 2 "So luong")
	  (setq i 1 row 2 )
	  (foreach e lst
	    (vla-setText TblObj row 0 (itoa i))
	    (vla-setText TblObj row 1 (car e))
	    (vla-setText TblObj row 2 (itoa (cdr e)))
	    (vla-SetCellAlignment TblObj row 1 7)
	    (vla-SetCellAlignment TblObj row 2 9)
	    (setq row (1+ row) i (1+ i))	)
	  ; Them hang tong cong
	  (vla-setText TblObj row 0 "")
	  (vla-setText TblObj row 1 "TONG CONG")
	  (vla-setText TblObj row 2 (itoa total-count))
	  (vla-SetCellAlignment TblObj row 1 8)
	  (vla-SetCellAlignment TblObj row 2 9)
	  (vla-put-regeneratetablesuppressed TblObj :vlax-false)
	  (vlax-release-object TblObj)	  )
	(alert "Khong chon duoc Text.")    )
      (princ)  )
    (alert "\nPhien ban Cad cua ban khong ho tro tao Bang (TABLE)")   )  )
;;----------------------------------------------------------------
(defun c:tka  (/ are asoc ent i lay ls1 lst ss typ)
  (prompt "\nQuet chon cac doi tuong LWPOLYLINE, HATCH...!")
  (if (and (setq ss (ssget '((-4 . "<OR")
                             (-4 . "<AND")
                             (0 . "LWPOLYLINE")
                             (70 . 1)
                             (-4 . "AND>")
                             (-4 . "<AND")
                             (0 . "HATCH")
                             (-4 . "AND>")
                             (-4 . "OR>"))))
           (setq fn (getfiled "Nhap ten File" "" "csv" 1))
           (setq fw (open fn "w"))
           (setq # ";"))
    (progn (repeat (setq i (sslength ss))
             (setq ent (ssname ss (setq i (1- i)))
                   typ (cdr (assoc 0 (entget ent)))
                   tha (cdr (assoc 2 (entget ent)))
                   lay (cdr (assoc 8 (entget ent))))
             (if (setq are (vla-get-Area (vlax-ename->vla-object ent)))
               (cond ((eq typ "LWPOLYLINE")
                      (if (not (setq asoc (assoc (list typ lay) lst)))
                        (setq lst (cons (cons (list typ lay) are) lst))
                        (setq lst (subst (cons (list typ lay) (+ are (cdr asoc))) asoc lst))))
                     ((eq typ "HATCH")
                      (if (not (setq asoc (assoc (list typ tha) ls1)))
                        (setq ls1 (cons (cons (list typ tha) are) ls1))
                        (setq ls1 (subst (cons (list typ tha) (+ are (cdr asoc))) asoc ls1)))))))
           ;; To Csv
           (write-line (strcat "sep=" #) fw)
           (write-line (strcat "TT" # "LOAI" # "TEN LAYER, HATCH" # "GIA TRI") fw)
           ; Tinh tong dien tich
           (setq total-area (apply '+ (mapcar 'cdr (append ls1 lst))))
           (foreach x  (vl-sort (append ls1 lst) '(lambda (a b) (< (caar a) (caar b))))
             (write-line (apply 'strcat
                                (cons (strcat (itoa (1+ i)) #) (list (caar x) # (cadar x) # (rtos (cdr x) 2 3))))
                         fw)
             (setq i (1+ i)))
           ; Them dong tong cong
           (write-line (strcat "" # "TONG CONG" # "" # (rtos total-area 2 3)) fw)
           (close fw)))
  (princ))

;;;**********************************************************************


(defun c:tkb (/ blk_id blk_len blk_name blks cur_var ent h header_lsp height i
		 ins j len0 lst_blk msp pt row ss str tblobj width width1 width2 x y)
;;  By : Gia Bach, gia_bach @  www.CadViet.com             ;;
(defun TxtWidth (val h msp / txt minp maxp)
  (setq	txt (vla-AddText msp val (vlax-3d-point '(0 0 0)) h))
  (vla-getBoundingBox txt 'minp 'maxp )
  (vla-Erase txt)
  (-(car(vlax-safearray->list maxp))(car(vlax-safearray->list minp)))  )

(defun GetOrCreateTableStyle (tbl_name / name namelst objtblsty objtblstydic tablst txtsty)
  (setq objTblStyDic (vla-item (vla-get-dictionaries *adoc) "ACAD_TABLESTYLE") )
  (foreach itm (vlax-for itm objTblStyDic
		 (setq tabLst (append tabLst (list itm))))
    (if (not
	  (vl-catch-all-error-p
	    (setq name (vl-catch-all-apply 'vla-get-Name (list itm)))))
      (setq nameLst (append nameLst (list name)))  )  )
  (if (not (vl-position tbl_name nameLst))
    (vla-addobject objTblStyDic tbl_name "AcDbTableStyle"))
  (setq objTblSty (vla-item objTblStyDic tbl_name)
	TxtSty (variant-value (vla-getvariable *adoc "TextStyle")))
  (mapcar '(lambda (x)(vla-settextstyle objTblSty x TxtSty))
	      (list acTitleRow acHeaderRow acDataRow) )
  (vla-setvariable *adoc "CTableStyle" tbl_name) )

(defun GetObjectID (obj)
  (if (vl-string-search "64" (getenv "PROCESSOR_ARCHITECTURE"))
    (vlax-invoke-method
      (setq Utility
        (cond
	  (Utility)
          ((vla-get-Utility *adoc))))
      'GetObjectIdString obj :vlax-false )
    (vla-get-Objectid obj)))
;main
  (if (setq ss (ssget (list (cons 0 "INSERT"))))
    (progn
      (vl-load-com)
      (setq i -1 len0 8)
      (while (setq ent (ssname ss (setq i (1+ i))))
	(setq blk_name (vla-get-name (vlax-Ename->Vla-Object ent)))
	(if (> (setq blk_len (strlen blk_name)) len0)
	  (setq str blk_name len0 blk_len) )
	(if (not (assoc blk_name lst_blk))
	  (setq lst_blk (cons (cons blk_name 1) lst_blk))
	  (setq lst_blk (subst (cons blk_name (1+ (cdr (assoc blk_name lst_blk))))
			       (assoc blk_name lst_blk) lst_blk)))	    )
      (setq lst_blk (vl-sort lst_blk '(lambda (x y) (< (car x) (car y)) ) ))
      (setq cur_var (mapcar 'getvar '("DYNMODE" "DYNPROMPT")))
      (mapcar 'setvar '("DYNMODE" "DYNPROMPT") '(1 1))
      (initget "Yes No")
      (setq ins (getkword "\nChen ki hieu Block [Yes/No ] <yes> : ") )
      (or ins (setq ins "Yes"))
      (mapcar 'setvar '("DYNMODE" "DYNPROMPT") cur_var)
      (or *h* (setq *h* (* (getvar "dimtxt")(getvar "dimscale"))))
      (initget 6)
      (setq h (getreal (strcat "\nChieu cao chu <" (rtos *h*) "> :")))
      (if h (setq *h* h) (setq h *h*) )
      (setq *adoc (vla-get-ActiveDocument (vlax-get-acad-object))
	    msp (vla-get-modelspace *adoc)
	    blks (vla-get-blocks *adoc))
      (setq width1 (* 2 (TxtWidth "STT" h msp))
	    width (* 2 (TxtWidth "So luong" h msp))
	    height (* 2 h))
      (if str
	(setq width2 (* 1.5 (TxtWidth (strcase str) h msp)))
	(setq width2 width))
      (if (> h 3)
	(setq width (* (fix (/ width 10))10)
	      width1 (* (fix (/ width1 10))10)
	      width2 (* (fix (/ width2 10))10)
	      height (* (fix (/ height 5))5)))
      ; Tinh tong so luong block
      (setq total-blocks (apply '+ (mapcar 'cdr lst_blk)))
      (GetOrCreateTableStyle "CadViet")
      (setq pt (getpoint "\nDiem dat Bang :")
	    TblObj (vla-addtable msp (vlax-3d-point pt) (+ (length lst_blk) 3) 5 height width))
      (vla-put-regeneratetablesuppressed TblObj :vlax-true)
      (vla-SetColumnWidth TblObj 0 width1)
      (vla-SetColumnWidth TblObj 1 width2)
      (vla-put-vertcellmargin TblObj (* 0.75 h))
      (vla-put-horzcellmargin TblObj (* 0.75 h))
      (mapcar '(lambda (x)(vla-setTextHeight TblObj x h))
	      (list acTitleRow acHeaderRow acDataRow) )
      (mapcar '(lambda (x)(vla-setAlignment TblObj x 2))
	      (list acTitleRow acHeaderRow acDataRow))
      (vl-catch-all-error-p (vl-catch-all-apply (function(lambda () (vla-MergeCells TblObj 0 0 0 3)) )))
      (vla-setText TblObj 0 0 "Bang thong ke")
      (setq j -1 header_lsp (list "STT" "Ten" "Don vi" "So luong" "Ky hieu"))
      (repeat (length header_lsp)
	(vla-setText TblObj 1 (setq j (1+ j)) (nth j header_lsp)))
      (setq row 2 i 1)
      (foreach pt lst_blk
	(setq blk_name (car pt) j -1)
	(mapcar '(lambda (x)(vla-setText TblObj row (setq j (1+ j)) x))
		(list (itoa i) blk_name "cai" (itoa (cdr pt))))
	(if (= ins "Yes")
	  (vla-SetBlockTableRecordId TblObj row 4 (GetObjectID (vla-item blks blk_name)) :vlax-true))
	(vla-SetCellAlignment TblObj row 1 7)
	(vla-SetCellAlignment TblObj row 3 9)
	(setq row (1+ row) i (1+ i))	)
      ; Them hang tong cong
      (vla-setText TblObj row 0 "")
      (vla-setText TblObj row 1 "TONG CONG")
      (vla-setText TblObj row 2 "")
      (vla-setText TblObj row 3 (itoa total-blocks))
      (vla-setText TblObj row 4 "")
      (vla-SetCellAlignment TblObj row 1 8)
      (vla-SetCellAlignment TblObj row 3 9)
      (vla-put-regeneratetablesuppressed TblObj :vlax-false)
      (vlax-release-object TblObj) )  )
  (princ))

</yes>