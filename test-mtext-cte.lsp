; Test file cho cac ham MTEXT trong CTE
; Test cac ham CLEAN-MTEXT, REMOVE-FORMAT-CODES, REMOVE-BRACES, CONVERT-SPECIAL-SYMBOLS

; Load cac ham can thiet tu file CTE
(load "HNP_Cad Link Excel_CTE.lsp")

; Ham test CLEAN-MTEXT
(defun C:TEST-CLEAN-MTEXT ( / test1 test2 test3 test4 test5)
	(princ "\n=== TEST CLEAN-MTEXT FUNCTION ===")
	
	; Test 1: MTEXT voi format codes
	(setq test1 "{\\fArial|b0|i0|c0|p34;Hello \\C256;World}")
	(princ (strcat "\nTest 1 Input: " test1))
	(princ (strcat "\nTest 1 Output: '" (CLEAN-MTEXT test1) "'"))
	
	; Test 2: MTEXT voi alignment codes
	(setq test2 "\\A1;Center Text\\A0;")
	(princ (strcat "\nTest 2 Input: " test2))
	(princ (strcat "\nTest 2 Output: '" (CLEAN-MTEXT test2) "'"))
	
	; Test 3: Text voi ky hieu dac biet
	(setq test3 "Duong kinh %%c100mm va ±0.5")
	(princ (strcat "\nTest 3 Input: " test3))
	(princ (strcat "\nTest 3 Output: '" (CLEAN-MTEXT test3) "'"))
	
	; Test 4: MTEXT phuc tap
	(setq test4 "{\\fTimes New Roman|b1|i0|c0|p34;\\C1;Bold Text} Normal text %%d90")
	(princ (strcat "\nTest 4 Input: " test4))
	(princ (strcat "\nTest 4 Output: '" (CLEAN-MTEXT test4) "'"))
	
	; Test 5: Text binh thuong
	(setq test5 "Normal text without format")
	(princ (strcat "\nTest 5 Input: " test5))
	(princ (strcat "\nTest 5 Output: '" (CLEAN-MTEXT test5) "'"))
	
	(princ "\n=== TEST COMPLETED ===")
	(princ)
)

; Ham test cac ham thanh phan
(defun C:TEST-COMPONENTS ( / test-format test-braces test-symbols)
	(princ "\n=== TEST COMPONENT FUNCTIONS ===")
	
	; Test REMOVE-FORMAT-CODES
	(setq test-format "\\C256;Red text\\A1;center")
	(princ (strcat "\nREMOVE-FORMAT-CODES Input: " test-format))
	(princ (strcat "\nREMOVE-FORMAT-CODES Output: '" (REMOVE-FORMAT-CODES test-format) "'"))
	
	; Test REMOVE-BRACES
	(setq test-braces "{Bold text} normal {italic}")
	(princ (strcat "\nREMOVE-BRACES Input: " test-braces))
	(princ (strcat "\nREMOVE-BRACES Output: '" (REMOVE-BRACES test-braces) "'"))
	
	; Test CONVERT-SPECIAL-SYMBOLS
	(setq test-symbols "%%c diameter %%p plus-minus %%d degree")
	(princ (strcat "\nCONVERT-SPECIAL-SYMBOLS Input: " test-symbols))
	(princ (strcat "\nCONVERT-SPECIAL-SYMBOLS Output: '" (CONVERT-SPECIAL-SYMBOLS test-symbols) "'"))
	
	(princ "\n=== COMPONENT TEST COMPLETED ===")
	(princ)
)

; Ham test voi MTEXT that trong CAD
(defun C:TEST-REAL-MTEXT ( / ss ent entdata raw-text cleaned-text)
	(princ "\n=== TEST WITH REAL MTEXT ===")
	(princ "\nChon mot MTEXT object: ")
	(setq ss (ssget '((0 . "MTEXT"))))
	
	(if ss
		(progn
			(setq ent (ssname ss 0))
			(setq entdata (entget ent))
			(setq raw-text "")
			
			; Lay text tu DXF codes 1 va 3
			(foreach item entdata
				(if (= (car item) 1)
					(setq raw-text (strcat raw-text (cdr item)))
				)
				(if (= (car item) 3)
					(setq raw-text (strcat raw-text (cdr item)))
				)
			)
			
			; Lam sach text
			(setq cleaned-text (CLEAN-MTEXT raw-text))
			
			(princ (strcat "\nRaw MTEXT: " raw-text))
			(princ (strcat "\nCleaned text: " cleaned-text))
			(princ (strcat "\nLength before: " (itoa (strlen raw-text))))
			(princ (strcat "\nLength after: " (itoa (strlen cleaned-text))))
		)
		(princ "\nKhong co MTEXT nao duoc chon!")
	)
	
	(princ "\n=== REAL MTEXT TEST COMPLETED ===")
	(princ)
)

(princ "\nTest functions loaded:")
(princ "\n- TEST-CLEAN-MTEXT: Test ham CLEAN-MTEXT")
(princ "\n- TEST-COMPONENTS: Test cac ham thanh phan")
(princ "\n- TEST-REAL-MTEXT: Test voi MTEXT that trong CAD")
(princ)
