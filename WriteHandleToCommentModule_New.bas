Option Explicit

' Mo<PERSON><PERSON> doc lap de ghi handle tu doi tuong duoc chon ben CAD vao comment cua o Excel
' Tuong tu cach lam trong file 2025.txt
' PHIEN BAN HOAN TOAN DOC LAP - TRANH XUNG DOT VOI TAT CA CAC HAM KHAC
' Tat ca ham deu co prefix HandleCommentModule_ de tranh xung dot

' Bien toan cuc rieng cho module nay
Private HandleCommentModule_AcadApp As Object
Private HandleCommentModule_AcadDoc As Object

' Ham chinh de ghi handle vao comment
Public Sub WriteHandleToComment()
    Dim SsetObj As Object
    Dim ent As Object
    Dim targetCell As Range
    Dim handleText As String
    Dim filePathCad As String
    Dim i As Integer
    
    On Error Resume Next
    
    ' Ket noi den AutoCAD
    If Not HandleCommentModule_IsAutocadConnected() Then
        Call HandleCommentModule_ConnectAutocad
        If Not HandleCommentModule_IsAutocadConnected() Then
            MsgBox "Khong the ket noi den AutoCAD. Vui long kiem tra:" & vbCrLf & _
                   "1. AutoCAD da duoc cai dat" & vbCrLf & _
                   "2. AutoCAD dang chay" & vbCrLf & _
                   "3. Co file CAD dang mo", vbCritical
            Exit Sub
        End If
    End If
    
    ' Lay cell hien tai trong Excel (tu nhan dien vi tri con chuot)
    Set targetCell = ActiveCell
    If targetCell Is Nothing Then
        MsgBox "Khong xac dinh duoc cell de ghi comment", vbInformation
        Exit Sub
    End If
    
    ' Chuyen sang AutoCAD de chon doi tuong
    AppActivate HandleCommentModule_AcadApp.Caption
    
    ' Tao selection set de chon doi tuong
    Set SsetObj = HandleCommentModule_AcadDoc.SelectionSets.Add("HandleSelection-" & Now)
    If Err Then
        Err.Clear
        HandleCommentModule_AcadDoc.SelectionSets("HandleSelection-" & Now).Delete
        Set SsetObj = HandleCommentModule_AcadDoc.SelectionSets.Add("HandleSelection-" & Now)
    End If
    
    ' Yeu cau nguoi dung chon doi tuong
    HandleCommentModule_AcadDoc.Utility.Prompt vbCrLf & "Hay chon doi tuong de lay handle:"
    SsetObj.SelectOnScreen
    
    If SsetObj.Count = 0 Then
        MsgBox "Khong co doi tuong nao duoc chon", vbInformation
        GoTo CleanUp
    End If
    
    ' Lay duong dan file CAD
    filePathCad = ""
    If HandleCommentModule_AcadDoc.Path <> "" Then
        filePathCad = "FileCad: " & HandleCommentModule_AcadDoc.Path & "\" & HandleCommentModule_AcadDoc.Name
    End If
    
    ' Tao chuoi handle tu cac doi tuong da chon
    handleText = ""
    For Each ent In SsetObj
        If handleText <> "" Then handleText = handleText & "; "
        handleText = handleText & ent.Handle
    Next ent
    
    ' Them thong tin file CAD vao cuoi
    If filePathCad <> "" Then
        handleText = handleText & "; " & vbCrLf & filePathCad
    End If
    
    ' Quay ve Excel
    Call HandleCommentModule_ShowXLOnTop
    AppActivate Application.Caption
    
    ' Ghi handle vao comment cua cell
    Call HandleCommentModule_WriteHandleToCell(targetCell, handleText)
    
    ' Thong bao thanh cong
    HandleCommentModule_AcadDoc.Utility.Prompt vbCrLf & "Da ghi " & SsetObj.Count & " handle vao comment cua cell " & targetCell.Address
    
CleanUp:
    ' Don dep
    On Error Resume Next
    If Not SsetObj Is Nothing Then SsetObj.Delete
    Set SsetObj = Nothing
    
    ' Quay ve Excel
    Call HandleCommentModule_ShowXLOnTop
    AppActivate Application.Caption
End Sub

' Ham ghi handle vao comment cua cell
Private Sub HandleCommentModule_WriteHandleToCell(targetCell As Range, handleText As String)
    On Error Resume Next
    
    ' Xoa comment cu neu da co
    If Not targetCell.Comment Is Nothing Then
        targetCell.Comment.Delete
    End If
    
    ' Ghi comment moi neu co handle
    If handleText <> "" Then
        targetCell.AddComment Text:=CStr(handleText)
        
        ' Tuy chinh kich thuoc comment
        With targetCell.Comment
            .Shape.TextFrame.AutoSize = True
            .Shape.Width = 200
            .Shape.Height = 100
        End With
    End If
End Sub

' Ham ket noi den AutoCAD rieng cho module nay (tranh xung dot)
Private Sub HandleCommentModule_ConnectAutocad()
    On Error Resume Next
    
    ' Thu ket noi den AutoCAD da mo
    Set HandleCommentModule_AcadApp = GetObject(, "AutoCAD.Application")
    If Err Then
        Err.Clear
        ' Thu khoi dong AutoCAD neu chua mo
        Set HandleCommentModule_AcadApp = CreateObject("AutoCAD.Application")
        If Err Then
            MsgBox "Khong the ket noi hoac khoi dong AutoCAD. Hay dam bao AutoCAD da duoc cai dat.", vbCritical
            Set HandleCommentModule_AcadApp = Nothing
            Set HandleCommentModule_AcadDoc = Nothing
            Exit Sub
        End If
    End If
    
    ' Kiem tra xem AcadApp co hop le khong
    If HandleCommentModule_AcadApp Is Nothing Then
        MsgBox "Khong the khoi tao AutoCAD Application", vbCritical
        Exit Sub
    End If
    
    ' Lay document hien tai
    Set HandleCommentModule_AcadDoc = HandleCommentModule_AcadApp.ActiveDocument
    If Err Then
        Err.Clear
        MsgBox "Khong the lay ActiveDocument tu AutoCAD. Hay mo mot file CAD truoc.", vbInformation
        Set HandleCommentModule_AcadDoc = Nothing
        Exit Sub
    End If
    
    If HandleCommentModule_AcadDoc Is Nothing Then
        MsgBox "Khong co document nao dang mo trong AutoCAD. Hay mo mot file CAD truoc.", vbInformation
        Exit Sub
    End If
    
    ' Hien thi AutoCAD
    HandleCommentModule_AcadApp.Visible = True
    
    ' Thong bao ket noi thanh cong
    On Error GoTo 0
End Sub

' Ham kiem tra ket noi AutoCAD rieng cho module nay
Private Function HandleCommentModule_IsAutocadConnected() As Boolean
    On Error Resume Next
    HandleCommentModule_IsAutocadConnected = False
    
    If Not HandleCommentModule_AcadApp Is Nothing And Not HandleCommentModule_AcadDoc Is Nothing Then
        ' Thu truy cap mot thuoc tinh de kiem tra ket noi
        Dim testName As String
        testName = HandleCommentModule_AcadDoc.Name
        If Err = 0 Then
            HandleCommentModule_IsAutocadConnected = True
        End If
    End If
    
    On Error GoTo 0
End Function

' Ham hien thi Excel len tren rieng cho module nay (tranh xung dot)
Private Sub HandleCommentModule_ShowXLOnTop()
    On Error Resume Next
    ' Chi dat WindowState neu Excel dang bi an hoac minimize
    ' Khong thay doi neu Excel dang o che do Maximized
    If Application.WindowState = xlMinimized Then
        Application.WindowState = xlNormal
    End If
    Application.Visible = True
    AppActivate Application.Caption
End Sub

' Ham hien thi AutoCAD len tren rieng cho module nay
Private Sub HandleCommentModule_ShowCADOnTop()
    On Error Resume Next
    If Not HandleCommentModule_AcadApp Is Nothing Then
        HandleCommentModule_AcadApp.Visible = True
        AppActivate HandleCommentModule_AcadApp.Caption
    End If
End Sub

' Ham lay ten cot tu so cot rieng cho module nay (tranh xung dot)
Private Function HandleCommentModule_TimTenCot(columnNumber As Long) As String
    Dim result As String
    Dim temp As Long
    
    temp = columnNumber
    Do While temp > 0
        temp = temp - 1
        result = Chr(65 + (temp Mod 26)) & result
        temp = temp \ 26
    Loop
    
    HandleCommentModule_TimTenCot = result
End Function

' Ham ghi handle vao comment cho nhieu cell (mo rong)
Public Sub WriteHandleToMultipleCells()
    Dim selectedRange As Range
    Dim cell As Range
    Dim SsetObj As Object
    Dim ent As Object
    Dim handleText As String
    Dim filePathCad As String
    
    On Error Resume Next
    
    ' Lay vung cell da chon
    Set selectedRange = Selection
    If selectedRange Is Nothing Then
        MsgBox "Hay chon vung cell truoc khi thuc hien", vbInformation
        Exit Sub
    End If
    
    ' Ket noi den AutoCAD
    If Not HandleCommentModule_IsAutocadConnected() Then
        Call HandleCommentModule_ConnectAutocad
        If Not HandleCommentModule_IsAutocadConnected() Then
            MsgBox "Khong the ket noi den AutoCAD. Vui long kiem tra:" & vbCrLf & _
                   "1. AutoCAD da duoc cai dat" & vbCrLf & _
                   "2. AutoCAD dang chay" & vbCrLf & _
                   "3. Co file CAD dang mo", vbCritical
            Exit Sub
        End If
    End If
    
    ' Chuyen sang AutoCAD de chon doi tuong
    AppActivate HandleCommentModule_AcadApp.Caption
    
    ' Tao selection set de chon doi tuong
    Set SsetObj = HandleCommentModule_AcadDoc.SelectionSets.Add("HandleMultiSelection-" & Now)
    If Err Then
        Err.Clear
        HandleCommentModule_AcadDoc.SelectionSets("HandleMultiSelection-" & Now).Delete
        Set SsetObj = HandleCommentModule_AcadDoc.SelectionSets.Add("HandleMultiSelection-" & Now)
    End If
    
    ' Yeu cau nguoi dung chon doi tuong
    HandleCommentModule_AcadDoc.Utility.Prompt vbCrLf & "Hay chon doi tuong de lay handle cho nhieu cell:"
    SsetObj.SelectOnScreen
    
    If SsetObj.Count = 0 Then
        MsgBox "Khong co doi tuong nao duoc chon", vbInformation
        GoTo CleanUp2
    End If
    
    ' Lay duong dan file CAD
    filePathCad = ""
    If HandleCommentModule_AcadDoc.Path <> "" Then
        filePathCad = "FileCad: " & HandleCommentModule_AcadDoc.Path & "\" & HandleCommentModule_AcadDoc.Name
    End If
    
    ' Tao chuoi handle tu cac doi tuong da chon
    handleText = ""
    For Each ent In SsetObj
        If handleText <> "" Then handleText = handleText & "; "
        handleText = handleText & ent.Handle
    Next ent
    
    ' Them thong tin file CAD vao cuoi
    If filePathCad <> "" Then
        handleText = handleText & "; " & vbCrLf & filePathCad
    End If
    
    ' Quay ve Excel
    Call HandleCommentModule_ShowXLOnTop
    AppActivate Application.Caption
    
    ' Ghi handle vao comment cho tat ca cell trong vung chon
    For Each cell In selectedRange
        Call HandleCommentModule_WriteHandleToCell(cell, handleText)
    Next cell
    
    ' Thong bao thanh cong
    MsgBox "Da ghi " & SsetObj.Count & " handle vao comment cua " & selectedRange.Count & " cell", vbInformation
    
CleanUp2:
    ' Don dep
    On Error Resume Next
    If Not SsetObj Is Nothing Then SsetObj.Delete
    Set SsetObj = Nothing
    
    ' Quay ve Excel
    Call HandleCommentModule_ShowXLOnTop
    AppActivate Application.Caption
End Sub

' Ham xoa comment khoi cell hien tai
Public Sub ClearCommentFromCurrentCell()
    Dim targetCell As Range

    Set targetCell = ActiveCell
    If targetCell Is Nothing Then
        MsgBox "Khong xac dinh duoc cell hien tai", vbInformation
        Exit Sub
    End If

    On Error Resume Next
    If Not targetCell.Comment Is Nothing Then
        targetCell.Comment.Delete
        MsgBox "Da xoa comment khoi cell " & targetCell.Address, vbInformation
    Else
        MsgBox "Cell " & targetCell.Address & " khong co comment", vbInformation
    End If
End Sub

' Ham xoa comment khoi vung cell da chon
Public Sub ClearCommentFromSelectedCells()
    Dim selectedRange As Range
    Dim cell As Range
    Dim count As Integer

    Set selectedRange = Selection
    If selectedRange Is Nothing Then
        MsgBox "Hay chon vung cell truoc khi thuc hien", vbInformation
        Exit Sub
    End If

    count = 0
    On Error Resume Next
    For Each cell In selectedRange
        If Not cell.Comment Is Nothing Then
            cell.Comment.Delete
            count = count + 1
        End If
    Next cell

    MsgBox "Da xoa comment khoi " & count & " cell", vbInformation
End Sub

' Ham test ket noi AutoCAD cho module nay
Public Sub HandleCommentModule_TestAutocadConnection()
    On Error Resume Next

    MsgBox "Dang kiem tra ket noi AutoCAD...", vbInformation

    ' Thu ket noi
    Call HandleCommentModule_ConnectAutocad

    If HandleCommentModule_IsAutocadConnected() Then
        MsgBox "Ket noi AutoCAD thanh cong!" & vbCrLf & _
               "File hien tai: " & HandleCommentModule_AcadDoc.Name & vbCrLf & _
               "Duong dan: " & HandleCommentModule_AcadDoc.Path, vbInformation
    Else
        MsgBox "Khong the ket noi den AutoCAD!" & vbCrLf & _
               "Vui long kiem tra:" & vbCrLf & _
               "1. AutoCAD da duoc cai dat" & vbCrLf & _
               "2. AutoCAD dang chay" & vbCrLf & _
               "3. Co file CAD dang mo", vbCritical
    End If
End Sub

' Ham khoi tao ket noi AutoCAD cho module nay (goi truoc khi su dung cac ham khac)
Public Sub HandleCommentModule_InitializeAutocadConnection()
    Call HandleCommentModule_ConnectAutocad
    If HandleCommentModule_IsAutocadConnected() Then
        MsgBox "Da khoi tao ket noi AutoCAD thanh cong!", vbInformation
    Else
        MsgBox "Khong the khoi tao ket noi AutoCAD!", vbCritical
    End If
End Sub
