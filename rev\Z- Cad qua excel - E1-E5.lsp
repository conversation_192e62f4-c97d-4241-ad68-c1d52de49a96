-------------------------------------------------------------------------------------------------------------------
; CAD to Excel - Tong hop 4 lenh E1 - E7
; E1: Copy text (khong vien)
; E2: Copy text (co vien)
; E3: Copy text + nhay chuot 3 dong
; E4: Copy text + nhay chuot tuy chinh
; Tac gia: Z
-------------------------------------------------------------------------------------------------------------------

; Ham chung cho tat ca cac lenh E1, E2, E3, E4
(defun CAD-TO-EXCEL-MAIN ( mode jump-rows /
	ActDoc
	*Space*
	otcontents
	lpxlist
	lpylist
	textlist
	blocklist
	linelist
	colwidths
	rowheights
	tinfo
	binfo
	xlapp
	xlbooks
	xlbook
	xlsheets
	xlsheet
	xlcells
	xlselection
	startrow
	startcol
	numrows
	numcols
	endrow
	endcol
	newrow
	celladdress
	targetrange
	tablerange
	ecol
	urange
	user-input
	oerror)

	-------------------------------------------------------------------------------------------------------------------

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(setvar 'nomutt 0)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Xu ly input cho E4 (nhap so dong tuy chinh)
	(if (= mode "E4")
		(progn
			(or *E4-jump-rows* (setq *E4-jump-rows* 1))
			(initget 6)
			(setq user-input (getint (strcat "\nNhap so dong can nhay xuong <" (itoa *E4-jump-rows*) ">: ")))
			(if user-input
				(setq *E4-jump-rows* user-input jump-rows user-input)
				(setq jump-rows *E4-jump-rows*)
			)
			(princ (strcat "\nSo dong se nhay: " (itoa jump-rows)))
		)
	)

	; Chon cac doi tuong
	(princ "\nChon cac doi tuong tao thanh bang (Line, Polyline, Text, MText): ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Bo lenh zoom de tranh delay
			(princ "\nDang xu ly thong tin duong line...")
			(tableinfo otcontents)
			(setq lpxlist (vl-sort lpxlist '<) lpylist (vl-sort lpylist '>))
			(princ "\nDang xu ly thong tin text...")
			(mapcar '(lambda (txt)(gettxtinfo (entget txt))) textlist)
			(princ "\nDang xu ly thong tin block...")
			(mapcar '(lambda (blk)(getblockinfo blk)) blocklist)
			(setq colwidths (mapcar '(lambda (x)(- (nth (1+ (vl-position x lpxlist)) lpxlist) x))(reverse (cdr (reverse lpxlist))))
				rowheights (mapcar '(lambda (x)(- x (nth (1+ (vl-position x lpylist)) lpylist)))(reverse(cdr (reverse lpylist)))))

			; Kiem tra Excel co dang mo khong va lay vi tri con tro
			(if (setq xlapp (vlax-get-object "Excel.Application"))
				(progn
					; Su dung Excel dang mo va lay vi tri con tro hien tai
					(setq xlsheet (vlax-get-property xlapp "ActiveSheet")
						xlcells (vlax-get-property xlsheet 'Cells)
						xlselection (vlax-get-property xlapp "Selection")
						startrow (vlax-get-property xlselection "Row")
						startcol (vlax-get-property xlselection "Column"))
					(vlax-put-property xlapp "Visible" :vlax-true)
					(princ (strcat "\nVi tri bat dau: Hang " (itoa startrow) ", Cot " (itoa startcol)))
				)
				(progn
					; Tao Excel moi
					(setq xlapp (vlax-get-or-create-object "Excel.Application")
						xlbooks (vlax-get-property xlapp 'Workbooks)
						xlbook (vlax-invoke-method xlbooks 'Add)
						xlsheets (vlax-get-property xlbook 'Sheets)
						xlsheet (vlax-get-property xlsheets 'Item 1)
						xlcells (vlax-get-property xlsheet 'Cells)
						startrow 1
						startcol 1)
					(vlax-put-property xlapp "Visible" :vlax-true)
					(vlax-invoke-method xlsheet "Activate")
				)
			)

			(setq ecol (conexcelcolumn))

			; Dat text vao Excel tu vi tri con tro
			(mapcar '(lambda (x / r c)
					(setq r (+ startrow (cadr (assoc "Position" x)))
						  c (+ startcol (caddr (assoc "Position" x))))
					(setcelltext xlcells r c (cdr (assoc "Content" x)))
				)
				tinfo
			)

			; Dat thong tin block vao Excel tu vi tri con tro
			(mapcar '(lambda (x / r c bstring)
					(setq r (+ startrow (cadr (assoc "Position" x)))
						  c (+ startcol (caddr (assoc "Position" x))))
					(setq bstring "")
					(if (cdr (assoc "Attributes" x))
						(progn
						(mapcar
						'(lambda (y )
						(setq bstring (strcat ":"(cdr y) bstring)))
						(cdr (assoc "Attributes" x)))
						(setcelltext xlcells r c (strcat "Block:"(cdr (assoc "Name" x)) bstring))
						)
					)
				)
				binfo
			)

			; Xu ly theo mode
			(cond
				; E2: Ve vien cho bang
				((= mode "E2")
					(if (and tinfo (> (length lpxlist) 1) (> (length lpylist) 1))
						(progn
							(setq numrows (- (length lpylist) 1)
								  numcols (- (length lpxlist) 1)
								  endrow (+ startrow numrows -1)
								  endcol (+ startcol numcols -1))
							(setq tablerange (vlax-get-property xlsheet "Range"
								(strcat (nth (- startcol 1) ecol) (itoa startrow) ":"
										(nth (- endcol 1) ecol) (itoa endrow))))
							(vlax-put-property tablerange 'HorizontalAlignment -4108)
							(setgridlines xlapp tablerange)
						)
					)
					(princ "\nHoan thanh copy du lieu tu CAD sang Excel (co vien)!")
				)

				; E3 va E4: Nhay chuot
				((or (= mode "E3") (= mode "E4"))
					; Tinh kich thuoc bang va vi tri cuoi
					(if (and tinfo (> (length lpxlist) 1) (> (length lpylist) 1))
						(progn
							(setq numrows (- (length lpylist) 1)
								  numcols (- (length lpxlist) 1)
								  endrow (+ startrow numrows -1))
							; Tinh vi tri moi cho con tro
							(setq newrow (+ endrow jump-rows))
						)
						(progn
							; Neu khong co du lieu bang
							(setq newrow (+ startrow jump-rows))
						)
					)

					; Tu dong nhay con tro Excel den vi tri moi
					(vl-catch-all-apply '(lambda ()
						(setq celladdress (strcat (nth (- startcol 1) ecol) (itoa newrow)))
						(setq targetrange (vlax-get-property xlsheet "Range" celladdress))
						(vlax-invoke-method targetrange "Select")
						(vlax-put-property xlapp "ActiveCell" targetrange)
						(vlax-release-object targetrange)
					))

					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " celladdress))
				)

				; E1: Khong lam gi them
				(T
					(princ "\nHoan thanh copy du lieu tu CAD sang Excel!")
				)
			)
		)
		(princ "\nKhong chon duoc doi tuong nao!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

-------------------------------------------------------------------------------------------------------------------
; E1 - Copy text (khong vien)
(defun C:E1 ( / )
	(CAD-TO-EXCEL-MAIN "E1" 0)
)

-------------------------------------------------------------------------------------------------------------------
; E2 - Copy text (co vien)
(defun C:E2 ( / )
	(CAD-TO-EXCEL-MAIN "E2" 0)
)

-------------------------------------------------------------------------------------------------------------------
; E3 - Copy text + nhay chuot 3 dong
(defun C:E3 ( / )
	(CAD-TO-EXCEL-MAIN "E3" 3)
)

-------------------------------------------------------------------------------------------------------------------
; E4 - Copy text + nhay chuot tuy chinh
(defun C:E4 ( / )
	(CAD-TO-EXCEL-MAIN "E4" 0)
)

-------------------------------------------------------------------------------------------------------------------
; E5 - Xuat text theo cot/hang/o
(defun C:E5 ( /
	ActDoc
	*Space*
	otcontents
	textlist
	sortedtextlist
	xlapp
	xlbooks
	xlbook
	xlsheets
	xlsheet
	xlcells
	xlselection
	startrow
	startcol
	ecol
	mode
	separator
	result-text
	newrow
	newcol
	celladdress
	targetrange
	heso
	use-formula
	oerror)

	-------------------------------------------------------------------------------------------------------------------

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(setvar 'nomutt 0)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Chon cac text/mtext/dimension
	(princ "\nChon cac text/mtext/dimension (click hoac quet): ")
	(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

	(if otcontents
		(progn
			; Chuyen selection set thanh list text
			(setq textlist (E5-CONVERT-SS-TO-TEXTLIST otcontents))
			; Luu textlist voi toa do cho che do MANG
			(setq *E5-textlist-with-pos* textlist)
			(princ (strcat "\nDa chon " (itoa (length textlist)) " text/mtext/dimension"))

			; Hien menu chon che do
			(setq mode (E5-SHOW-MODE-MENU))

			(if mode
				(progn
					; Sap xep text theo che do
					(setq sortedtextlist (E5-SORT-TEXT-LIST textlist mode))

					; Xu ly separator cho che do "O"
					(if (= mode "O")
						(progn
							(or *E5-separator* (setq *E5-separator* "+"))
							(setq separator (getstring (strcat "\nNhap ky tu noi text <" *E5-separator* ">: ")))
							(if (= separator "")
								(setq separator *E5-separator*)
								(setq *E5-separator* separator)
							)
							(princ (strcat "\nKy tu noi: \"" separator "\""))

							; Neu separator la "+", yeu cau nhap he so
							(if (= separator "+")
								(progn
									(or *E5-heso* (setq *E5-heso* "1"))
									(setq heso (getstring (strcat "\nNhap he so <" *E5-heso* ">: ")))
									(if (= heso "")
										(setq heso *E5-heso*)
										(setq *E5-heso* heso)
									)
									(princ (strcat "\nHe so: " heso))
									(setq use-formula T)
								)
								(setq use-formula nil)
							)
						)
					)

					; Kiem tra Excel co dang mo khong
					(if (setq xlapp (vlax-get-object "Excel.Application"))
						(progn
							; Su dung Excel dang mo
							(setq xlsheet (vlax-get-property xlapp "ActiveSheet")
								xlcells (vlax-get-property xlsheet 'Cells)
								xlselection (vlax-get-property xlapp "Selection")
								startrow (vlax-get-property xlselection "Row")
								startcol (vlax-get-property xlselection "Column"))
							(vlax-put-property xlapp "Visible" :vlax-true)
							(princ (strcat "\nVi tri bat dau: Hang " (itoa startrow) ", Cot " (itoa startcol)))
						)
						(progn
							; Tao Excel moi
							(setq xlapp (vlax-get-or-create-object "Excel.Application")
								xlbooks (vlax-get-property xlapp 'Workbooks)
								xlbook (vlax-invoke-method xlbooks 'Add)
								xlsheets (vlax-get-property xlbook 'Sheets)
								xlsheet (vlax-get-property xlsheets 'Item 1)
								xlcells (vlax-get-property xlsheet 'Cells)
								startrow 1
								startcol 1)
							(vlax-put-property xlapp "Visible" :vlax-true)
							(vlax-invoke-method xlsheet "Activate")
						)
					)

					(setq ecol (conexcelcolumn))

					; Xuat text theo che do
					(cond
						; Che do COT: xuat xuong duoi
						((= mode "COT")
							(setq newrow startrow)
							(foreach txt sortedtextlist
								(setcelltext xlcells newrow startcol txt)
								(setq newrow (+ newrow 1))
							)
							(setq newrow newrow newcol startcol)
							(princ (strcat "\nDa xuat " (itoa (length sortedtextlist)) " text theo cot"))
						)

						; Che do HANG: xuat sang phai
						((= mode "HANG")
							(setq newcol startcol)
							(foreach txt sortedtextlist
								(setcelltext xlcells startrow newcol txt)
								(setq newcol (+ newcol 1))
							)
							(setq newrow startrow newcol newcol)
							(princ (strcat "\nDa xuat " (itoa (length sortedtextlist)) " text theo hang"))
						)

						; Che do O: noi text vao 1 o
						((= mode "O")
							(if use-formula
								; Tao cong thuc Excel
								(progn
									(setq result-text (E5-CREATE-FORMULA sortedtextlist heso))
									(setcelltext xlcells startrow startcol result-text)
									(princ (strcat "\nDa tao cong thuc Excel: " result-text))
								)
								; Noi text binh thuong
								(progn
									(setq result-text (E5-JOIN-TEXT sortedtextlist separator))
									(setcelltext xlcells startrow startcol result-text)
									(princ (strcat "\nDa noi " (itoa (length sortedtextlist)) " text vao 1 o"))
								)
							)
							(setq newrow (+ startrow 1) newcol startcol)
						)

						; Che do MANG: xuat theo mang
						((= mode "MANG")
							(setq newrow startrow newcol startcol)
							(E5-EXPORT-AS-ARRAY sortedtextlist xlcells startrow startcol)
							(setq newrow (+ startrow (E5-GET-ARRAY-ROWS sortedtextlist)) newcol startcol)
							(princ (strcat "\nDa xuat " (itoa (length sortedtextlist)) " text theo mang"))
						)
					)

					; Tu dong nhay con tro Excel den vi tri moi
					(vl-catch-all-apply '(lambda ()
						(setq celladdress (strcat (nth (- newcol 1) ecol) (itoa newrow)))
						(setq targetrange (vlax-get-property xlsheet "Range" celladdress))
						(vlax-invoke-method targetrange "Select")
						(vlax-put-property xlapp "ActiveCell" targetrange)
						(vlax-release-object targetrange)
					))

					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " celladdress))
				)
				(princ "\nDa huy lenh!")
			)
		)
		(princ "\nKhong chon duoc text/mtext nao!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

-------------------------------------------------------------------------------------------------------------------
; E6 - Ghi handle cac doi tuong vao comment Excel
(defun C:E6 ( /
	ActDoc
	*Space*
	otcontents
	xlapp
	xlsheet
	xlcells
	xlselection
	currentcell
	comment
	dwgpath
	dwgname
	handlelist
	commenttext
	oerror)

	-------------------------------------------------------------------------------------------------------------------

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(setvar 'nomutt 0)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Kiem tra Excel co dang mo khong
	(if (setq xlapp (vlax-get-object "Excel.Application"))
		(progn
			; Su dung Excel dang mo
			(setq xlsheet (vlax-get-property xlapp "ActiveSheet")
				xlcells (vlax-get-property xlsheet 'Cells)
				xlselection (vlax-get-property xlapp "Selection")
				currentcell (vlax-get-property xlapp "ActiveCell"))
			(vlax-put-property xlapp "Visible" :vlax-true)
			(princ "\nExcel da duoc ket noi")
		)
		(progn
			(princ "\nLoi: Khong tim thay Excel dang mo!")
			(princ "\nVui long mo Excel truoc khi chay lenh E6")
			(exit)
		)
	)

	; Chon cac doi tuong trong CAD
	(princ "\nChon cac doi tuong de ghi handle vao comment Excel: ")
	(setq otcontents (ssget))

	(if otcontents
		(progn
			; Lay duong dan file CAD hien tai
			(setq dwgpath (vla-get-fullname ActDoc))
			(setq dwgname (vl-filename-base dwgpath))
			(princ (strcat "\nFile CAD: " dwgpath))
			(princ (strcat "\nDa chon " (itoa (sslength otcontents)) " doi tuong"))

			; Lay handle cua tat ca doi tuong
			(setq handlelist (E6-GET-HANDLES otcontents))

			; Tao noi dung comment
			(setq commenttext (E6-CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
			(princ (strcat "\nNoi dung comment: " commenttext))

			; Ghi comment vao cell Excel
			(E6-WRITE-COMMENT-TO-CELL currentcell commenttext)

			(princ (strcat "\nHoan thanh! Da ghi " (itoa (length handlelist)) " handle vao comment cell Excel"))
		)
		(princ "\nKhong chon duoc doi tuong nao!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

-------------------------------------------------------------------------------------------------------------------
; E7 - Ghi text/dim vao o va handle vao comment dong thoi
(defun C:E7 ( /
	ActDoc
	*Space*
	otcontents
	xlapp
	xlsheet
	xlcells
	xlselection
	currentcell
	dwgpath
	dwgname
	handlelist
	commenttext
	celltext
	oerror)

	-------------------------------------------------------------------------------------------------------------------

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(setvar 'nomutt 0)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc)'("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Kiem tra Excel co dang mo khong
	(if (setq xlapp (vlax-get-object "Excel.Application"))
		(progn
			(setq xlsheet (vlax-get-property xlapp "ActiveSheet")
				xlcells (vlax-get-property xlsheet 'Cells)
				xlselection (vlax-get-property xlapp "Selection")
				currentcell (vlax-get-property xlapp "ActiveCell"))
			(vlax-put-property xlapp "Visible" :vlax-true)
			(princ "\nExcel da duoc ket noi")
		)
		(progn
			(princ "\nLoi: Khong tim thay Excel dang mo!")
			(princ "\nVui long mo Excel truoc khi chay lenh E7")
			(exit)
		)
	)

	; Chon cac doi tuong trong CAD
	(princ "\nChon cac text/mtext/dimension: ")
	(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

	(if otcontents
		(progn
			(setq dwgpath (vla-get-fullname ActDoc)
				dwgname (vl-filename-base dwgpath))
			(princ (strcat "\nDa chon " (itoa (sslength otcontents)) " doi tuong"))

			; Xu ly noi dung cell va handle
			(setq celltext (E7-CREATE-CELL-TEXT otcontents))
			(princ (strcat "\nNoi dung cell: " celltext))

			(setq handlelist (E6-GET-HANDLES otcontents))
			(princ (strcat "\nSo handle: " (itoa (length handlelist))))

			(setq commenttext (E6-CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
			(princ (strcat "\nNoi dung comment: " commenttext))

			; Ghi vao Excel
			(E7-WRITE-TO-EXCEL currentcell celltext commenttext)
			(princ "\nDa ghi xong vao Excel")

			(princ (strcat "\nHoan thanh! Da ghi " (itoa (sslength otcontents)) " doi tuong vao Excel"))
		)
		(princ "\nKhong chon duoc doi tuong nao!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

-------------------------------------------------------------------------------------------------------------------
; Ham tao noi dung cell cho E7
(defun E7-CREATE-CELL-TEXT ( ss / n ent entobj objname txtcontent measurement textlist dimlist result)
	(setq n 0 textlist '() dimlist '())

	; Phan loai text va dimension
	(repeat (sslength ss)
		(setq ent (ssname ss n)
			  entobj (vlax-ename->vla-object ent)
			  objname (vla-get-objectname entobj))

		(cond
			; TEXT va MTEXT
			((or (= objname "AcDbText") (= objname "AcDbMText"))
				(setq txtcontent (vla-get-textstring entobj))
				; Xu ly MText - loai bo format
				(if (= objname "AcDbMText")
					(progn
						(setq txtcontent (vl-string-subst "" "\\P" txtcontent))
						(setq txtcontent (vl-string-subst "" "\\p" txtcontent))
						(while (vl-string-search "\\" txtcontent)
							(setq txtcontent (vl-string-subst "" (substr txtcontent (vl-string-search "\\" txtcontent) 2) txtcontent))
						)
					)
				)
				(setq textlist (append textlist (list txtcontent)))
			)
			; DIMENSION
			((or (= objname "AcDbAlignedDimension")
				 (= objname "AcDbRotatedDimension")
				 (= objname "AcDbRadialDimension")
				 (= objname "AcDbDiametricDimension")
				 (= objname "AcDbAngularDimension")
				 (wcmatch objname "*Dimension*"))
				(setq measurement (vla-get-measurement entobj))
				(setq dimlist (append dimlist (list (itoa (fix (+ measurement 0.5))))))
			)
		)
		(setq n (+ n 1))
	)

	; Tao ket qua
	(setq result "")

	; Xu ly text - noi bang dau ";"
	(if textlist
		(progn
			(setq result (car textlist))
			(foreach txt (cdr textlist)
				(setq result (strcat result ";" txt))
			)
		)
	)

	; Xu ly dimension - tao cong thuc "=dim1+dim2+..."
	(if dimlist
		(progn
			; Neu da co text, them dau ";"
			(if (> (strlen result) 0)
				(setq result (strcat result ";"))
			)
			; Tao cong thuc dimension
			(setq result (strcat result "=" (car dimlist)))
			(foreach dim (cdr dimlist)
				(setq result (strcat result "+" dim))
			)
		)
	)

	result
)

-------------------------------------------------------------------------------------------------------------------
; Ham ghi vao Excel cho E7
(defun E7-WRITE-TO-EXCEL ( cell celltext commenttext / result cellrange worksheet targetrange)
	; Ghi noi dung vao cell
	(if (and celltext (> (strlen celltext) 0))
		(progn
			(princ (strcat "\nDang ghi cell: " celltext))
			(setq result (vl-catch-all-apply '(lambda ()
				; Thu cach 1: Su dung Value2
				(vlax-put-property cell "Value2" celltext)
			)))
			(if (vl-catch-all-error-p result)
				(progn
					(princ (strcat "\nLoi cach 1: " (vl-catch-all-error-message result)))
					; Thu cach 2: Su dung Formula
					(setq result (vl-catch-all-apply '(lambda ()
						(vlax-put-property cell "Formula" celltext)
					)))
					(if (vl-catch-all-error-p result)
						(progn
							(princ (strcat "\nLoi cach 2: " (vl-catch-all-error-message result)))
							; Thu cach 3: Su dung Range
							(setq result (vl-catch-all-apply '(lambda ()
								(setq cellrange (vlax-get-property cell "Address"))
								(setq worksheet (vlax-get-property cell "Worksheet"))
								(setq targetrange (vlax-get-property worksheet "Range" cellrange))
								(vlax-put-property targetrange "Value" celltext)
							)))
							(if (vl-catch-all-error-p result)
								(princ (strcat "\nLoi cach 3: " (vl-catch-all-error-message result)))
								(princ "\nGhi cell thanh cong (cach 3)")
							)
						)
						(princ "\nGhi cell thanh cong (cach 2)")
					)
				)
				(princ "\nGhi cell thanh cong (cach 1)")
			)
		)
		(princ "\nKhong co noi dung cell de ghi")
	)

	; Ghi comment
	(if (and commenttext (> (strlen commenttext) 0))
		(progn
			(princ (strcat "\nDang ghi comment: " (substr commenttext 1 50) "..."))
			(setq result (vl-catch-all-apply '(lambda ()
				(E6-WRITE-COMMENT-TO-CELL cell commenttext)
			)))
			(if (vl-catch-all-error-p result)
				(princ (strcat "\nLoi ghi comment: " (vl-catch-all-error-message result)))
				(princ "\nGhi comment thanh cong")
			)
		)
		(princ "\nKhong co noi dung comment de ghi")
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay handle cua cac doi tuong cho E6
(defun E6-GET-HANDLES ( ss / n ent handlelist entobj handle)
	(setq n 0 handlelist '())
	(repeat (sslength ss)
		(setq ent (ssname ss n))
		(setq entobj (vlax-ename->vla-object ent))
		(setq handle (vla-get-handle entobj))
		(setq handlelist (append handlelist (list handle)))
		(setq n (+ n 1))
	)
	handlelist
)

-------------------------------------------------------------------------------------------------------------------
; Ham tao noi dung comment cho E6
(defun E6-CREATE-COMMENT-TEXT ( handlelist dwgpath dwgname / commenttext filename handletext)
	; Tao phan handle
	(setq handletext "")
	(if handlelist
		(progn
			(setq handletext (car handlelist))
			(foreach handle (cdr handlelist)
				(setq handletext (strcat handletext "; " handle))
			)
			; Them dau ";" vao handle cuoi cung
			(setq handletext (strcat handletext ";"))
		)
	)

	; Tao noi dung comment - khong co khoang trang truoc handle dau
	(setq commenttext (strcat handletext " \nFileCad: " dwgpath))

	commenttext
)

-------------------------------------------------------------------------------------------------------------------
; Ham ghi comment vao cell Excel cho E6 - toi uu
(defun E6-WRITE-COMMENT-TO-CELL ( cell commenttext / comment)
	(vl-catch-all-apply '(lambda ()
		; Xoa comment cu neu co
		(setq comment (vlax-get-property cell "Comment"))
		(if comment (vlax-invoke-method comment "Delete"))

		; Them comment moi don gian
		(if (and commenttext (> (strlen commenttext) 0))
			(vlax-invoke-method cell "AddComment" commenttext)
		)
	))
)

-------------------------------------------------------------------------------------------------------------------
; Ham chuyen selection set thanh list text cho E5 (ho tro dimension)
(defun E5-CONVERT-SS-TO-TEXTLIST ( ss / n ent textlist txtobj txtcontent txtpos objname measurement)
	(setq n 0 textlist '())
	(repeat (sslength ss)
		(setq ent (ssname ss n))
		(setq txtobj (vlax-ename->vla-object ent))
		(setq objname (vla-get-objectname txtobj))

		; Xu ly theo loai doi tuong
		(cond
			; TEXT
			((= objname "AcDbText")
				(setq txtcontent (vla-get-textstring txtobj))
				(setq txtpos (vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj))))
			)
			; MTEXT
			((= objname "AcDbMText")
				(setq txtcontent (vla-get-textstring txtobj))
				(setq txtpos (vlax-safearray->list (vlax-variant-value (vla-get-insertionpoint txtobj))))
				; Loai bo cac ky tu dinh dang MText
				(setq txtcontent (vl-string-subst "" "\\P" txtcontent))
				(setq txtcontent (vl-string-subst "" "\\p" txtcontent))
				(while (vl-string-search "\\" txtcontent)
					(setq txtcontent (vl-string-subst "" (substr txtcontent (vl-string-search "\\" txtcontent) 2) txtcontent))
				)
			)
			; DIMENSION (tat ca cac loai)
			((or (= objname "AcDbAlignedDimension")
				 (= objname "AcDbRotatedDimension")
				 (= objname "AcDbRadialDimension")
				 (= objname "AcDbDiametricDimension")
				 (= objname "AcDbAngularDimension")
				 (wcmatch objname "*Dimension*"))
				; Lay gia tri Measurement va lam tron 0 chu so thap phan
				(setq measurement (vla-get-measurement txtobj))
				(setq txtcontent (itoa (fix (+ measurement 0.5)))) ; Lam tron 0 chu so thap phan
				(setq txtpos (vlax-safearray->list (vlax-variant-value (vla-get-textposition txtobj))))
			)
			; Loai khac - bo qua
			(T
				(setq txtcontent nil txtpos nil)
			)
		)

		; Them vao list neu co noi dung
		(if (and txtcontent txtpos)
			(setq textlist (append textlist (list (list txtcontent txtpos))))
		)
		(setq n (+ n 1))
	)
	textlist
)

-------------------------------------------------------------------------------------------------------------------
; Ham hien menu chon che do cho E5
(defun E5-SHOW-MODE-MENU ( / choice)
	(princ "\n=== CHON CHE DO XUAT ===")
	(princ "\n1. Cot (xuat xuong duoi)")
	(princ "\n2. Hang (xuat sang phai)")
	(princ "\n3. O (noi text vao 1 o)")
	(princ "\n4. Mang (xuat theo mang)")
	(initget "1 2 3 4 Cot Hang O Mang")
	(setq choice (getkword "\nChon che do [1/2/3/4] hoac [Cot/Hang/O/Mang]: "))
	(cond
		((or (= choice "1") (= choice "Cot")) "COT")
		((or (= choice "2") (= choice "Hang")) "HANG")
		((or (= choice "3") (= choice "O")) "O")
		((or (= choice "4") (= choice "Mang")) "MANG")
		(T nil)
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham sap xep text list cho E5 - logic chinh xac
(defun E5-SORT-TEXT-LIST ( textlist mode / tolerance xa ya xb yb)
	(setq tolerance 10.0) ; Tang tolerance de nhan dien hang/cot tot hon

	(cond
		; Che do COT: tu trai sang phai, cung cot thi tu tren xuong
		((= mode "COT")
			(setq textlist (vl-sort textlist '(lambda (a b)
				(setq xa (car (cadr a)) ya (cadr (cadr a))
					  xb (car (cadr b)) yb (cadr (cadr b)))
				; Neu cung cot (X gan bang nhau), sap xep theo Y (tren xuong)
				(if (< (abs (- xa xb)) tolerance)
					(> ya yb) ; Cung cot: tu tren xuong (Y giam dan)
					(< xa xb) ; Khac cot: tu trai sang phai (X tang dan)
				)
			)))
		)
		; Che do HANG: tu tren xuong, cung hang thi tu trai sang phai
		((= mode "HANG")
			(setq textlist (vl-sort textlist '(lambda (a b)
				(setq xa (car (cadr a)) ya (cadr (cadr a))
					  xb (car (cadr b)) yb (cadr (cadr b)))
				; Neu cung hang (Y gan bang nhau), sap xep theo X (trai phai)
				(if (< (abs (- ya yb)) tolerance)
					(< xa xb) ; Cung hang: tu trai sang phai (X tang dan)
					(> ya yb) ; Khac hang: tu tren xuong (Y giam dan)
				)
			)))
		)
		; Che do O: giu nguyen thu tu chon (khong sap xep)
		((= mode "O")
			textlist
		)
		; Che do MANG: sap xep theo mang (tren xuong truoc, trai phai sau)
		((= mode "MANG")
			(setq textlist (vl-sort textlist '(lambda (a b)
				(setq xa (car (cadr a)) ya (cadr (cadr a))
					  xb (car (cadr b)) yb (cadr (cadr b)))
				; Uu tien Y truoc (tren xuong), neu cung hang thi X (trai phai)
				(if (< (abs (- ya yb)) tolerance)
					(< xa xb) ; Cung hang: tu trai sang phai (X tang dan)
					(> ya yb) ; Khac hang: tu tren xuong (Y giam dan)
				)
			)))
		)
	)
	; Tra ve chi noi dung text, bo toa do
	(mapcar 'car textlist)
)



-------------------------------------------------------------------------------------------------------------------
; Ham noi text cho E5
(defun E5-JOIN-TEXT ( textlist separator / result)
	(setq result "")
	(if textlist
		(progn
			(setq result (car textlist))
			(foreach txt (cdr textlist)
				(setq result (strcat result separator txt))
			)
		)
	)
	result
)

-------------------------------------------------------------------------------------------------------------------
; Ham tao cong thuc Excel cho E5
(defun E5-CREATE-FORMULA ( textlist heso / formula)
	(setq formula "=(")
	(if textlist
		(progn
			; Them text dau tien
			(setq formula (strcat formula (car textlist)))
			; Them cac text con lai voi dau "+"
			(foreach txt (cdr textlist)
				(setq formula (strcat formula "+" txt))
			)
			; Them he so
			(setq formula (strcat formula ")*" heso))
		)
		(setq formula (strcat formula "0)*" heso))
	)
	formula
)

-------------------------------------------------------------------------------------------------------------------
; Ham xuat text theo mang cho E5
(defun E5-EXPORT-AS-ARRAY ( textlist xlcells startrow startcol /
	textlist-with-pos sorted-textlist unique-rows unique-cols
	row-count col-count current-row current-col txt-pos txt-content
	xa ya xb yb x-pos y-pos)

	; Lay lai textlist voi toa do tu ham chinh
	(setq textlist-with-pos (E5-GET-TEXTLIST-WITH-POS))

	; Sap xep lai theo mang (tren xuong truoc, trai phai sau)
	(setq sorted-textlist (vl-sort textlist-with-pos '(lambda (a b)
		(setq xa (car (cadr a)) ya (cadr (cadr a))
			  xb (car (cadr b)) yb (cadr (cadr b)))
		; Uu tien Y truoc (tren xuong), neu cung hang thi X (trai phai)
		(if (< (abs (- ya yb)) 10.0)
			(< xa xb) ; Cung hang: tu trai sang phai
			(> ya yb) ; Khac hang: tu tren xuong
		)
	)))

	; Tim cac hang va cot duy nhat
	(setq unique-rows (E5-GET-UNIQUE-ROWS sorted-textlist)
		  unique-cols (E5-GET-UNIQUE-COLS sorted-textlist))

	; Xuat text theo vi tri mang
	(setq current-row startrow)
	(foreach row-y unique-rows
		(setq current-col startcol)
		(foreach col-x unique-cols
			; Tim text tai vi tri (row-y, col-x)
			(foreach txt-item sorted-textlist
				(setq txt-pos (cadr txt-item)
					  txt-content (car txt-item))
				(if (and (< (abs (- (cadr txt-pos) row-y)) 10.0)
						 (< (abs (- (car txt-pos) col-x)) 10.0))
					(setcelltext xlcells current-row current-col txt-content)
				)
			)
			(setq current-col (+ current-col 1))
		)
		(setq current-row (+ current-row 1))
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay so hang cua mang
(defun E5-GET-ARRAY-ROWS ( textlist / textlist-with-pos unique-rows)
	(setq textlist-with-pos (E5-GET-TEXTLIST-WITH-POS))
	(setq unique-rows (E5-GET-UNIQUE-ROWS textlist-with-pos))
	(length unique-rows)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay cac hang duy nhat
(defun E5-GET-UNIQUE-ROWS ( textlist-with-pos / unique-rows y-pos)
	(setq unique-rows '())
	(foreach txt textlist-with-pos
		(setq y-pos (cadr (cadr txt)))
		(if (not (vl-some '(lambda (existing-y) (< (abs (- y-pos existing-y)) 10.0)) unique-rows))
			(setq unique-rows (append unique-rows (list y-pos)))
		)
	)
	(vl-sort unique-rows '>)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay cac cot duy nhat
(defun E5-GET-UNIQUE-COLS ( textlist-with-pos / unique-cols x-pos)
	(setq unique-cols '())
	(foreach txt textlist-with-pos
		(setq x-pos (car (cadr txt)))
		(if (not (vl-some '(lambda (existing-x) (< (abs (- x-pos existing-x)) 10.0)) unique-cols))
			(setq unique-cols (append unique-cols (list x-pos)))
		)
	)
	(vl-sort unique-cols '<)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay textlist voi toa do (bien global tam thoi)
(defun E5-GET-TEXTLIST-WITH-POS ( / )
	*E5-textlist-with-pos*
)

-------------------------------------------------------------------------------------------------------------------
; Ham phan tich thong tin bang
(defun tableinfo ( ss  / n entlist)
	(setq n 0)
	(repeat (sslength ss)
		(setq entlist (entget (ssname ss n)))
		(cond ((member (cdr (assoc 0 entlist)) '("LINE" "POLYLINE"))
			(getlinepts entlist)(setq linelist (cons (ssname ss n) linelist)))
			((member (cdr (assoc 0 entlist)) '("TEXT" "MTEXT"))
			(setq textlist (cons (ssname ss n) textlist)))
			((member (cdr (assoc 0 entlist)) '("INSERT"))
			(setq blocklist (cons (ssname ss n) blocklist)))
		)
		(setq n (1+ n))
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay diem cua line va polyline
(defun getlinepts (alist / x  xpt ypt)
  (foreach x alist
     (if (member (car x) '(10 11))
         (progn
           (if (not (vl-position (setq xpt (atof (rtos (car (trans (cdr x) 0 1)) 2 2))) lpxlist))
               (setq lpxlist (cons xpt lpxlist)))
           (if (not (vl-position (setq ypt (atof (rtos (cadr (trans (cdr x) 0 1)) 2 2))) lpylist))
               (setq lpylist (cons ypt lpylist)))
         )
      )
   )
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay thong tin text va vi tri cell
(defun gettxtinfo (alist / x vlaobj pos rpos cpos expos txt)
	(setq txt (cdr (assoc -1 alist)))
	(setq vlaobj (vlax-ename->vla-object txt)
			pos (trans (midp vlaobj) 0 1);Midpoint
			rpos (1- (vl-position (cadr pos)(vl-sort (cons (cadr pos) lpylist) '>)));Row Position
			cpos (1- (vl-position (car pos) (vl-sort (cons (car pos) lpxlist) '<))));Column Position
	(if (setq expos (vl-position (list rpos cpos) (mapcar '(lambda (x)(cdr (assoc "Position" x))) tinfo)));if cell is taken
		(setq tinfo
			(replace tinfo expos
				(replace
					(nth expos tinfo)
					2
					(cons "Content"
						(if (> (cadr pos) (cdr (assoc "Order" (nth expos tinfo))));in order according to y position
							(strcat (vla-fieldcode vlaobj) " " (cdr (assoc "Content" (nth expos tinfo))))
							(strcat (cdr (assoc "Content" (nth expos tinfo))) " " (vla-fieldcode vlaobj))
						)
					)
				)
			)
		)
		(setq tinfo
			(cons
				(list
					(Cons "Order" (cadr pos))
					(Cons "Position" (list rpos cpos));Position
					(Cons "Content" (vla-fieldcode vlaobj));Content
					(Cons "Height" (vla-get-height vlaobj))
					(Cons "Rotation" (vla-get-rotation vlaobj))
					(Cons "StyleName" (vla-get-StyleName vlaobj))
					(Cons "TrueColor"
						(if (= (vla-get-colorindex (vla-get-truecolor vlaobj)) 256)
							(vla-get-truecolor (vla-item (vla-get-layers ActDoc) (vla-get-layer vlaobj)))
							(vla-get-truecolor vlaobj)
						)
					)
				)
				tinfo
			)
		)
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham lay thong tin block va vi tri cell
(defun getblockinfo (obj / pos rpos cpos bname objid bobj attid)
	(if (= (type obj) 'ename) (setq obj (vlax-ename->vla-object obj)))
	(setq pos (trans (midp obj) 0 1)
		rpos (1- (vl-position (cadr pos) (vl-sort (cons (cadr pos) lpylist) '>)));Row Position
		cpos (1- (vl-position (car pos) (vl-sort (cons (car pos) lpxlist) '<)));Column Position
		bname (vla-get-name obj);Block Name
		bobj (vla-item (vla-get-blocks ActDoc) bname));Block Vla Object
	(vlax-for i bobj ; Foreach item in block
		(if (eq (vla-get-objectname i) "AcDbAttributeDefinition");If item is an attribute
			(setq attid (append attid (list (vla-get-objectid i))));List Attribute Id
		)
	)
	(setq objid (vla-get-objectid bobj));Block Object Id
	(setq binfo
		(cons
			(list
				(Cons "Name" bname)
				(Cons "Position" (list rpos cpos))
				(Cons "ObjID" objid)
				(if (= (vla-get-hasattributes obj) :vlax-true)
					(Cons "Attributes"
						(reverse
							(mapcar
								'(lambda (x y) (cons y (vla-get-textstring x)))
								(vlax-safearray->list (variant-value (vla-getattributes obj)))
								attid
							)
						)
					)
				)
				(Cons "Scale" (vla-get-xscalefactor obj))
			)
			binfo
		)
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham replace cua Charles Alan Butler
(defun replace (lst i itm)
  (setq i (1+ i))
  (mapcar
    '(lambda (x)
      (if (zerop (setq i (1- i))) itm x)
    )
    lst
  )
)

-------------------------------------------------------------------------------------------------------------------
; Ham tim diem giua cua object
(defun midp (obj / ObjLl ObjUr)
	(vla-GetBoundingBox obj 'ObjLl 'ObjUr)
	(mapcar '(lambda (a b) (/ (+ a b) 2.0))
		(safearray-value ObjLl)
		(safearray-value ObjUr)
	)
)

-------------------------------------------------------------------------------------------------------------------
; Ham dat text vao cell Excel
(defun setcelltext(cells row column value)
  (vl-catch-all-apply
    'vlax-put-property
    (list cells 'Item row column
	 (vlax-make-variant
	   (vl-princ-to-string value) 8)))
  )

-------------------------------------------------------------------------------------------------------------------
; Ham ve vien cho bang Excel
(defun setgridlines(xlapp range)
  ; select the range:
  (vlax-invoke-method range 'Select)
  ; get excel application selection property:
  (setq range (vlax-get-property xlapp 'Selection))
  ; get selection borders
  (setq borders (vlax-get-property range 'Borders))
  ; iterate through all edges of the selection
  (setq cnt 0)
    (vlax-for a	 borders
      (setq cnt (1+ cnt))
      (vl-catch-all-apply
	(function
	  (lambda ()
	    (progn
	      (if (< cnt 5)
		(progn
		  (vlax-put-property
		    a
		    'LineStyle
		    (vlax-make-variant 1 3)); single line style
		  (vlax-put-property
		    a
		    'Weight
		    (vlax-make-variant 2 3));  lines
		  (vlax-put-property
		    a
		    'ColorIndex
		    (vlax-make-variant 1 5))); color black

		;; turn off the diagonal lines:
		(vlax-put-property a 'LineStyle (vlax-make-variant -4142 3))
		)
	      )
	    )
	  )
	)
      )
  (princ)
  )

-------------------------------------------------------------------------------------------------------------------
; Ham tao danh sach cot Excel
(defun conexcelcolumn (/ a b list1)
  (setq a 65)
  (setq list1 nil)
  (repeat 26
    (setq list1 (append
		  list1
		  (list (chr a))
		)
    )
    (setq a (1+ a))
  )
  (setq a 65)
  (repeat 26
    (setq b 65)
    (repeat 26
      (setq list1 (append
		    list1
		    (list (strcat (chr a) (chr b)))
		  )
      )
      (setq b (1+ b))
    )
    (setq a (1+ a))
  )
  list1
)

-------------------------------------------------------------------------------------------------------------------
; Ket thuc file CAD to Excel - Tong hop E1 E2 E3 E4 E5 E6 E7
(princ "\nCAD to Excel da duoc load thanh cong!")
(princ "\nCo 7 lenh co san:")
(princ "\n  E1 - Copy text (khong vien)")
(princ "\n  E2 - Copy text (co vien)")
(princ "\n  E3 - Copy text + nhay chuot 3 dong")
(princ "\n  E4 - Copy text + nhay chuot tuy chinh")
(princ "\n  E5 - Xuat text theo cot/hang/o/mang")
(princ "\n  E6 - Ghi handle doi tuong vao comment Excel")
(princ "\n  E7 - Ghi text/dim vao o + handle vao comment")
(princ "\nLuu y: Mo Excel truoc khi chay lenh de dat du lieu vao vi tri con tro.")
(princ)
