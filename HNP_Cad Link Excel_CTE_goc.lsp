-------------------------------------------------------------------------------------------------------------------
; HNP_Cad Link Excel_CTE_fixed.lsp - Fixed for AutoCAD 2025+
; Phien ban sua loi cho AutoCAD 2025+ voi Express Tools loading
-------------------------------------------------------------------------------------------------------------------

; Load Express Tools cho AutoCAD 2025+
(if (not acet-util-ver)
	(acet-load-expresstools)
)

(defun C:ETC ( /
	CheckActiveCell
	ListDataCoordinateX
	ListDataCoordinateY
	ListDataLine
	ListDataTable
	ListTemp
	ListVarSystem_OldValue
	ListVlaObjectOfTable
	NodeTotalX
	NodeTotalY
	ScaleGlobal
;	ScaleTable
	ScaleTableTemp
	VlaDrawingCurrent
	VlaSpace)

	-------------------------------------------------------------------------------------------------------------------
	
	(vl-load-com)
	(setq VlaDrawingCurrent (vla-get-activedocument (vlax-get-acad-object)))
	(vla-startundomark VlaDrawingCurrent)
	(CAEX_SET_VARSYSTEM_E2C)
	(CAEX_CREATE_LISTVLALAYERLOCK)

	(vl-catch-all-apply (function (lambda ( / )
		(setq CheckActiveCell (CAEX_CHECKACTIVECELL))
		(if CheckActiveCell
			(progn
				(if
					(not
						(and
							(or
								(= (type ScaleTable) 'INT)
								(= (type ScaleTable) 'REAL)
							)
							(> ScaleTable 0)
						)
					)
					(setq ScaleTable 1.0)
				)
				(initget 4)
				(setq ScaleTableTemp (getreal (strcat "\nEnter the scale of table <" (rtos ScaleTable 2) ">:")))
				(if ScaleTableTemp
					(setq ScaleTable ScaleTableTemp)
				)
				(if (= (getvar "CVPORT") 1)
					(setq VlaSpace (vla-get-PaperSpace VlaDrawingCurrent))
					(setq VlaSpace (vla-get-ModelSpace VlaDrawingCurrent))
				)

				(setq ListTemp (CAEX_GET_LISTDATATABLE_FROM_EXCEL Nil Nil))
				(setq ListDataTable (nth 0 ListTemp))
				(setq ListDataCoordinateX (nth 1 ListTemp))
				(setq ListDataCoordinateY (nth 2 ListTemp))

				(setq NodeTotalX (- (length ListDataCoordinateX) 1))
				(setq NodeTotalY (- (length ListDataCoordinateY) 1))
				(setq ScaleGlobal (* (/ 2.5 (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 6 x))) ListDataTable))) ScaleTable))

				(setq ListDataLine (CAEX_GET_LISTDATALINE_FROM_EXCEL))
				(setq ListVlaObjectOfTable (CAEX_CREATE_TABLE_FOR_CAD))
				(CAEX_PICK_POINT_FOR_TABLE)
			)
			(princ "\nData from excel could not be found!")
		)
	)))

	(CAEX_RESTORE_LOCK_LAYER)
	(CAEX_RESET_VARSYSTEM)
	(vla-endundomark VlaDrawingCurrent)
	(princ)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_PICK_POINT_FOR_TABLE ( / 
	CheckStop
	CodeMouse
	PointBase
	PointTemp
	Temp)

	(setq SeletionSet (ssadd))
	(foreach VlaObject ListVlaObjectOfTable
		(ssadd (vlax-vla-object->ename VlaObject) SeletionSet)
	)
	(command ".COPYBASE" (list 0.0 0.0 0.0) SeletionSet "")
	(mapcar 'vla-delete ListVlaObjectOfTable)
	(vl-catch-all-apply (function (lambda ( / )
		(command ".PASTECLIP" pause)
	)))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTDATALINE_FROM_EXCEL (  / 
	ListDataAddress
	ListNodeDash
	ListDataLine
	NodeDash
	NodeEndX
	NodeEndY
	NodeLine
	NodeStartX
	NodeStartY
	NodeX
	NodeY)

	(setq ListDataAddress (mapcar '(lambda (x) (nth 1 (assoc 1 x))) ListDataTable))
	(foreach ListAddress ListDataAddress
		(setq NodeX (+ (nth 0 ListAddress) 1))
		(repeat (- (nth 2 ListAddress) (nth 0 ListAddress))
			(setq NodeY (nth 1 ListAddress))
			(repeat (- (nth 3 ListAddress) (nth 1 ListAddress) -1)
				(setq NodeDash (list NodeX NodeY NodeX (+ NodeY 1)))
				(setq ListNodeDash (append ListNodeDash (list NodeDash)))
				(setq NodeY (+ NodeY 1))
			)
			(setq NodeX (+ NodeX 1))
		)

		(setq NodeY (+ (nth 1 ListAddress) 1))
		(repeat (- (nth 3 ListAddress) (nth 1 ListAddress))
			(setq NodeX (nth 0 ListAddress))
			(repeat (- (nth 2 ListAddress) (nth 0 ListAddress) -1)
				(setq NodeDash (list NodeX NodeY (+ NodeX 1) NodeY))
				(setq ListNodeDash (append ListNodeDash (list NodeDash)))
				(setq NodeX (+ NodeX 1))
			)
			(setq NodeY (+ NodeY 1))
		)
	)
	
	(setq ListDataLine
		(list
			(list 0 0 0 NodeTotalY)
			(list NodeTotalX 0 NodeTotalX NodeTotalY)
			(list 0 0 NodeTotalX 0)
			(list 0 NodeTotalY NodeTotalX NodeTotalY)
		)
	)

	(setq NodeX 1)
	(repeat (- NodeTotalX 1)
		(setq NodeY 0)
		(setq NodeStartY NodeY)
		(repeat NodeTotalY
			(setq NodeDash (list NodeX NodeY NodeX (+ NodeY 1)))
			(if (member NodeDash ListNodeDash)
				(progn
					(setq NodeEndY NodeY)
					(if (/= NodeStartY NodeEndY)
						(progn
							(setq NodeLine (list NodeX NodeStartY NodeX NodeEndY))
							(setq ListDataLine (append ListDataLine (list NodeLine)))
						)
					)
					(setq NodeStartY (+ NodeY 1))
				)
			)
			(setq NodeY (+ NodeY 1))
		)
		(setq NodeEndY NodeY)
		(if (/= NodeStartY NodeEndY)
			(progn
				(setq NodeLine (list NodeX NodeStartY NodeX NodeEndY))
				(setq ListDataLine (append ListDataLine (list NodeLine)))
			)
		)
		(setq NodeX (+ NodeX 1))
	)

	(setq NodeY 1)
	(repeat (- NodeTotalY 1)
		(setq NodeX 0)
		(setq NodeStartX NodeX)
		(repeat NodeTotalX
			(setq NodeDash (list NodeX NodeY (+ NodeX 1) NodeY))
			(if (member NodeDash ListNodeDash)
				(progn
					(setq NodeEndX NodeX)
					(if (/= NodeStartX NodeEndX)
						(progn
							(setq NodeLine (list NodeStartX NodeY NodeEndX NodeY))
							(setq ListDataLine (append ListDataLine (list NodeLine)))
						)
					)
					(setq NodeStartX (+ NodeX 1))
				)
			)
			(setq NodeX (+ NodeX 1))
		)
		(setq NodeEndX NodeX)
		(if (/= NodeStartX NodeEndX)
			(progn
				(setq NodeLine (list NodeStartX NodeY NodeEndX NodeY))
				(setq ListDataLine (append ListDataLine (list NodeLine)))
			)
		)
		(setq NodeY (+ NodeY 1))
	)
	ListDataLine
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTDATATABLE_FROM_EXCEL ( NumColTotal NumRowTotal / 
	CheckCloseApp
	CheckHiddenCol
	CheckHiddenRow
	HeightRow
	ListAddressTotal
	ListDataCoordinateY
	ListDataCoordinateX
	ListDataTable
	NumCol
	NumColStart
	NumRow
	NumRowStart
	StringAddress
	VlaAppExcel
	VlaRange
	VlaRangeCol
    VlaRangeRow
	VlaSheet
	VlaWorkbooks
	WidthColumn)

	(setq VlaAppExcel (vlax-get-or-create-Object "Excel.Application"))
	(vlax-put-property VlaAppExcel "Visible" :vlax-true)
	(setq VlaWorkbooks (vlax-get-property VlaAppExcel "Workbooks"))
	(setq CheckCloseApp (= (vla-get-count VlaWorkbooks) 0))
	(setq VlaRange (vlax-get-property VlaAppExcel "Selection"))
	(setq VlaSheet (vlax-get-property VlaAppExcel "ActiveSheet"))

	(if VlaRange
		(progn
			(setq StringAddress (vlax-get-property VlaRange "Address" :vlax-false :vlax-false 1 :vlax-false :vlax-false))
			(setq ListAddressTotal (CAEX_STRINGADDRESS_TO_LISTADDRESS StringAddress))
			(if (= (length ListAddressTotal) 2)
				(setq ListAddressTotal (append ListAddressTotal ListAddressTotal))
			)
			(if (not NumColTotal)
				(setq NumColTotal (- (nth 2 ListAddressTotal) (nth 0 ListAddressTotal) -1))
			)
			(if (not NumRowTotal)
				(setq NumRowTotal (- (nth 3 ListAddressTotal) (nth 1 ListAddressTotal) -1))
			)
			(setq NumColStart (nth 0 ListAddressTotal))
			(setq NumRowStart (nth 1 ListAddressTotal))

			(setq NumRow NumRowStart)
			(repeat NumRowTotal
				(setq NumCol NumColStart)
				(repeat NumColTotal
					(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS (list NumCol NumRow)))
					(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
					(setq VlaRangeRow (vlax-get-property VlaRange "Rows"))
					(setq CheckHiddenRow (vlax-variant-value (vlax-get-property VlaRangeRow "Hidden")))
					(setq VlaRangeCol (vlax-get-property VlaRange "Columns"))
					(setq CheckHiddenCol (vlax-variant-value (vlax-get-property VlaRangeCol "Hidden")))
					(if
						(and
							(= CheckHiddenRow :vlax-false)
							(= CheckHiddenCol :vlax-false)
						)
						(CAEX_ADD_LISTDATATABLE_FROM_EXCEL VlaRange)
					)
					(setq NumCol (+ NumCol 1))
				)
				(setq NumRow (+ NumRow 1))
			)

			(setq NumCol NumColStart)
			(setq ListDataCoordinateX (list 0.0))
			(repeat NumColTotal
				(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS (list NumCol 0)))
				(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
				(setq VlaRange (vlax-get-property VlaRange "Columns"))
				(setq WidthColumn (variant-value (vlax-get-property VlaRange "ColumnWidth")))
				(setq ListDataCoordinateX (append ListDataCoordinateX (list (+ (last ListDataCoordinateX) WidthColumn))))
				(setq NumCol (+ NumCol 1))
			)

			(setq NumRow NumRowStart)
			(setq ListDataCoordinateY (list 0.0))
			(repeat NumRowTotal
				(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS (list 0 NumRow)))
				(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
				(setq VlaRange (vlax-get-property VlaRange "Rows"))
				(setq HeightRow (variant-value (vlax-get-property VlaRange "RowHeight")))
				(setq ListDataCoordinateY (append ListDataCoordinateY (list (+ (last ListDataCoordinateY) HeightRow))))
				(setq NumRow (+ NumRow 1))
			)
		)
	)

	(if CheckCloseApp
		(progn
			(vlax-invoke-method VlaAppExcel "Quit")
			(vlax-release-object VlaAppExcel)
		)
	)
	(list ListDataTable ListDataCoordinateX ListDataCoordinateY)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_ADD_LISTDATATABLE_FROM_EXCEL ( VlaRange / 
	Alignment
	DataTable
	ListAlignment
	ListNameProperty
	ListAddressMerge
	ListValueProperty
	ListValuePropertyTemp
	ListStringContent
	FontBold
	FontItalic
	FontName
	HeightText
	Num
	Rotation
	StringAddressMerge
	StringContent
	VlaFont
	VlaRangeMerge
	ValueProperty)

	(setq VlaRangeMerge (vlax-get-property VlaRange "MergeArea"))
	(setq StringAddressMerge (vlax-get-property VlaRangeMerge "Address" :vlax-false :vlax-false 1 :vlax-false :vlax-false))
	(setq ListAddressMerge (CAEX_STRINGADDRESS_TO_LISTADDRESS StringAddressMerge))
	(if (= (length ListAddressMerge) 2)
		(setq ListAddressMerge (append ListAddressMerge ListAddressMerge))
	)
	(setq ListAddressMerge
		(list
			(- (nth 0 ListAddressMerge) NumColStart)
			(- (nth 1 ListAddressMerge) NumRowStart)
			(- (nth 2 ListAddressMerge) NumColStart)
			(- (nth 3 ListAddressMerge) NumRowStart)
		)
	)

	(if (not (assoc (list 1 ListAddressMerge) ListDataTable))
		(progn
			(setq StringContent (vlax-variant-value (vlax-get-property VlaRange "Text")))
			(setq ListStringContent (CAEX_STRING_TO_LIST_NEW StringContent "\n"))
			(setq VlaFont (vlax-get-property VlaRange "Font"))
			(setq ListNameProperty (list "Name" "Bold" "Italic" "Size"))
			(foreach NameProperty ListNameProperty
				(setq ValueProperty (vlax-variant-value (vlax-get-property VlaFont NameProperty)))
				(if (not ValueProperty)
					(progn
						(setq ListValuePropertyTemp Nil)
						(setq Num 1)
						(repeat (strlen StringContent)
							(setq VlaCharacters (vlax-get-property VlaRange "Characters" Num 1))
							(setq VlaFontChar (vlax-get-property VlaCharacters "Font"))
							(setq ValuePropertyTemp (vlax-variant-value (vlax-get-property VlaFontChar NameProperty)))
							(setq ListValuePropertyTemp (cons ValuePropertyTemp ListValuePropertyTemp))
							(setq Num (+ Num 1))
						)
						(setq ValueProperty (CAEX_FIND_VALUE_POPULAR ListValuePropertyTemp))
					)
				)
				(setq ListValueProperty (append ListValueProperty (list ValueProperty)))
			)
			(setq FontName (nth 0 ListValueProperty))
			(setq FontBold (nth 1 ListValueProperty))
			(setq FontItalic (nth 2 ListValueProperty))
			(setq HeightText (nth 3 ListValueProperty))

			(setq Rotation (vlax-variant-value (vlax-get-property VlaRange "Orientation")))
			(cond
				((= Rotation -4128)
					(setq Rotation 0.0)
				)
				((= Rotation -4171)
					(setq Rotation 90.0)
				)
				((= Rotation -4170)
					(setq Rotation -90.0)
				)
				((= Rotation -4166)
					(setq Rotation -90.0)
				)
			)
			(setq Rotation (CAEX_ROUNDOFF_NUMBER Rotation 90.0))
			(setq Rotation (* (/ Rotation 180.0) Pi))

			(if (= Rotation 0.0)
				(progn
					(setq ListAlignment
						(list
							(cons -4108 "Center")
							(cons 7 "Center")
							(cons -4117 "Center")
							(cons 5 "Center")
							(cons 1 "Center")
							(cons -4130 "Center")
							(cons -4131 "Left")
							(cons -4152 "Right")
						)
					)
					(setq Alignment (vlax-variant-value (vlax-get-property VlaRange "HorizontalAlignment")))
					(setq Alignment (cdr (assoc Alignment ListAlignment)))
					(setq Rotation 0.0)
				)
				(progn
					(setq ListAlignment
						(list
							(cons -4107 "Bottom")
							(cons -4108 "Center")
							(cons -4117 "Center")
							(cons -4130 "Center")
							(cons -4160 "Top")
						)
					)
					(setq Alignment (vlax-variant-value (vlax-get-property VlaRange "VerticalAlignment")))
					(setq Alignment (cdr (assoc Alignment ListAlignment)))
				)
			)

			(setq DataTable
				(list
					(list 1 ListAddressMerge)
					(list 2 ListStringContent)
					(list 3 FontName)
					(list 4 FontBold)
					(list 5 FontItalic)
					(list 6 HeightText)
					(list 7 Alignment)
					(list 8 Rotation)
				)
			)
			(setq ListDataTable (append ListDataTable (list DataTable)))
		)
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CREATE_TABLE_FOR_CAD ( /
	Alignment
	CodeAlignment
	DeltaX
	DeltaY
	FontBold
	FontItalic
	FontName
	GapText
	HeightText
	LengthMax
	LengthMaxX
	LengthMaxY
	LengthText
	LengthTextX
	LengthTextY
	ListAddress
	ListDataBoundary
	ListStringContent
	ListVlaObjectOfTable
	ListVlaObjectTextNew
	NameTextStyle
	NumText
	Point1
	Point2
	PointMinX
	PointMinY
	PointMaxX
	PointMaxY
	Point1Pre
	Point2Pre
	PointText
	PointTextBase
	PointTextTemp
	PointTextX
	PointTextY
	Rotation
	ValueScaleText
	VlaObjectLine
	VlaObjectText)

	(foreach NodeLine ListDataLine
		(setq Point1
			(list
				(CAEX_NODEX_TO_POINTX (nth 0 NodeLine))
				(CAEX_NODEY_TO_POINTY (nth 1 NodeLine))
				0.0
			)
		)
		(setq Point2
			(list
				(CAEX_NODEX_TO_POINTX (nth 2 NodeLine))
				(CAEX_NODEY_TO_POINTY (nth 3 NodeLine))
				0.0
			)
		)
		(if
			(or
				(not (equal Point1 Point1Pre))
				(not (equal Point2 Point2Pre))
            )
			(progn
				(setq VlaObjectLine (vla-addline VlaSpace (vlax-3d-point Point1) (vlax-3d-point Point2)))
				(setq ListVlaObjectOfTable (cons VlaObjectLine ListVlaObjectOfTable))
				(setq Point1Pre Point1)
				(setq Point2Pre Point2)
            )
        )
	)

	(foreach DataTable ListDataTable
		(setq ListAddress (nth 1 (assoc 1 DataTable)))
		(setq ListStringContent (nth 1 (assoc 2 DataTable)))
		(setq FontName (nth 1 (assoc 3 DataTable)))
		(setq FontBold (nth 1 (assoc 4 DataTable)))
		(setq FontItalic (nth 1 (assoc 5 DataTable)))
		(setq HeightText (* (nth 1 (assoc 6 DataTable)) ScaleGlobal))
		(setq Alignment (nth 1 (assoc 7 DataTable)))
		(setq Rotation (nth 1 (assoc 8 DataTable)))

		(setq PointMinX (CAEX_NODEX_TO_POINTX (nth 0 ListAddress)))
		(setq PointMinY (CAEX_NODEY_TO_POINTY (+ (nth 3 ListAddress) 1)))
		(setq PointMaxX (CAEX_NODEX_TO_POINTX (+ (nth 2 ListAddress) 1)))
		(setq PointMaxY (CAEX_NODEY_TO_POINTY (nth 1 ListAddress)))

		(setq NameTextStyle (CAEX_FIND_NAMETEXTSTYLE FontName FontBold FontItalic))
		(setq ListVlaObjectTextNew Nil)

		(setq NumText (length ListStringContent))
		(if (= Rotation 0.0)
			(progn
				(setq GapText (/ (- PointMaxY PointMinY (* NumText HeightText)) (+ NumText 1)))
				(cond
					((= Alignment "Left")
						(setq PointTextX (+ PointMinX (* HeightText 0.5)))
						(setq CodeAlignment acAlignmentBottomLeft)
					)
					((= Alignment "Center")
						(setq PointTextX (+ PointMinX (* (- PointMaxX PointMinX) 0.5)))
						(setq CodeAlignment acAlignmentBottomCenter)
					)
					((= Alignment "Right")
						(setq PointTextX (- PointMaxX (* HeightText 0.5)))
						(setq CodeAlignment acAlignmentBottomRight)
					)
				)
				(setq PointTextY (- PointMaxY GapText (* HeightText 1.0)))
				(setq PointText (list PointTextX PointTextY 0.0))
				(foreach StringContent ListStringContent
					(if (/= StringContent "")
						(progn
							(setq VlaObjectText (vla-AddText VlaSpace StringContent (vlax-3d-point PointText) HeightText))
							(setq ListVlaObjectTextNew (cons VlaObjectText ListVlaObjectTextNew))
							(setq ListVlaObjectOfTable (cons VlaObjectText ListVlaObjectOfTable))
							(vla-put-alignment VlaObjectText CodeAlignment)
							(setq PointTextTemp
								(list
									(nth 0 PointText)
									(- (nth 1 PointText) (nth 1 (vlax-safearray->list (vlax-variant-value (vla-get-InsertionPoint VlaObjectText)))))
									(nth 2 PointText)
								)
							)
							(vla-put-TextAlignmentPoint VlaObjectText (vlax-3d-point PointTextTemp))
							(vla-put-StyleName VlaObjectText NameTextStyle)
						)
					)
					(setq PointTextY (- PointTextY GapText HeightText))
					(setq PointText (list PointTextX PointTextY 0.0))
				)
			)
			(progn
				(setq GapText (/ (- PointMaxX PointMinX (* NumText HeightText)) (+ NumText 1)))
				(cond
					((= Alignment "Bottom")
						(progn
							(setq PointTextY (+ PointMinY (* HeightText 0.5)))
							(if (= Rotation (* Pi 0.5))
								(setq CodeAlignment acAlignmentBottomLeft)
								(setq CodeAlignment acAlignmentBottomRight)
							)
						)
					)
					((= Alignment "Center")
						(setq PointTextY (+ PointMinY (* (- PointMaxY PointMinY) 0.5)))
						(setq CodeAlignment acAlignmentBottomCenter)
					)
					((= Alignment "Top")
						(progn
							(setq PointTextY (- PointMaxY (* HeightText 0.5)))
							(if (= Rotation (* Pi 0.5))
								(setq CodeAlignment acAlignmentBottomRight)
								(setq CodeAlignment acAlignmentBottomLeft)
							)
						)
					)
				)
				(if (= Rotation (* Pi 0.5))
					(progn
						(setq PointTextX (+ PointMinX GapText (* HeightText 1.0)))
					)
					(progn
						(setq ListStringContent (reverse ListStringContent))
						(setq PointTextX (+ PointMinX GapText (* HeightText 0.0)))
					)
				)
				(setq PointText (list PointTextX PointTextY 0.0))
				(foreach StringContent ListStringContent
					(if (/= StringContent "")
						(progn
							(setq VlaObjectText (vla-AddText VlaSpace StringContent (vlax-3d-point PointText) HeightText))
							(setq ListVlaObjectTextNew (cons VlaObjectText ListVlaObjectTextNew))
							(setq ListVlaObjectOfTable (cons VlaObjectText ListVlaObjectOfTable))
							(vla-put-Rotation VlaObjectText Rotation)
							(vla-put-alignment VlaObjectText CodeAlignment)
							(setq PointTextTemp
								(list
									(- (nth 0 PointText) (nth 0 (vlax-safearray->list (vlax-variant-value (vla-get-InsertionPoint VlaObjectText)))))
									(nth 1 PointText)
									(nth 2 PointText)
								)
							)
							(vla-put-TextAlignmentPoint VlaObjectText (vlax-3d-point PointTextTemp))
							(vla-put-StyleName VlaObjectText NameTextStyle)
						)
					)
					(setq PointTextX (+ PointTextX GapText HeightText))
					(setq PointText (list PointTextX PointTextY 0.0))
				)
			)
		)

		(setq ListDataBoundary (mapcar 'CAEX_GET_POINTMIN_POINTMAX_TEXT_MTEXT ListVlaObjectTextNew))
		(setq ListDataBoundary (vl-remove ListDataBoundary Nil))
		(if ListDataBoundary
			(progn
				(setq LengthTextX
					(-
						(apply 'max (mapcar '(lambda (x) (nth 0 (nth 1 x))) ListDataBoundary))
						(apply 'min (mapcar '(lambda (x) (nth 0 (nth 0 x))) ListDataBoundary))
					)
				)
				(setq LengthTextY
					(-
						(apply 'max (mapcar '(lambda (x) (nth 1 (nth 1 x))) ListDataBoundary))
						(apply 'min (mapcar '(lambda (x) (nth 1 (nth 0 x))) ListDataBoundary))
					)
				)
			)
			(progn
				(setq LengthTextX 0.0)
				(setq LengthTextY 0.0)
			)
		)
		(setq LengthMaxX (- PointMaxX PointMinX (* HeightText 0.1)))
		(setq DeltaX (- LengthMaxX LengthTextX))

		(setq LengthMaxY (- PointMaxY PointMinY (* HeightText 0.1)))
		(setq DeltaY (- LengthMaxY LengthTextY))
		(if
			(or
				(< DeltaX 0.0)
				(< DeltaY 0.0)
			)
			(progn
				(if (< DeltaX DeltaY)
					(progn
						(setq LengthMax LengthMaxX)
						(setq LengthText LengthTextX)
					)
					(progn
						(setq LengthMax LengthMaxY)
						(setq LengthText LengthTextY)
					)
				)
				(setq ValueScaleText (/ LengthMax LengthText))

				(if (= Rotation 0.0)
					(progn
						(cond
							((= Alignment "Left")
								(setq PointTextBase
									(list
										PointMinX
										(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
										0.0
									)
								)
							)
							((= Alignment "Center")
								(setq PointTextBase
									(list
										(+ PointMinX (* (- PointMaxX PointMinX) 0.5))
										(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
										0.0
									)
								)
							)
							((= Alignment "Right")
								(setq PointTextBase
									(list
										PointMaxX
										(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
										0.0
									)
								)
							)
						)
					)
					(progn
						(cond
							((= Alignment "Left")
								(setq PointTextBase
									(list
										PointMinX
										(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
										0.0
									)
								)
							)
							((= Alignment "Center")
								(setq PointTextBase
									(list
										(+ PointMinX (* (- PointMaxX PointMinX) 0.5))
										(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
										0.0
									)
								)
							)
							((= Alignment "Right")
								(setq PointTextBase
									(list
										PointMaxX
										(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
										0.0
									)
								)
							)
						)
					)
				)

				(foreach VlaObjectText ListVlaObjectTextNew
					(vla-ScaleEntity VlaObjectText (vlax-3d-point PointTextBase) ValueScaleText)
				)
			)
		)
	)
	ListVlaObjectOfTable
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_FIND_NAMETEXTSTYLE ( FontName FontBold FontItalic / 
	Charset
	NameTextStyle
	PitchAndFamily
	VlaTextStyle
	VlaTextStylesGroup)

	(setq NameTextStyle (strcat "CLE-" (strcase FontName)))
	(if (= FontBold :vlax-true)
		(setq NameTextStyle (strcat NameTextStyle "-B"))
	)
	(if (= FontItalic :vlax-true)
		(setq NameTextStyle (strcat NameTextStyle "-I"))
	)
	(setq VlaTextStylesGroup (vla-get-textstyles VlaDrawingCurrent))
	(vl-catch-all-apply (function (lambda ( / )
		(setq VlaTextStyle (vla-item VlaTextStylesGroup NameTextStyle))
	)))
	(if (not VlaTextStyle)
		(setq VlaTextStyle (vla-add VlaTextStylesGroup NameTextStyle))
	)
	(setq Charset 1)
	(setq PitchAndFamily 0)

	(if
		(vl-catch-all-error-p (vl-catch-all-apply (function (lambda ( / )
			(vla-SetFont VlaTextStyle FontName FontBold FontItalic Charset PitchAndFamily)
		))))
		(progn
			(setq FontName "Arial")
			(vla-SetFont VlaTextStyle FontName FontBold FontItalic Charset PitchAndFamily)
		)
	)
	(vla-put-Height VlaTextStyle 0.0)
	(vla-put-width VlaTextStyle 0.7)
	(CAEX_PUT_ANNOTATIVE_TEXTSTYLE VlaTextStyle Nil)
	(CAEX_PUT_ORIENTATION_TEXTSTYLE VlaTextStyle Nil)
	NameTextStyle
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_PUT_ANNOTATIVE_TEXTSTYLE ( VlaTextStyle ModeAnnotative / EnameTextStyle )
	(setq EnameTextStyle (vlax-vla-object->ename VlaTextStyle))
	(if ModeAnnotative
		(entmod (list (cons -1 EnameTextStyle) (list -3 (list "AcadAnnotative" (cons 1000 "AnnotativeData") (cons 1002 "{") (cons 1070 1) (cons 1070 1) (cons 1002 "}")))))
		(entmod (list (cons -1 EnameTextStyle) (list -3 (cons "AcadAnnotative" Nil))))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_PUT_ORIENTATION_TEXTSTYLE ( VlaObject ModeOrientation / EnameTextStyle)

	(setq EnameTextStyle (vlax-vla-object->ename VlaObject))
	(if ModeOrientation
		(entmod (list (cons -1 EnameTextStyle) (list -3 (cons "AcadAnnoPO" (list (cons 1070 1))))))
		(entmod (list (cons -1 EnameTextStyle) (list -3 (cons "AcadAnnoPO" Nil))))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_NODEX_TO_POINTX ( NodeX / PointX)
	(setq PointX (+ 0.0 (* (nth NodeX ListDataCoordinateX) ScaleGlobal 11.64)))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_NODEY_TO_POINTY ( NodeY / PointY)
	(setq PointY (- 0.0 (* (nth NodeY ListDataCoordinateY) ScaleGlobal 1.91)))
)
-------------------------------------------------------------------------------------------------------------------
(defun C:CTE ( / 
	CheckActiveCell
	CheckRun
	FullNameFileXlsx
	GroupListAddress
	ListCoordinateX
	ListCoordinateY
	ListDataLineX
	ListDataLineY
	ListDataTable
	ListTemp
	ListVarSystem_OldValue
	ListVlaLayerLock
	ListVlaObjectLine
	ListVlaObjectDelete
	ListVlaObjectText
	SeparatorCSV
	ToleranceValue
	VlaDrawingCurrent)

	-------------------------------------------------------------------------------------------------------------------
	
	(vl-load-com)
	(setq VlaDrawingCurrent (vla-get-activedocument (vlax-get-acad-object)))
	(vla-startundomark VlaDrawingCurrent)
	(setq ToleranceValue 1e-8)
	(CAEX_SET_VARSYSTEM_C2E)
	(CAEX_CREATE_LISTVLALAYERLOCK)

	(vl-catch-all-apply (function (lambda ( / )
		(setq SeparatorCSV (vl-registry-read "HKEY_CURRENT_USER\\Control Panel\\International" "sList"))
		(if (not SeparatorCSV)
			(setq SeparatorCSV ",")
		)

		(setq ListTemp (CAEX_SELECT_TABLE_IN_CAD))
		(setq ListVlaObjectLine (nth 0 ListTemp))
		(setq ListVlaObjectText (nth 1 ListTemp))
		(setq ListVlaObjectDelete (nth 2 ListTemp))

		(if
			(and
				ListVlaObjectLine
				ListVlaObjectText
			)
			(progn
				(setq CheckActiveCell (CAEX_CHECKACTIVECELL))
				(if CheckActiveCell
					(setq CheckRun T)
					(progn
						(setq FullNameFileXlsx (getfiled "Select Output File" (CAEX_GET_FULLNAMEFILEXLSX "xlsx") "xlsx" 1))
						(if FullNameFileXlsx
							(setq CheckRun T)
						)
					)
				)
			)
		)

		(if CheckRun
			(progn
				(setq ListTemp (CAEX_GET_LISTDATALINE_FROM_CAD))
				(setq ListDataLineX (nth 0 ListTemp))
				(setq ListDataLineY (nth 1 ListTemp))
				(setq ListCoordinateX (mapcar 'car ListDataLineX))
				(setq ListCoordinateY (mapcar 'car ListDataLineY))

				(setq GroupListAddress (CAEX_GET_GROUPLISTADDRESS_FROM_CAD))
				(setq ListDataTable (CAEX_GET_LISTDATATABLE_FROM_CAD))
				(CAEX_CREATE_TABLE_FOR_EXCEL)
			)
		)
	)))

	(mapcar 'vla-delete ListVlaObjectDelete)
	(CAEX_RESTORE_LOCK_LAYER)
	(CAEX_RESET_VARSYSTEM)
	(vla-endundomark VlaDrawingCurrent)
	(princ)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CHECKACTIVECELL ( / 
	CheckActiveCell
	CheckCloseApp
	VlaAppExcel
	VlaRangeActive
	VlaWorkbooks)

	(setq VlaAppExcel (vlax-get-Object "Excel.Application"))
	(if VlaAppExcel
		(progn
			(setq VlaRangeActive (vlax-get-property VlaAppExcel "Selection"))
			(if VlaRangeActive
				(setq CheckActiveCell T)
			)

			(setq VlaWorkbooks (vlax-get-property VlaAppExcel "Workbooks"))
			(setq CheckCloseApp (= (vla-get-count VlaWorkbooks) 0))
			(if CheckCloseApp
				(progn
					(vlax-invoke-method VlaAppExcel "Quit")
					(vlax-release-object VlaAppExcel)
				)
			)
		)
	)
	CheckActiveCell
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_SELECT_TABLE_IN_CAD ( / 
	ListVlaObjectLine
	ListVlaObjectDelete
	ListVlaObjectSelect
	ListVlaObjectTemp
	ListVlaObjectText
	SelectionFilter
	SelectionSet
	SelectionSetTemp
	TypeObject
	VlaObjectCopy)

	(setq SelectionFilter
		(list
			(cons -4 "<OR")
			(cons 0 "LINE")
			(cons 0 "LWPOLYLINE")
			(cons 0 "TEXT")
			(cons 0 "MTEXT")
			(cons -4 "OR>")
		)
	)
	(setq SelectionSet (ssget SelectionFilter))
	(setq ListVlaObjectSelect (CAEX_CONVERT_SELECTIONSET_TO_LISTVLAOBJECT SelectionSet))
	(foreach VlaObjectSelect ListVlaObjectSelect
		(setq TypeObject (vla-get-ObjectName VlaObjectSelect))
		(if (= TypeObject "AcDbPolyline")
			(progn
				(setq VlaObjectCopy (vla-copy VlaObjectSelect))
				(setq SelectionSetTemp (acet-explode (vlax-vla-object->ename VlaObjectCopy)))
				(setq ListVlaObjectTemp (CAEX_CONVERT_SELECTIONSET_TO_LISTVLAOBJECT SelectionSetTemp))
				(foreach VlaObjectTemp ListVlaObjectTemp
					(if (= (vla-get-ObjectName VlaObjectTemp) "AcDbLine")
						(setq ListVlaObjectLine (cons VlaObjectTemp ListVlaObjectLine))
					)
				)
				(setq ListVlaObjectDelete (append ListVlaObjectDelete ListVlaObjectTemp))
			)
		)
		(if (= TypeObject "AcDbLine")
			(setq ListVlaObjectLine (cons VlaObjectSelect ListVlaObjectLine))
		)

		(if
			(or
				(= TypeObject "AcDbText")
				(= TypeObject "AcDbMText")
			)
			(setq ListVlaObjectText (cons VlaObjectSelect ListVlaObjectText))
		)
	)
	(list ListVlaObjectLine ListVlaObjectText ListVlaObjectDelete)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_FULLNAMEFILEXLSX ( Extension / 
	FullNameFileDwg
	FullNameFileXlsx
	NameFileXlsx
	PathFileXlsx)

	(setq FullNameFileDwg (vla-get-fullname VlaDrawingCurrent))
	(setq PathFileXlsx (vl-filename-directory FullNameFileDwg))
	(setq NameFileXlsx (strcat (vl-filename-base FullNameFileDwg) "." Extension))
	(setq FullNameFileXlsx (strcat PathFileXlsx "\\" NameFileXlsx))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTDATALINE_FROM_CAD ( /
	ListDataLineY
	ListDataLineX
	PointEnd
	PointStart
	PointX
	Temp1
	Temp2)

	(foreach VlaObject ListVlaObjectLine
		(setq PointStart (vlax-curve-getStartPoint VlaObject))
		(setq PointEnd (vlax-curve-getEndPoint VlaObject))
		(if (= (CAEX_ROUNDOFF_NUMBER (sin (vla-get-angle VlaObject)) 0.05) 0.0)
			(setq ToleranceValue (max ToleranceValue (abs (- (nth 1 PointStart) (nth 1 PointEnd)))))
		)
		(if (= (CAEX_ROUNDOFF_NUMBER (cos (vla-get-angle VlaObject)) 0.05) 0.0)
			(setq ToleranceValue (max ToleranceValue (abs (- (nth 0 PointStart) (nth 0 PointEnd)))))
		)
	)
	(setq ToleranceValue (* (expt 10.0 (CAEX_ROUNDOFF_NUMBER (/ (log ToleranceValue) (log 10.0)) 1.0)) 2.0))

	(foreach VlaObject ListVlaObjectLine
		(setq PointStart (vlax-curve-getStartPoint VlaObject))
		(setq PointEnd (vlax-curve-getEndPoint VlaObject))
		(setq PointStart (mapcar '(lambda (x) (CAEX_ROUNDOFF_NUMBER x ToleranceValue)) PointStart))
		(setq PointEnd (mapcar '(lambda (x) (CAEX_ROUNDOFF_NUMBER x ToleranceValue)) PointEnd))

		(if (= (CAEX_ROUNDOFF_NUMBER (cos (vla-get-angle VlaObject)) 0.05) 0.0)
			(progn
				(setq PointX (nth 0 PointStart))
				(setq Temp2 (vl-sort (list (nth 1 PointStart) (nth 1 PointEnd)) '<))
				(if (setq Temp1 (assoc PointX ListDataLineX))
					(setq ListDataLineX (subst (append Temp1 (list Temp2)) Temp1 ListDataLineX))
					(setq ListDataLineX (append ListDataLineX (list (list PointX Temp2))))
				)
			)
		)

		(if (= (CAEX_ROUNDOFF_NUMBER (sin (vla-get-angle VlaObject)) 0.05) 0.0)
			(progn
				(setq PointY (nth 1 PointStart))
				(setq Temp2 (vl-sort (list (nth 0 PointStart) (nth 0 PointEnd)) '<))
				(if (setq Temp1 (assoc PointY ListDataLineY))
					(setq ListDataLineY (subst (append Temp1 (list Temp2)) Temp1 ListDataLineY))
					(setq ListDataLineY (append ListDataLineY (list (list PointY Temp2))))
				)
			)
		)
	)

	(setq ListDataLineX (vl-sort ListDataLineX '(lambda (a b) (< (car a) (car b)))))
	(setq ListDataLineY (vl-sort ListDataLineY '(lambda (a b) (> (car a) (car b)))))
	(list ListDataLineX ListDataLineY)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_GROUPLISTADDRESS_FROM_CAD ( / 
	Cell
	Cell2
	CheckMerge
	GroupListAddress
	NodeCell
	NodeCellMerge
	NodeCellTemp
	NumCol
	NumColTotal
	NumRow
	NumRowTotal
	ListCoordinateX
	ListCoordinateY
	ListNodeCell
	ListNodeCell1
	ListNodeCell2
	ListNodeCellHorizontal
	ListNodeCellTemp
	ListNodeCellVertical
	ListNumCol
	ListNumRow
	PointX
	PointY)

	(setq ListCoordinateX (mapcar 'car ListDataLineX))
	(setq ListCoordinateY (mapcar 'car ListDataLineY))
	(setq NumColTotal (- (length ListCoordinateX) 1))
	(setq NumRowTotal (- (length ListCoordinateY) 1))

	(setq NumCol 1)
	(repeat NumColTotal
		(setq NumRow 1)
		(setq NodeCell (list (cons NumCol NumRow)))
		(setq PointX (* (+ (nth (- NumCol 1) ListCoordinateX) (nth NumCol ListCoordinateX)) 0.5))
		(repeat (- NumRowTotal 1)
			(setq PointY (nth NumRow ListCoordinateY))
			(setq CheckMerge T)
			(foreach Temp (cdr (assoc PointY ListDataLineY))
				(if
					(and
						(> PointX (nth 0 Temp))
						(< PointX (nth 1 Temp))
					)
					(setq CheckMerge Nil)
				)
			)
			(setq NumRow (+ NumRow 1))
			(if CheckMerge
				(setq NodeCell (append NodeCell (list (cons NumCol NumRow))))
				(progn
					(if (> (length NodeCell) 1)
						(setq ListNodeCellVertical (append ListNodeCellVertical (list NodeCell)))
					)
					(setq NodeCell (list (cons NumCol NumRow)))
				)
			)
		)
		(if (> (length NodeCell) 1)
			(setq ListNodeCellVertical (append ListNodeCellVertical (list NodeCell)))
		)
		(setq NumCol (+ NumCol 1))
	)

	(setq NumRow 1)
	(repeat NumRowTotal
		(setq NumCol 1)
		(setq NodeCell (list (cons NumCol NumRow)))
		(setq PointY (* (+ (nth (- NumRow 1) ListCoordinateY) (nth NumRow ListCoordinateY)) 0.5))
		(repeat (- NumColTotal 1)
			(setq PointX (nth NumCol ListCoordinateX))
			(setq CheckMerge T)
			(foreach Temp (cdr (assoc PointX ListDataLineX))
				(if
					(and
						(> PointY (nth 0 Temp))
						(< PointY (nth 1 Temp))
					)
					(setq CheckMerge Nil)
				)
			)
			(setq NumCol (+ NumCol 1))
			(if CheckMerge
				(setq NodeCell (append NodeCell (list (cons NumCol NumRow))))
				(progn
					(if (> (length NodeCell) 1)
						(setq ListNodeCellHorizontal (append ListNodeCellHorizontal (list NodeCell)))
					)
					(setq NodeCell (list (cons NumCol NumRow)))
				)
			)
		)
		(if (> (length NodeCell) 1)
			(setq ListNodeCellHorizontal (append ListNodeCellHorizontal (list NodeCell)))
		)
		(setq NumRow (+ NumRow 1))
	)

	(setq ListNodeCell ListNodeCellHorizontal)
	(setq ListNodeCell1 ListNodeCellVertical)
	(foreach NodeCell1 ListNodeCell1
		(setq NodeCellMerge NodeCell1)
		(setq ListNodeCell2 ListNodeCell)
		(foreach NodeCell2 ListNodeCell2
			(setq CheckMerge (CAEX_CHECK_MERGE_NODECELL NodeCell1 NodeCell2))
			(if CheckMerge
				(progn
					(foreach Cell2 NodeCell2
						(if (not (member Cell2 NodeCellMerge))
							(setq NodeCellMerge (append NodeCellMerge (list Cell2)))
						)
					)
					(setq ListNodeCell (vl-remove NodeCell2 ListNodeCell))
				)
			)
		)
		(setq ListNodeCell (append ListNodeCell (list NodeCellMerge)))
	)

	(setq NodeCellTemp (apply 'append ListNodeCell))
	(setq NumCol 1)
	(repeat NumColTotal
		(setq NumRow 1)
		(repeat NumRowTotal
			(setq Cell (cons NumCol NumRow))
			(if (not (member Cell NodeCellTemp))
				(setq ListNodeCell (append ListNodeCell (list (list Cell))))
			)
			(setq NumRow (+ NumRow 1))
		)
		(setq NumCol (+ NumCol 1))
	)

	(setq ListNodeCellTemp ListNodeCell)
	(setq ListNodeCell Nil)
	(foreach NodeCell ListNodeCellTemp
		(setq ListNumCol (vl-sort (mapcar 'car NodeCell) '<))
		(setq ListNumRow (vl-sort (mapcar 'cdr NodeCell) '<))
		(setq NodeCell
			(list
				(cons (car ListNumCol) (car ListNumRow))
				(cons (last ListNumCol) (last ListNumRow))
			)
		)
		(setq ListNodeCell (append ListNodeCell (list NodeCell)))
	)

	(setq GroupListAddress (mapcar '(lambda (x) (list (- (car (nth 0 x)) 1) (- (cdr (nth 0 x)) 1) (- (car (nth 1 x)) 1) (- (cdr (nth 1 x)) 1))) ListNodeCell))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CHECK_MERGE_NODECELL ( NodeCell1 NodeCell2 / CheckMerge)
	(foreach Cell2 NodeCell2
		(if (member Cell2 NodeCell1)
			(setq CheckMerge T)
		)
	)
	CheckMerge
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTDATATABLE_FROM_CAD ( / 
	Alignment
	DistanceAdd
	DistanceDiv
	DistanceSub
	Charset
	FontBold
	FontItalic                
	HeightText
	ListDataTable
	GroupListAddressEmpty
	ListTemp
	ListVlaObjectTextSelect
	PitchAndFamily
	PointMaxX
	PointMaxY
	PointMinX
	PointMinY
	PointTextMax
	PointTextMaxX
	PointTextMaxY
	PointTextMin
	PointTextMinX
	PointTextMinY
	FontName
	NameTextStyle
	Rotation
	SelectionFilter
	SelectionFrame
	SelectionSet
	StringContent
	VlaTextStyle)


	(foreach ListAddress GroupListAddress
		(setq ListVlaObjectTextSelect (CAEX_GET_LISTVLATEXT_IN_CELL ListAddress))
		(if ListVlaObjectTextSelect
			(progn
				(setq PointMinX (nth (nth 0 ListAddress) ListCoordinateX))
				(setq PointMaxY (nth (nth 1 ListAddress) ListCoordinateY))
				(setq PointMaxX (nth (+ (nth 2 ListAddress) 1) ListCoordinateX))
				(setq PointMinY (nth (+ (nth 3 ListAddress) 1) ListCoordinateY))

				(setq ListTemp (CAEX_GET_STRINGCONTENT_MULTIOBJECT ListVlaObjectTextSelect))
				(setq StringContent (nth 0 ListTemp))
				(setq PointTextMin (nth 1 ListTemp))
				(setq PointTextMax (nth 2 ListTemp))
				(setq PointTextMinX (nth 0 PointTextMin))
				(setq PointTextMaxX (nth 0 PointTextMax))
				(setq PointTextMinY (nth 1 PointTextMin))
				(setq PointTextMaxY (nth 1 PointTextMax))

				(setq NameTextStyle (CAEX_FIND_VALUE_POPULAR (mapcar 'CAEX_VLA_GET_STYLENAME ListVlaObjectTextSelect)))
				(setq VlaTextStyle (vla-item (vla-get-textstyles VlaDrawingCurrent) NameTextStyle))
				(vla-GetFont VlaTextStyle 'FontName 'FontBold 'FontItalic 'Charset 'PitchAndFamily)
				(if
					(or
						(not FontName)
						(= FontName "")
					)
					(setq FontName "Arial")
				)
				(setq HeightText (CAEX_FIND_VALUE_POPULAR (mapcar 'vla-get-height ListVlaObjectTextSelect)))

				(setq Rotation (CAEX_FIND_VALUE_POPULAR (mapcar 'vla-get-rotation ListVlaObjectTextSelect)))
				(setq Rotation (* (/ Rotation Pi) 180.0))
				(setq Rotation (CAEX_ROUNDOFF_NUMBER Rotation 90.0))
				(if (> Rotation 90.0)
					(setq Rotation (- Rotation 180.0))
				)

				(if (= Rotation 0.0)
					(progn
						(if (= (- PointMaxX PointTextMaxX) 0.0)
							(setq DistanceDiv (/ (- PointTextMinX PointMinX) ToleranceValue))
							(setq DistanceDiv (/ (- PointTextMinX PointMinX) (- PointMaxX PointTextMaxX)))
						)
						(setq DistanceAdd (+ (- PointTextMinX PointMinX) (- PointMaxX PointTextMaxX)))
						(setq DistanceSub (abs (- (- PointTextMinX PointMinX) (- PointMaxX PointTextMaxX))))
						(if (= DistanceSub 0.0)
							(setq DistanceSub ToleranceValue)
						)
						(setq Alignment Nil)
						(if (>= DistanceDiv 10.0)
							(setq Alignment "Right")
						)
						(if (<= DistanceDiv 1.0)
							(setq Alignment "Left")
						)
						(if (>= (/ DistanceAdd DistanceSub) 5.0)
							(setq Alignment "Center")
						)
						(if (not Alignment)
							(setq Alignment (CAEX_FIND_VALUE_POPULAR (mapcar 'CAEX_FIND_ALIGNMENT_TEXT_MTEXT ListVlaObjectTextSelect)))
						)
					)
					(progn
						(if (= (- PointMaxY PointTextMaxY) 0.0)
							(setq DistanceDiv (/ (- PointTextMinY PointMinY) ToleranceValue))
							(setq DistanceDiv (/ (- PointTextMinY PointMinY) (- PointMaxY PointTextMaxY)))
						)
						(setq DistanceAdd (+ (- PointMaxY PointTextMaxY) (- PointTextMinY PointMinY)))
						(setq DistanceSub (abs (- (- PointTextMinY PointMinY) (- PointMaxY PointTextMaxY))))
						(if (= DistanceSub 0.0)
							(setq DistanceSub ToleranceValue)
						)
						(setq Alignment Nil)
						(if (>= DistanceDiv 10.0)
							(setq Alignment "Top")
						)
						(if (<= DistanceDiv 0.1)
							(setq Alignment "Bottom")
						)
						(if (>= (/ DistanceAdd DistanceSub) 5.0)
							(setq Alignment "Center")
						)
						(if (not Alignment)
							(setq Alignment (CAEX_FIND_VALUE_POPULAR (mapcar 'CAEX_FIND_ALIGNMENT_TEXT_MTEXT ListVlaObjectTextSelect)))
						)
					)
				)

				(setq ListDataTable
					(cons
						(list
							(list 0 (list (nth 0 ListAddress) (nth 1 ListAddress)))
							(list 1 ListAddress)
							(list 2 StringContent)
							(list 3 FontName)
							(list 4 FontBold)
							(list 5 FontItalic)
							(list 6 HeightText)
							(list 7 Alignment)
							(list 8 Rotation)
							(list 9 ListVlaObjectTextSelect)
						)
						ListDataTable
					)
				)
			)
			(setq GroupListAddressEmpty (append GroupListAddressEmpty (list ListAddress)))
		)
	)

	(if GroupListAddressEmpty
		(progn
			(setq StringContent "")
			(setq FontName (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 3 (cdr x)))) ListDataTable)))
			(setq FontBold (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 4 (cdr x)))) ListDataTable)))
			(setq FontItalic (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 5 (cdr x)))) ListDataTable)))
			(setq HeightText (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 6 (cdr x)))) ListDataTable)))
			(setq Alignment (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 7 (cdr x)))) ListDataTable)))
			(setq Rotation (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 8 (cdr x)))) ListDataTable)))
			(if
				(or
					(not FontName)
					(= FontName "")
				)
				(setq FontName "Arial")
			)
			(if (not FontBold)
				(setq FontBold :vlax-false)
			)
			(if (not FontItalic)
				(setq FontItalic :vlax-false)
			)
			(if (not HeightText)
				(setq HeightText 12.0)
			)
			(if (not Alignment)
				(setq Alignment "Center")
			)
			(if (not Rotation)
				(setq Rotation 0.0)
			)
			(foreach ListAddress GroupListAddressEmpty
				(setq ListDataTable
					(cons
						(list
							(list 0 (list (nth 0 ListAddress) (nth 1 ListAddress)))
							(list 1 ListAddress)
							(list 2 StringContent)
							(list 3 FontName)
							(list 4 FontBold)
							(list 5 FontItalic)
							(list 6 HeightText)
							(list 7 Alignment)
							(list 8 Rotation)
							(list 9 Nil)
						)
						ListDataTable
					)
				)
			)
		)
	)
	ListDataTable
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTVLATEXT_IN_CELL ( ListAddress /
	ListTemp
	ListVlaObjectTextSelect
	ListVlaTextTemp
	P_MaxX1
	P_MaxX2
	P_MaxY1
	P_MaxY2
	P_MinX1
	P_MinX2
	P_MinY1
	P_MinY2
	SelectionFilter
	SelectionFrame
	SelectionSet)

	(setq SelectionFilter
		(list
			(cons -4 "<OR")
			(cons 0 "TEXT")
			(cons 0 "MTEXT")
			(cons -4 "OR>")
		)
	)
	(setq P_MinX1 (nth (nth 0 ListAddress) ListCoordinateX))
	(setq P_MaxY1 (nth (nth 1 ListAddress) ListCoordinateY))
	(setq P_MaxX1 (nth (+ (nth 2 ListAddress) 1) ListCoordinateX))
	(setq P_MinY1 (nth (+ (nth 3 ListAddress) 1) ListCoordinateY))
	(setq SelectionFrame
		(list
			(list P_MinX1 P_MinY1)
			(list P_MaxX1 P_MinY1)
			(list P_MaxX1 P_MaxY1)
			(list P_MinX1 P_MaxY1)
		)
	)

	(setq SelectionSet (ssget "_CP" SelectionFrame SelectionFilter))
	(setq ListVlaTextTemp (CAEX_CONVERT_SELECTIONSET_TO_LISTVLAOBJECT SelectionSet))
	(foreach VlaTextTemp ListVlaTextTemp
		(if (member VlaTextTemp ListVlaObjectText)
			(progn
				(setq ListTemp (CAEX_GET_POINTMIN_POINTMAX_TEXT_MTEXT VlaTextTemp))
				(setq P_MinX2 (nth 0 (nth 0 ListTemp)))
				(setq P_MinY2 (nth 1 (nth 0 ListTemp)))
				(setq P_MaxX2 (nth 0 (nth 1 ListTemp)))
				(setq P_MaxY2 (nth 1 (nth 1 ListTemp)))
				(if
					(and
						(<= P_MinX1 P_MinX2)
						(>= P_MaxX1 P_MaxX2)
						(<= P_MinY1 P_MinY2)
						(>= P_MaxY1 P_MaxY2)
					)
					(setq ListVlaObjectTextSelect (cons VlaTextTemp ListVlaObjectTextSelect))
				)
			)
		)
	)
	ListVlaObjectTextSelect
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CREATE_TABLE_FOR_EXCEL ( / 
	DataTable
	FileCsv
	FullNameFileCsv
	ListAddress
	ListDataTableRaw
	ListStringContent
	NumCol
	NumColTotal
	NumRow
	NumRowTotal
	StringContent
	StringValue)

	(setq NumColTotal (- (length ListCoordinateX) 1))
	(setq NumRowTotal (- (length ListCoordinateY) 1))

	(setq NumRow 0)
	(repeat NumRowTotal
		(setq NumCol 0)
		(setq ListStringContent Nil)
		(repeat NumColTotal
			(setq ListAddress (list NumCol NumRow))
			(if (setq DataTable (assoc (list 0 ListAddress) ListDataTable))
				(setq StringContent (nth 1 (assoc 2 DataTable)))
				(setq StringContent "")
			)
			(setq ListStringContent (append ListStringContent (list StringContent)))
			(setq NumCol (+ NumCol 1))
		)
		(setq ListDataTableRaw (append ListDataTableRaw (list ListStringContent)))
		(setq NumRow (+ NumRow 1))
	)

	(setq FullNameFileCsv (strcat (getvar "TEMPPREFIX") (CAEX_RANDOM_STRING 8) ".csv"))
	(while (CAEX_CHECK_FILE_EXIST FullNameFileCsv)
		(setq FullNameFileCsv (strcat (getvar "TEMPPREFIX") (CAEX_RANDOM_STRING 8) ".csv"))
	)

	(setq FileCsv (open FullNameFileCsv "w"))
	(setq StringValue (strcat "sep=" SeparatorCsv))
	(write-line StringValue FileCsv)

	(setq StringValue "")
	(write-line StringValue FileCsv)
	(foreach DataTableRaw ListDataTableRaw
		(setq DataTableRaw (cons "" DataTableRaw))
		(setq StringValue (CAEX_LIST_TO_STRING DataTableRaw SeparatorCsv))
		(write-line StringValue FileCsv)
	)
	(close FileCsv)

	(CAEX_PROCESS_CSV)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_PROCESS_CSV ( / 
	Alignment
	ColumnWidth
	DataTable
	FontBold
	FontItalic
	HeightText
	ListAddress
	ListAlignment
	ListFontProperty
	FontName
	NameSheet
	NumCol
	NumColStart
	NumColTotal
	NumRow
	NumRowStart
	NumRowTotal
	Rotation
	RowHeight
	ScaleGlobal
	StringAddress
	VlaAppExcel
	VlaBorders
	VlaFont
	VlaRange
	VlaRangeActive
	VlaSheet
	VlaSheets
	VlaWorkbook
	VlaWorkbooks)

	(setq NumColTotal (- (length ListCoordinateX) 1))
	(setq NumRowTotal (- (length ListCoordinateY) 1))

	(setq VlaAppExcel (vlax-get-or-create-Object "Excel.Application"))
	(if CheckActiveCell
		(progn
			(vlax-put-property VlaAppExcel "Visible" :vlax-true)
			(setq VlaSheetActive (vlax-get-property VlaAppExcel "ActiveSheet"))
			(setq VlaRangeActive (vlax-get-property VlaAppExcel "Selection"))
			(setq StringAddress (vlax-get-property VlaRangeActive "Address" :vlax-false :vlax-false 1 :vlax-false :vlax-false))
			(setq ListAddress (CAEX_STRINGADDRESS_TO_LISTADDRESS StringAddress))
			(setq NumColStart (nth 0 ListAddress))
			(setq NumRowStart (nth 1 ListAddress))

			(setq ListAddress (list NumColStart NumRowStart (+ NumColStart NumColTotal -1) (+ NumRowStart NumRowTotal -1)))
			(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
			(setq VlaRangeActive (vlax-get-property VlaSheetActive "Range" StringAddress))
			(vlax-put-property VlaRangeActive "MergeCells" 0)
			(vlax-invoke-method VlaRangeActive "ClearFormats")
		)
		(progn
			(vlax-put-property VlaAppExcel "Visible" :vlax-false)
			(setq NumColStart 1)
			(setq NumRowStart 1)
		)
	)
	(setq VlaWorkbooks (vlax-get-property VlaAppExcel "Workbooks"))
	(setq VlaWorkbook (vlax-invoke-method VlaWorkbooks "Open" FullNameFileCsv))
	(setq VlaSheets (vlax-get-property VlaWorkbook "Sheets"))
	(setq VlaSheet (vlax-get-property VlaSheets "Item" 1))

	(if CheckActiveCell
		(progn
			(setq VlaRange (vlax-get-property VlaSheet "Range" (CAEX_LISTADDRESS_TO_STRINGADDRESS (list 1 1 NumColTotal NumRowTotal))))
			(vlax-invoke-method VlaRange "Copy")
			(vlax-invoke-method VlaRangeActive "PasteSpecial" -4163 -4142 :vlax-false :vlax-false)
			(vlax-put-property VlaAppExcel "CutCopyMode" :vlax-false)
			(vlax-invoke-method VlaWorkbook "Save")
			(vlax-invoke-method VlaWorkbook "Close")
			(setq VlaSheet VlaSheetActive)
		)

		(progn
			(if
				(vl-catch-all-error-p (vl-catch-all-apply (function (lambda ( / )
					(setq NameSheet (vl-filename-base FullNameFileXlsx))
					(vlax-put-property VlaSheet "Name" NameSheet)
				))))
				(progn
					(setq NameSheet "Data Cad")
					(vlax-put-property VlaSheet "Name" NameSheet)
				)
			)

			(setq VlaRange (vlax-get-property VlaSheet "Cells"))
			(vlax-invoke-method VlaRange "ClearFormats")
			(vlax-put-property VlaRange "VerticalAlignment" -4108)
			(vlax-put-property VlaRange "HorizontalAlignment" -4108)
			(setq VlaFont (vlax-get-property VlaRange "Font"))
			(setq ListFontProperty
				(list
					(cons "Background" Nil)
					(cons "Bold" 0)
					(cons "Color" 0.0)
					(cons "ColorIndex" 1)
					(cons "FontStyle" "Regular")
					(cons "Italic" 0)
					(cons "Name" "Arial")
					(cons "Size" 12)
					(cons "Strikethrough" 0)
					(cons "Subscript" 0)
					(cons "ThemeColor" 2)
					(cons "ThemeFont" 0)
					(cons "TintAndShade" 0.0)
					(cons "Underline" -4142)
				)
			)
			(foreach FontProperty ListFontProperty
				(vlax-put-property VlaFont (car FontProperty) (cdr FontProperty))
			)
			(vlax-put-property VlaRange "RowHeight" 25)
			(vlax-put-property VlaRange "ColumnWidth" 30)

			(setq VlaRange (vlax-get-property VlaSheet "Range" (CAEX_LISTADDRESS_TO_STRINGADDRESS (list 1 1 NumColTotal NumRowTotal))))
			(vlax-invoke-method VlaRange "Copy")
			(vlax-invoke-method VlaRange "PasteSpecial" -4163 -4142 :vlax-false :vlax-false)
			(vlax-put-property VlaAppExcel "CutCopyMode" :vlax-false)
		)
	)

	(setq ListAddress (list NumColStart NumRowStart (+ NumColStart NumColTotal -1) (+ NumRowStart NumRowTotal -1)))
	(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
	(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
	(vlax-put-property VlaRange "VerticalAlignment" -4108)
	(vlax-put-property VlaRange "HorizontalAlignment" -4108)

	(setq VlaBorders (vlax-get-property VlaRange "Borders"))
	(foreach BordersIndex (list 7 8 9 10 11 12)
		(setq VlaBorder (vlax-get-property VlaBorders "Item" BordersIndex))
		(vlax-put-property VlaBorder "LineStyle" 1)
		(vlax-put-property VlaBorder "Weight" 2)
	)

	(setq ScaleGlobal (/ 12.0 (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 6 x))) (mapcar 'cdr ListDataTable)))))
	(foreach DataTable ListDataTable
		(setq ListAddress (nth 1 (assoc 1 DataTable)))
		(setq FontName (nth 1 (assoc 3 DataTable)))
		(setq FontBold (nth 1 (assoc 4 DataTable)))
		(setq FontItalic (nth 1 (assoc 5 DataTable)))
		(setq HeightText (* (nth 1 (assoc 6 DataTable)) ScaleGlobal))
		(setq Alignment (nth 1 (assoc 7 DataTable)))
		(setq Rotation (nth 1 (assoc 8 DataTable)))

		(setq ListAddress (list (+ NumColStart (nth 0 ListAddress)) (+ NumRowStart (nth 1 ListAddress)) (+ NumColStart (nth 2 ListAddress)) (+ NumRowStart (nth 3 ListAddress))))
		(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
		(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))

		(if (= Rotation 0.0)
			(progn
				(setq ListAlignment
					(list
						(cons "Center" -4108)
						(cons "Left" -4131)
						(cons "Right" -4152)
					)
				)
				(setq Alignment (cdr (assoc Alignment ListAlignment)))
				(vlax-put-property VlaRange "HorizontalAlignment" Alignment)
			)
			(progn
				(setq ListAlignment
					(list
						(cons "Top" -4160)
						(cons "Center" -4108)
						(cons "Bottom" -4107)
					)
				)
				(setq Alignment (cdr (assoc Alignment ListAlignment)))
				(vlax-put-property VlaRange "VerticalAlignment" Alignment)
			)
		)
		(setq VlaFont (vlax-get-property VlaRange "Font"))
		(vlax-put-property VlaFont "Name" FontName)
		(vlax-put-property VlaFont "Bold" FontBold)
		(vlax-put-property VlaFont "Italic" FontItalic)
		(vlax-put-property VlaFont "Size" HeightText)
		(vlax-put-property VlaRange "Orientation" Rotation)
		(vlax-put-property VlaRange "MergeCells" 1)
		(vlax-put-property VlaRange "WrapText" 1)
	)

	(setq NumCol 0)
	(repeat NumColTotal
		(setq ListAddress (list (+ NumColStart NumCol) NumRowStart (+ NumColStart NumCol) (+ NumRowStart NumRowTotal)))
		(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
		(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
		(setq VlaRange (vlax-get-property VlaRange "Columns"))
		(setq ColumnWidth (/ (* (- (nth (+ NumCol 1) ListCoordinateX) (nth NumCol ListCoordinateX)) ScaleGlobal) 11.64))
		(vlax-put-property VlaRange "ColumnWidth" ColumnWidth)
		(setq NumCol (+ NumCol 1))
	)

	(setq NumRow 0)
	(repeat NumRowTotal
		(setq ListAddress (list NumColStart (+ NumRowStart NumRow) (+ NumColStart NumColTotal) (+ NumRowStart NumRow)))
		(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
		(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
		(setq VlaRange (vlax-get-property VlaRange "Rows"))
		(setq RowHeight (/ (* (- (nth NumRow ListCoordinateY) (nth (+ NumRow 1) ListCoordinateY)) ScaleGlobal) 1.91))
		(vlax-put-property VlaRange "RowHeight" RowHeight)
		(setq NumRow (+ NumRow 1))
	)

	(if (not CheckActiveCell)
		(progn
			(vl-catch-all-apply (function (lambda ( / )
				(vl-file-delete FullNameFileXlsx)
				(vlax-invoke-method VlaWorkbook "SaveAs" FullNameFileXlsx 51 nil nil :vlax-false :vlax-false 1 2)
			)))
			(princ (strcat "\nExported data to file \"" FullNameFileXlsx "\"!"))
		)
	)

	(vlax-put-property VlaAppExcel "Visible" :vlax-true)
	(vl-file-delete FullNameFileCsv)
)
-------------------------------------------------------------------------------------------------------------------
(defun C:UPDATEEXCELFROMCAD ( / 
	CheckActiveCell
	GroupListAddress
	GroupListAddressUpdate
	ListCoordinateX
	ListCoordinateY
	ListDataCell
	ListDataLineX
	ListDataLineY
	ListDataTable
	ListTemp
	ListVarSystem_OldValue
	ListVlaLayerLock
	ListVlaObjectDelete
	ListVlaObjectLine
	ListVlaObjectText
	NameLayerCell
	ToleranceValue
	VlaDrawingCurrent
	VlaLayersGroup
	VlaSpace)

	-------------------------------------------------------------------------------------------------------------------
	(defun LIC_REQUESTCODE ( / 
		CheckLicense
		CodeSoftware
		DateCurrent
		DateUsed
		ListDataUser
		ListCharTotal
		ListNumHash
		ListSerialNumTotal
		ListSerialString
		ListStringQuery
		NameSoftware
		NumCharTotal
		NumCodeSoftware
		NumRequestCode
		NumHash
		NumSeedMax
		NumSeed
		NumUsed
		Pos
		LicenseKey
		LicenseKeyExact
		RequestCode
		UserName)

		(setq NameSoftware "Cad Link Excel")
		(defun LIC_LOAD_DIALOG ( NameSoftware /
			End_Main_DCL
			Main_DCL
			LicenseKey)

			(defun LIC_GET_TILE_LICENSENUMBER ( / )
				(setq LicenseKey (vl-string-trim " " (get_tile "Tile_LicenseNumber")))
				(setq ListNumHash (vl-string->list RequestCode))
				(setq NumSeed (rem (apply '+ ListNumHash) NumSeedMax))
				(setq LicenseKeyExact "")
				(foreach NumHash ListNumHash
					(setq LicenseKeyExact (strcat LicenseKeyExact (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
				)
				(if (= LicenseKeyExact LicenseKey)
					(progn
						(vl-registry-write (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "License Key" LicenseKey)
						(set_tile "Tile_ActivateText" "The official version has been activated!")
						(mode_tile "Tile_ActivateText" 1)
						(mode_tile "Tile_LicenseNumber" 1)
					)
					(progn
						(set_tile "Tile_ActivateText" "License number is incorrect!")
						(mode_tile "Tile_ActivateText" 1)
						(mode_tile "Tile_LicenseNumber" 0)
					)
				)
				(setq LicenseKeyExact Nil)
			)

			(LIC_MAKE_FILE_DCL NameSoftware)
			(setq Main_DCL (load_dialog (strcat NameSoftware " License.dcl")))
			(new_dialog (strcat (LIC_REMOVE_SPACE_OF_STRING NameSoftware) "License") Main_DCL)

			(setq LicenseKey "")
			(LIC_SET_TILE_DECORATION 4)
			(set_tile "Tile_RequestCode" RequestCode)

			(action_tile "Tile_LicenseNumber" "(LIC_GET_TILE_LICENSENUMBER)")
			(action_tile "Tile_CopyRequestCode" "(vlax-invoke (vlax-get (vlax-get (vlax-create-object \"HTMLFile\") 'ParentWindow) 'ClipBoardData) 'setData \"Text\" RequestCode)")

			(setq End_Main_DCL (start_dialog))
			(cond
				(
					(or
						(= End_Main_DCL 0)
						(= End_Main_DCL 1)
					)
					(unload_dialog Main_DCL)
				)
			)
			(setq LIC_GET_TILE_LICENSENUMBER Nil)
			(princ)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_MAKE_FILE_DCL ( NameSoftware /
			Num
			DclFile
			DirectoryDes)

			(setq DirectoryDes (strcat (getvar "roamablerootprefix") "Support"))
			(setq DclFile (open (strcat DirectoryDes "\\" NameSoftware " License.dcl") "w"))
			(write-line "///------------------------------------------------------------------------" DclFile)
			(write-line (strcat "///		 " NameSoftware " License.dcl") DclFile)
			(write-line (strcat (LIC_REMOVE_SPACE_OF_STRING NameSoftware) "License:dialog{") DclFile)
			(write-line (strcat "label = \"" NameSoftware " License\";") DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"Tile_ActivateText\";" DclFile)
			(write-line "	alignment = centered;" DclFile)
			(write-line "	width = 60;" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep0\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "		:column{" DclFile)
			(write-line "		width = 60;" DclFile)

			(write-line "			:text{" DclFile)
			(write-line "				label = \"     You are using the trial version of this app.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     If you find it useful, you can pay 5 USD per license.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     Then you will not see this message board every time you use the application.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     <NAME_EMAIL> (+84 933 648 160) .\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     Thank you for using and supporting.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "		}" DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep1\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:row{" DclFile)
			(write-line "		:text{" DclFile)
			(write-line "		label = \"     Request code\";" DclFile)
			(write-line "		width = 15;" DclFile)
			(write-line "		}" DclFile)

			(write-line "		:text{" DclFile)
			(write-line "		key = \"Tile_RequestCode\";" DclFile)
			(write-line "		width = 45;" DclFile)
			(write-line "		}" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep2\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:row{" DclFile)
			(write-line "		:text{" DclFile)
			(write-line "		label = \"     Enter license\";" DclFile)
			(write-line "		width = 15;" DclFile)
			(write-line "		}" DclFile)

			(write-line "		:edit_box{" DclFile)
			(write-line "		key = \"Tile_LicenseNumber\";" DclFile)
			(write-line "		width = 45;" DclFile)
			(write-line "		}" DclFile)
			(write-line "	}" DclFile)  
		
			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep3\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:row{" DclFile)
			(write-line "		:button{" DclFile)
			(write-line "		label = \"&Continue\";" DclFile)
			(write-line "		key = \"Ok\";" DclFile)
			(write-line "		is_default = true;" DclFile)
			(write-line "		is_cancel = true;" DclFile)
			(write-line "		width = 30;" DclFile)
			(write-line "		}" DclFile)

			(write-line "		:button{" DclFile)
			(write-line "		label = \"Copy &request code\";" DclFile)
			(write-line "		key = \"Tile_CopyRequestCode\";" DclFile)
			(write-line "		width = 30;" DclFile)
			(write-line "		}" DclFile)
			(write-line "	}" DclFile)
			(write-line "}" DclFile)
			(close DclFile)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_SET_TILE_DECORATION ( NumTotal / Num Tile)
			(setq Num 0)
			(repeat NumTotal
				(setq Tile (strcat "sep" (itoa Num)))
				(set_tile Tile "--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------")
				(setq Num (+ Num 1))
			)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_REMOVE_SPACE_OF_STRING ( String / )
			(while (/= String (setq StringTemp (vl-string-subst "" " " String)))
				(setq String StringTemp)
			)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_GETSERIALNUMBER ( StringQuery StringNameSerial / 
			SerialNumber
			VlaObjectLocal
			VlaObjectServive
			VlaObjectSet)

			(setq VlaObjectLocal (vlax-create-object "WbemScripting.SWbemLocator"))
			(setq VlaObjectServive (vlax-invoke VlaObjectLocal 'ConnectServer nil nil nil nil nil nil nil nil))
			(setq Server (vlax-invoke VlaObjectLocal 'ConnectServer "." "root\\cimv2"))
			(setq VlaObjectSet
				(vlax-invoke 
					VlaObjectServive
					"ExecQuery"
					StringQuery
				)
			)
			(vlax-for VlaObject VlaObjectSet
				(setq SerialNumber (vlax-get VlaObject StringNameSerial))
			)
			SerialNumber
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_SEND_REQUESTAPI ( StringUrl / 
			CodeStatus
			StringResponse
			VlaHttpRequest)

			(vl-catch-all-apply (function (lambda ( / )
				(setq VlaHttpRequest (vlax-invoke-method (vlax-get-acad-object) 'GetInterfaceObject "WinHttp.WinHttpRequest.5.1"))
				(vlax-invoke-method VlaHttpRequest 'Open "GET" StringUrl :vlax-false)
				(vlax-invoke-method VlaHttpRequest 'Send)
				(setq CodeStatus (vlax-get-property VlaHttpRequest 'Status))
				(if (= CodeStatus 200)
					(setq StringResponse (vlax-get-property VlaHttpRequest 'ResponseText))
				)
			)))
			StringResponse
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_SEND_REQUESTAPI_SENDDATAUSER ( ListData / 
			StringResponse
			StringUrl)

			(vl-catch-all-apply (function (lambda ( / )
				(setq StringUrl "https://script.google.com/macros/s/AKfycbz1n3v780vBayl68zGEGeEc0swT9HgO1Sg5c_bz4xK-tO9oUARczwj59oH4UCPcgify/exec?")
				(foreach Data ListData
					(setq StringUrl (strcat StringUrl (car Data) "=" (cdr Data) "&"))
				)
				(setq StringResponse (LIC_SEND_REQUESTAPI StringUrl))
			)))
			StringResponse
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_GET_CURRENT_DATE ( / )
			(setq StringTemp (rtos (getvar "CDATE") 2 6))
			(strcat (substr StringTemp 1 4) "-" (substr StringTemp 5 2) "-" (substr StringTemp 7 2))
		)
		-------------------------------------------------------------------------------------------------------------------
		(vl-load-com)
		(setq ListStringQuery
			(list
				(cons "Select * from Win32_BaseBoard" "SerialNumber")
				(cons "Select * from Win32_BIOS" "SerialNumber")
			)
		)
		(setq ListSerialString (cons NameSoftware (mapcar '(lambda (x) (LIC_GETSERIALNUMBER (car x) (cdr x))) ListStringQuery)))
		(setq ListSerialString (vl-remove Nil ListSerialString))
		(setq ListSerialNumTotal (mapcar 'vl-string->list ListSerialString))
		(setq LIC_GETSERIALNUMBER Nil)

		(setq ListCharTotal (list "A" "B" "C" "D" "E" "F" "G" "H" "I" "J" "K" "L" "M" "N" "O" "P" "Q" "R" "S" "T" "U" "V" "W" "X" "Y" "Z" "0" "1" "2" "3" "4" "5" "6" "7" "8" "9"))
		(setq NumCharTotal (length ListCharTotal))
		(setq NumSeedMax 100000000)

		(setq ListNumHash (vl-string->list NameSoftware))
		(setq NumSeed (rem (apply '+ ListNumHash) NumSeedMax))
		(setq CodeSoftware "")
		(setq NumCodeSoftware 6)
		(setq Pos 0)
		(while (< (strlen CodeSoftware) NumCodeSoftware)
			(setq NumHash (nth 0 ListNumHash))
			(if (not NumHash)
				(setq NumHash NumSeed)
			)
			(setq CodeSoftware (strcat CodeSoftware (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
		)

		(setq RequestCode CodeSoftware)
		(setq Pos 0)
		(setq NumRequestCode 36)
		(while (< (strlen RequestCode) NumRequestCode)
			(foreach ListSerialNum ListSerialNumTotal
				(setq NumHash Nil)
				(vl-catch-all-apply (function (lambda ( / )
					(setq NumHash (nth Pos ListSerialNum))
				)))
				(if (not NumHash)
					(setq NumHash NumSeed)
				)
				(setq RequestCode (strcat RequestCode (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
			)
			(setq Pos (+ Pos 1))
		)

		(setq LicenseKey (vl-registry-read (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "License Key"))
		(setq ListNumHash (vl-string->list RequestCode))
		(setq NumSeed (rem (apply '+ ListNumHash) NumSeedMax))
		(setq LicenseKeyExact "")
		(foreach NumHash ListNumHash
			(setq LicenseKeyExact (strcat LicenseKeyExact (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
		)
		(if (= LicenseKeyExact LicenseKey)
			(setq CheckLicense T)
			(progn
				(setq CheckLicense Nil)
				(setq LicenseKey "")
			)
		)
		(setq LicenseKeyExact Nil)

		(if (not CheckLicense)
			(LIC_LOAD_DIALOG NameSoftware)
		)

		(setq UserName (getenv "ComputerName"))
		(setq DateUsed (vl-registry-read (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "DateUsed"))
		(setq NumUsed (vl-registry-read (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "NumUsed"))
		(setq DateCurrent (LIC_GET_CURRENT_DATE))
		(if (not NumUsed)
			(setq NumUsed "0")
		)
		(if (not DateUsed)
			(setq DateUsed DateCurrent)
		)
		(setq NumUsed (itoa (+ (atoi NumUsed) 1)))
		(if (/= DateUsed DateCurrent)
			(progn
				(setq ListDataUser
					(list
						(cons "NameSoftware" NameSoftware)
						(cons "UserName" UserName)
						(cons "RequestCode" RequestCode)
						(cons "LicenseKey" LicenseKey)
						(cons "DateUsed" DateUsed)
						(cons "NumUsed" NumUsed)
					)
				)
				(if (LIC_SEND_REQUESTAPI_SENDDATAUSER ListDataUser)
					(progn
						(setq NumUsed "1")
						(setq DateUsed DateCurrent)
					)
				)
			)
		)
		(vl-registry-write (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "DateUsed" DateUsed)
		(vl-registry-write (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "NumUsed" NumUsed)
	)
	-------------------------------------------------------------------------------------------------------------------
	(LIC_REQUESTCODE)
	(setq LIC_REQUESTCODE nil)

	(vl-load-com)
	(setq VlaDrawingCurrent (vla-get-activedocument (vlax-get-acad-object)))
	(vla-startundomark VlaDrawingCurrent)
	(CAEX_SET_VARSYSTEM_CUE)
	(CAEX_CREATE_LISTVLALAYERLOCK)

	(vl-catch-all-apply (function (lambda ( / )
		(setq CheckActiveCell (CAEX_CHECKACTIVECELL))
		(if CheckActiveCell
			(progn
				(setq VlaLayersGroup (vla-get-layers VlaDrawingCurrent))
				(setq ToleranceValue 1e-8)
				(if (= (getvar "CVPORT") 1)
					(setq VlaSpace (vla-get-PaperSpace VlaDrawingCurrent))
					(setq VlaSpace (vla-get-ModelSpace VlaDrawingCurrent))
				)
				(setq SeparatorCSV (vl-registry-read "HKEY_CURRENT_USER\\Control Panel\\International" "sList"))
				(if (not SeparatorCSV)
					(setq SeparatorCSV ",")
				)

				(setq ListTemp (CAEX_SELECT_TABLE_IN_CAD))
				(setq ListVlaObjectLine (nth 0 ListTemp))
				(setq ListVlaObjectText (nth 1 ListTemp))
				(setq ListVlaObjectDelete (nth 2 ListTemp))

				(if ListVlaObjectLine
					(progn
						(setq ListTemp (CAEX_GET_LISTDATALINE_FROM_CAD))
						(setq ListDataLineX (nth 0 ListTemp))
						(setq ListDataLineY (nth 1 ListTemp))
						(setq ListCoordinateX (mapcar 'car ListDataLineX))
						(setq ListCoordinateY (mapcar 'car ListDataLineY))
						(setq GroupListAddress (CAEX_GET_GROUPLISTADDRESS_FROM_CAD))
						(setq ListTemp (CAEX_CREATE_LISTDATACELL))
						(setq ListDataCell (nth 0 ListTemp))
						(setq NameLayerCell (nth 1 ListTemp))
						(setq ListDataTable (CAEX_GET_LISTDATATABLE_FROM_CAD))
						(while T
							(setq GroupListAddressUpdate (CAEX_GET_GROUPLISTADDRESSUPDATE))
							(CAEX_UPDATE_TABLE_FOR_EXCEL)
						)
					)
				)
				
			)
			(princ "\nData from excel could not be found!")
		)
	)))

	(mapcar 'vla-delete (append ListVlaObjectDelete (mapcar 'car ListDataCell)))
	(if NameLayerCell
		(vla-delete (vla-item VlaLayersGroup NameLayerCell))
	)

	(CAEX_RESTORE_LOCK_LAYER)
	(CAEX_RESET_VARSYSTEM)
	(vla-endundomark VlaDrawingCurrent)
	(princ)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CREATE_LISTDATACELL ( / 
	DataPolyline
	ListBulge
	ListPoint
	ListDataCell
	NameLayerCell
	Point1
	Point2
	Point3
	Point4
	PointMinX
	PointMinY 
	PointMaxX 
	PointMaxY
	VlaPolylineCell)

	(setq NameLayerCell (CAEX_RANDOM_STRING 8))
	(while (not (vl-catch-all-error-p (vl-catch-all-apply 'vla-item (list VlaLayersGroup NameLayerCell))))
		(setq NameLayerCell (CAEX_RANDOM_STRING 8))
	)
	(vla-add VlaLayersGroup NameLayerCell)

	(foreach ListAddress GroupListAddress
		(setq PointMinX (nth (nth 0 ListAddress) ListCoordinateX))
		(setq PointMinY (nth (+ (nth 3 ListAddress) 1) ListCoordinateY))
		(setq PointMaxX (nth (+ (nth 2 ListAddress) 1) ListCoordinateX))
		(setq PointMaxY (nth (nth 1 ListAddress) ListCoordinateY))
		(setq Point1 (list PointMinX PointMinY 0.0))
		(setq Point2 (list PointMaxX PointMinY 0.0))
		(setq Point3 (list PointMaxX PointMaxY 0.0))
		(setq Point4 (list PointMinX PointMaxY 0.0))
		(setq ListPoint (list Point1 Point2 Point3 Point4))
		(setq ListBulge (mapcar '(lambda (x) 0.0) ListPoint))
		(setq DataPolyline (list ListPoint ListBulge))
		(setq VlaPolylineCell (CAEX_CREATE_POLYLINE DataPolyline T))
		(vla-put-layer VlaPolylineCell NameLayerCell)
		(vla-put-color VlaPolylineCell 256)
		(vla-put-Linetype VlaPolylineCell "ByLayer")
		(setq ListDataCell (cons (list VlaPolylineCell ListAddress) ListDataCell))
	)
	(list ListDataCell NameLayerCell)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_GROUPLISTADDRESSUPDATE ( / 
	GroupListAddressUpdate
	ListAddress
	ListVlaPolylineCell
	SelectionFilter
	SelectionSet)

	(setq SelectionFilter (list (cons 8 NameLayerCell)))
	(setq SelectionSet (ssget SelectionFilter))
	(setq ListVlaPolylineCell (CAEX_CONVERT_SELECTIONSET_TO_LISTVLAOBJECT SelectionSet))
	(foreach VlaPolylineCell ListVlaPolylineCell
		(vla-Highlight VlaPolylineCell 1)
		(setq ListAddress (nth 1 (assoc VlaPolylineCell ListDataCell)))
		(setq GroupListAddressUpdate (cons ListAddress GroupListAddressUpdate))
	)
	GroupListAddressUpdate
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_UPDATE_TABLE_FOR_EXCEL ( / 
	Alignment
	DataTable
	FontBold
	ListAddress
	ListAlignment
	FontName
	FontItalic
	NumColStart
	NumRowStart
	Rotation
	StringAddress
	StringContent
	VlaAppExcel
	VlaBorder
	VlaBorders
	VlaFont
	VlaRange
	VlaRangeActive
	VlaSheet)

	(setq VlaAppExcel (vlax-get-or-create-Object "Excel.Application"))
	(vlax-put-property VlaAppExcel "Visible" :vlax-true)
	(setq VlaSheet (vlax-get-property VlaAppExcel "ActiveSheet"))
	(setq VlaRangeActive (vlax-get-property VlaAppExcel "Selection"))
	(setq StringAddress (vlax-get-property VlaRangeActive "Address" :vlax-false :vlax-false 1 :vlax-false :vlax-false))
	(setq ListAddress (CAEX_STRINGADDRESS_TO_LISTADDRESS StringAddress))
	(setq NumColStart (nth 0 ListAddress))
	(setq NumRowStart (nth 1 ListAddress))

	(foreach ListAddressCad GroupListAddressUpdate
		(setq ListAddress (list (nth 0 ListAddressCad) (nth 1 ListAddressCad)))
		(setq DataTable (assoc (list 0 ListAddress) ListDataTable))
		(setq StringContent (nth 1 (assoc 2 DataTable)))
		(setq FontName (nth 1 (assoc 3 DataTable)))
		(setq FontBold (nth 1 (assoc 4 DataTable)))
		(setq FontItalic (nth 1 (assoc 5 DataTable)))
		(setq Alignment (nth 1 (assoc 7 DataTable)))
		(setq Rotation (nth 1 (assoc 8 DataTable)))

		(setq ListAddress (list (+ NumColStart (nth 0 ListAddressCad)) (+ NumRowStart (nth 1 ListAddressCad)) (+ NumColStart (nth 2 ListAddressCad)) (+ NumRowStart (nth 3 ListAddressCad))))
		(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
		(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
		(vlax-put-property VlaRange "MergeCells" 0)
		(vlax-put VlaRange "Value" "")
		(setq VlaBorders (vlax-get-property VlaRange "Borders"))
		(foreach BordersIndex (list 7 8 9 10 11 12)
			(setq VlaBorder (vlax-get-property VlaBorders "Item" BordersIndex))
			(vlax-put-property VlaBorder "LineStyle" 1)
			(vlax-put-property VlaBorder "Weight" 2)
		)

		(setq ListAddress (list (+ NumColStart (nth 0 ListAddressCad)) (+ NumRowStart (nth 1 ListAddressCad))))
		(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
		(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
		(vlax-put VlaRange "Formula" StringContent)
		(vlax-invoke-method VlaRange "Copy")
		(vlax-invoke-method VlaRange "PasteSpecial" -4163 -4142 :vlax-false :vlax-false)
		(vlax-put-property VlaAppExcel "CutCopyMode" :vlax-false)

		(setq ListAddress (list (+ NumColStart (nth 0 ListAddressCad)) (+ NumRowStart (nth 1 ListAddressCad)) (+ NumColStart (nth 2 ListAddressCad)) (+ NumRowStart (nth 3 ListAddressCad))))
		(setq StringAddress (CAEX_LISTADDRESS_TO_STRINGADDRESS ListAddress))
		(setq VlaRange (vlax-get-property VlaSheet "Range" StringAddress))
		(if (= Rotation 0.0)
			(progn
				(setq ListAlignment
					(list
						(cons "Center" -4108)
						(cons "Left" -4131)
						(cons "Right" -4152)
					)
				)
				(setq Alignment (cdr (assoc Alignment ListAlignment)))
				(vlax-put-property VlaRange "HorizontalAlignment" Alignment)
			)
			(progn
				(setq ListAlignment
					(list
						(cons "Top" -4160)
						(cons "Center" -4108)
						(cons "Bottom" -4107)
					)
				)
				(setq Alignment (cdr (assoc Alignment ListAlignment)))
				(vlax-put-property VlaRange "VerticalAlignment" Alignment)
			)
		)
		(setq VlaFont (vlax-get-property VlaRange "Font"))
		(vlax-put-property VlaFont "Name" FontName)
		(vlax-put-property VlaFont "Bold" FontBold)
		(vlax-put-property VlaFont "Italic" FontItalic)
		(vlax-put-property VlaRange "Orientation" Rotation)
		(vlax-put-property VlaRange "MergeCells" 1)
		(vlax-put-property VlaRange "WrapText" 1)
	)
	(vlax-invoke-method VlaRangeActive "Select")
)
-------------------------------------------------------------------------------------------------------------------
(defun C:UPDATECADFROMEXCEL ( / 
	CheckActiveCell
	GroupListAddress
	GroupListAddressUpdate
	ListCoordinateX
	ListCoordinateY
	ListDataCell
	ListDataLineX
	ListDataLineY
	ListDataTable
	ListTemp
	ListVarSystem_OldValue
	ListVlaLayerLock
	ListVlaObjectLine
	ListVlaObjectDelete
	ListVlaObjectText
	NameLayerCell
	ToleranceValue
	VlaDrawingCurrent
	VlaLayersGroup
	VlaSpace)
	-------------------------------------------------------------------------------------------------------------------
	(defun LIC_REQUESTCODE ( / 
		CheckLicense
		CodeSoftware
		DateCurrent
		DateUsed
		ListDataUser
		ListCharTotal
		ListNumHash
		ListSerialNumTotal
		ListSerialString
		ListStringQuery
		NameSoftware
		NumCharTotal
		NumCodeSoftware
		NumRequestCode
		NumHash
		NumSeedMax
		NumSeed
		NumUsed
		Pos
		LicenseKey
		LicenseKeyExact
		RequestCode
		UserName)

		(setq NameSoftware "Cad Link Excel")
		(defun LIC_LOAD_DIALOG ( NameSoftware /
			End_Main_DCL
			Main_DCL
			LicenseKey)

			(defun LIC_GET_TILE_LICENSENUMBER ( / )
				(setq LicenseKey (vl-string-trim " " (get_tile "Tile_LicenseNumber")))
				(setq ListNumHash (vl-string->list RequestCode))
				(setq NumSeed (rem (apply '+ ListNumHash) NumSeedMax))
				(setq LicenseKeyExact "")
				(foreach NumHash ListNumHash
					(setq LicenseKeyExact (strcat LicenseKeyExact (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
				)
				(if (= LicenseKeyExact LicenseKey)
					(progn
						(vl-registry-write (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "License Key" LicenseKey)
						(set_tile "Tile_ActivateText" "The official version has been activated!")
						(mode_tile "Tile_ActivateText" 1)
						(mode_tile "Tile_LicenseNumber" 1)
					)
					(progn
						(set_tile "Tile_ActivateText" "License number is incorrect!")
						(mode_tile "Tile_ActivateText" 1)
						(mode_tile "Tile_LicenseNumber" 0)
					)
				)
				(setq LicenseKeyExact Nil)
			)

			(LIC_MAKE_FILE_DCL NameSoftware)
			(setq Main_DCL (load_dialog (strcat NameSoftware " License.dcl")))
			(new_dialog (strcat (LIC_REMOVE_SPACE_OF_STRING NameSoftware) "License") Main_DCL)

			(setq LicenseKey "")
			(LIC_SET_TILE_DECORATION 4)
			(set_tile "Tile_RequestCode" RequestCode)

			(action_tile "Tile_LicenseNumber" "(LIC_GET_TILE_LICENSENUMBER)")
			(action_tile "Tile_CopyRequestCode" "(vlax-invoke (vlax-get (vlax-get (vlax-create-object \"HTMLFile\") 'ParentWindow) 'ClipBoardData) 'setData \"Text\" RequestCode)")

			(setq End_Main_DCL (start_dialog))
			(cond
				(
					(or
						(= End_Main_DCL 0)
						(= End_Main_DCL 1)
					)
					(unload_dialog Main_DCL)
				)
			)
			(setq LIC_GET_TILE_LICENSENUMBER Nil)
			(princ)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_MAKE_FILE_DCL ( NameSoftware /
			Num
			DclFile
			DirectoryDes)

			(setq DirectoryDes (strcat (getvar "roamablerootprefix") "Support"))
			(setq DclFile (open (strcat DirectoryDes "\\" NameSoftware " License.dcl") "w"))
			(write-line "///------------------------------------------------------------------------" DclFile)
			(write-line (strcat "///		 " NameSoftware " License.dcl") DclFile)
			(write-line (strcat (LIC_REMOVE_SPACE_OF_STRING NameSoftware) "License:dialog{") DclFile)
			(write-line (strcat "label = \"" NameSoftware " License\";") DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"Tile_ActivateText\";" DclFile)
			(write-line "	alignment = centered;" DclFile)
			(write-line "	width = 60;" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep0\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "		:column{" DclFile)
			(write-line "		width = 60;" DclFile)

			(write-line "			:text{" DclFile)
			(write-line "				label = \"     You are using the trial version of this app.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     If you find it useful, you can pay 5 USD per license.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     Then you will not see this message board every time you use the application.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     <NAME_EMAIL> (+84 933 648 160) .\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "			:text{" DclFile)
			(write-line "				label = \"     Thank you for using and supporting.\";" DclFile)
			(write-line "			}" DclFile)
			(write-line "		}" DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep1\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:row{" DclFile)
			(write-line "		:text{" DclFile)
			(write-line "		label = \"     Request code\";" DclFile)
			(write-line "		width = 15;" DclFile)
			(write-line "		}" DclFile)

			(write-line "		:text{" DclFile)
			(write-line "		key = \"Tile_RequestCode\";" DclFile)
			(write-line "		width = 45;" DclFile)
			(write-line "		}" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep2\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:row{" DclFile)
			(write-line "		:text{" DclFile)
			(write-line "		label = \"     Enter license\";" DclFile)
			(write-line "		width = 15;" DclFile)
			(write-line "		}" DclFile)

			(write-line "		:edit_box{" DclFile)
			(write-line "		key = \"Tile_LicenseNumber\";" DclFile)
			(write-line "		width = 45;" DclFile)
			(write-line "		}" DclFile)
			(write-line "	}" DclFile)  
		
			(write-line "	:text{" DclFile)
			(write-line "	key = \"sep3\";" DclFile)
			(write-line "	}" DclFile)

			(write-line "	:row{" DclFile)
			(write-line "		:button{" DclFile)
			(write-line "		label = \"&Continue\";" DclFile)
			(write-line "		key = \"Ok\";" DclFile)
			(write-line "		is_default = true;" DclFile)
			(write-line "		is_cancel = true;" DclFile)
			(write-line "		width = 30;" DclFile)
			(write-line "		}" DclFile)

			(write-line "		:button{" DclFile)
			(write-line "		label = \"Copy &request code\";" DclFile)
			(write-line "		key = \"Tile_CopyRequestCode\";" DclFile)
			(write-line "		width = 30;" DclFile)
			(write-line "		}" DclFile)
			(write-line "	}" DclFile)
			(write-line "}" DclFile)
			(close DclFile)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_SET_TILE_DECORATION ( NumTotal / Num Tile)
			(setq Num 0)
			(repeat NumTotal
				(setq Tile (strcat "sep" (itoa Num)))
				(set_tile Tile "--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------")
				(setq Num (+ Num 1))
			)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_REMOVE_SPACE_OF_STRING ( String / )
			(while (/= String (setq StringTemp (vl-string-subst "" " " String)))
				(setq String StringTemp)
			)
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_GETSERIALNUMBER ( StringQuery StringNameSerial / 
			SerialNumber
			VlaObjectLocal
			VlaObjectServive
			VlaObjectSet)

			(setq VlaObjectLocal (vlax-create-object "WbemScripting.SWbemLocator"))
			(setq VlaObjectServive (vlax-invoke VlaObjectLocal 'ConnectServer nil nil nil nil nil nil nil nil))
			(setq Server (vlax-invoke VlaObjectLocal 'ConnectServer "." "root\\cimv2"))
			(setq VlaObjectSet
				(vlax-invoke 
					VlaObjectServive
					"ExecQuery"
					StringQuery
				)
			)
			(vlax-for VlaObject VlaObjectSet
				(setq SerialNumber (vlax-get VlaObject StringNameSerial))
			)
			SerialNumber
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_SEND_REQUESTAPI ( StringUrl / 
			CodeStatus
			StringResponse
			VlaHttpRequest)

			(vl-catch-all-apply (function (lambda ( / )
				(setq VlaHttpRequest (vlax-invoke-method (vlax-get-acad-object) 'GetInterfaceObject "WinHttp.WinHttpRequest.5.1"))
				(vlax-invoke-method VlaHttpRequest 'Open "GET" StringUrl :vlax-false)
				(vlax-invoke-method VlaHttpRequest 'Send)
				(setq CodeStatus (vlax-get-property VlaHttpRequest 'Status))
				(if (= CodeStatus 200)
					(setq StringResponse (vlax-get-property VlaHttpRequest 'ResponseText))
				)
			)))
			StringResponse
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_SEND_REQUESTAPI_SENDDATAUSER ( ListData / 
			StringResponse
			StringUrl)

			(vl-catch-all-apply (function (lambda ( / )
				(setq StringUrl "https://script.google.com/macros/s/AKfycbz1n3v780vBayl68zGEGeEc0swT9HgO1Sg5c_bz4xK-tO9oUARczwj59oH4UCPcgify/exec?")
				(foreach Data ListData
					(setq StringUrl (strcat StringUrl (car Data) "=" (cdr Data) "&"))
				)
				(setq StringResponse (LIC_SEND_REQUESTAPI StringUrl))
			)))
			StringResponse
		)
		-------------------------------------------------------------------------------------------------------------------
		(defun LIC_GET_CURRENT_DATE ( / )
			(setq StringTemp (rtos (getvar "CDATE") 2 6))
			(strcat (substr StringTemp 1 4) "-" (substr StringTemp 5 2) "-" (substr StringTemp 7 2))
		)
		-------------------------------------------------------------------------------------------------------------------
		(vl-load-com)
		(setq ListStringQuery
			(list
				(cons "Select * from Win32_BaseBoard" "SerialNumber")
				(cons "Select * from Win32_BIOS" "SerialNumber")
			)
		)
		(setq ListSerialString (cons NameSoftware (mapcar '(lambda (x) (LIC_GETSERIALNUMBER (car x) (cdr x))) ListStringQuery)))
		(setq ListSerialString (vl-remove Nil ListSerialString))
		(setq ListSerialNumTotal (mapcar 'vl-string->list ListSerialString))
		(setq LIC_GETSERIALNUMBER Nil)

		(setq ListCharTotal (list "A" "B" "C" "D" "E" "F" "G" "H" "I" "J" "K" "L" "M" "N" "O" "P" "Q" "R" "S" "T" "U" "V" "W" "X" "Y" "Z" "0" "1" "2" "3" "4" "5" "6" "7" "8" "9"))
		(setq NumCharTotal (length ListCharTotal))
		(setq NumSeedMax 100000000)

		(setq ListNumHash (vl-string->list NameSoftware))
		(setq NumSeed (rem (apply '+ ListNumHash) NumSeedMax))
		(setq CodeSoftware "")
		(setq NumCodeSoftware 6)
		(setq Pos 0)
		(while (< (strlen CodeSoftware) NumCodeSoftware)
			(setq NumHash (nth 0 ListNumHash))
			(if (not NumHash)
				(setq NumHash NumSeed)
			)
			(setq CodeSoftware (strcat CodeSoftware (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
		)

		(setq RequestCode CodeSoftware)
		(setq Pos 0)
		(setq NumRequestCode 36)
		(while (< (strlen RequestCode) NumRequestCode)
			(foreach ListSerialNum ListSerialNumTotal
				(setq NumHash Nil)
				(vl-catch-all-apply (function (lambda ( / )
					(setq NumHash (nth Pos ListSerialNum))
				)))
				(if (not NumHash)
					(setq NumHash NumSeed)
				)
				(setq RequestCode (strcat RequestCode (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
			)
			(setq Pos (+ Pos 1))
		)

		(setq LicenseKey (vl-registry-read (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "License Key"))
		(setq ListNumHash (vl-string->list RequestCode))
		(setq NumSeed (rem (apply '+ ListNumHash) NumSeedMax))
		(setq LicenseKeyExact "")
		(foreach NumHash ListNumHash
			(setq LicenseKeyExact (strcat LicenseKeyExact (nth (rem (setq NumSeed (rem (abs (fix (+ NumHash (* (sin NumSeed) NumSeedMax)))) NumSeedMax)) NumCharTotal) ListCharTotal)))
		)
		(if (= LicenseKeyExact LicenseKey)
			(setq CheckLicense T)
			(progn
				(setq CheckLicense Nil)
				(setq LicenseKey "")
			)
		)
		(setq LicenseKeyExact Nil)

		(if (not CheckLicense)
			(LIC_LOAD_DIALOG NameSoftware)
		)

		(setq UserName (getenv "ComputerName"))
		(setq DateUsed (vl-registry-read (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "DateUsed"))
		(setq NumUsed (vl-registry-read (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "NumUsed"))
		(setq DateCurrent (LIC_GET_CURRENT_DATE))
		(if (not NumUsed)
			(setq NumUsed "0")
		)
		(if (not DateUsed)
			(setq DateUsed DateCurrent)
		)
		(setq NumUsed (itoa (+ (atoi NumUsed) 1)))
		(if (/= DateUsed DateCurrent)
			(progn
				(setq ListDataUser
					(list
						(cons "NameSoftware" NameSoftware)
						(cons "UserName" UserName)
						(cons "RequestCode" RequestCode)
						(cons "LicenseKey" LicenseKey)
						(cons "DateUsed" DateUsed)
						(cons "NumUsed" NumUsed)
					)
				)
				(if (LIC_SEND_REQUESTAPI_SENDDATAUSER ListDataUser)
					(progn
						(setq NumUsed "1")
						(setq DateUsed DateCurrent)
					)
				)
			)
		)
		(vl-registry-write (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "DateUsed" DateUsed)
		(vl-registry-write (strcat "HKEY_CURRENT_USER\\Software\\Cad Standard\\" NameSoftware) "NumUsed" NumUsed)
	)
	-------------------------------------------------------------------------------------------------------------------
	(LIC_REQUESTCODE)
	(setq LIC_REQUESTCODE nil)

	(vl-load-com)
	(setq VlaDrawingCurrent (vla-get-activedocument (vlax-get-acad-object)))
	(vla-startundomark VlaDrawingCurrent)
	(CAEX_SET_VARSYSTEM_EUC)
	(CAEX_CREATE_LISTVLALAYERLOCK)

	(vl-catch-all-apply (function (lambda ( / )
		(setq CheckActiveCell (CAEX_CHECKACTIVECELL))
		(if CheckActiveCell
			(progn
				(setq VlaLayersGroup (vla-get-layers VlaDrawingCurrent))
				(setq ToleranceValue 1e-8)
				(if (= (getvar "CVPORT") 1)
					(setq VlaSpace (vla-get-PaperSpace VlaDrawingCurrent))
					(setq VlaSpace (vla-get-ModelSpace VlaDrawingCurrent))
				)

				(setq ListTemp (CAEX_SELECT_TABLE_IN_CAD))
				(setq ListVlaObjectLine (nth 0 ListTemp))
				(setq ListVlaObjectText (nth 1 ListTemp))
				(setq ListVlaObjectDelete (nth 2 ListTemp))

				(if ListVlaObjectLine
					(progn
						(setq ListTemp (CAEX_GET_LISTDATALINE_FROM_CAD))
						(setq ListDataLineX (nth 0 ListTemp))
						(setq ListDataLineY (nth 1 ListTemp))
						(setq ListCoordinateX (mapcar 'car ListDataLineX))
						(setq ListCoordinateY (mapcar 'car ListDataLineY))
						(setq NumColTotal (- (length ListCoordinateX) 1))
						(setq NumRowTotal (- (length ListCoordinateY) 1))
						(setq GroupListAddress (CAEX_GET_GROUPLISTADDRESS_FROM_CAD))
						(setq ListTemp (CAEX_CREATE_LISTDATACELL))
						(setq ListDataCell (nth 0 ListTemp))
						(setq NameLayerCell (nth 1 ListTemp))
						(setq HeightTextGlobal (CAEX_GET_HEIGHTTEXTGLOBAL ListVlaObjectText))
						(setq ListPropertyObjectGlobal (CAEX_GET_LISTPROPERTYOBJECT_MULTIOBJECT ListVlaObjectText))

						(setq ListTemp (CAEX_GET_LISTDATATABLE_FROM_EXCEL NumColTotal NumRowTotal))
						(setq ListDataTable (nth 0 ListTemp))
						(while T
							(setq GroupListAddressUpdate (CAEX_GET_GROUPLISTADDRESSUPDATE))
							(CAEX_UPDATE_TABLE_FOR_CAD)
						)
					)
				)
				
			)
			(princ "\nData from excel could not be found!")
		)
	)))

	(mapcar 'vla-delete (append ListVlaObjectDelete (mapcar 'car ListDataCell)))
	(if NameLayerCell
		(vla-delete (vla-item VlaLayersGroup NameLayerCell))
	)

	(CAEX_RESTORE_LOCK_LAYER)
	(CAEX_RESET_VARSYSTEM)
	(vla-endundomark VlaDrawingCurrent)
	(princ)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_HEIGHTTEXTGLOBAL ( ListVlaObjectText /
	HeightText
	HeightTextGlobal
	ListHeightText
	Num)

	(setq HeightTextGlobal (CAEX_FIND_VALUE_POPULAR (mapcar 'vla-get-Height ListVlaObjectText)))
	(if (not HeightTextGlobal)
		(progn
			(setq Num 0)
			(repeat (- (length ListCoordinateY) 1)
				(setq HeightText (/ (- (nth Num ListCoordinateY) (nth (+ Num 1) ListCoordinateY)) 2.0))
				(setq ListHeightText (cons HeightText ListHeightText))
				(setq Num (+ Num 1))
			)
			(setq HeightTextGlobal (CAEX_FIND_VALUE_POPULAR ListHeightText))
		)
	)
	HeightTextGlobal
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTPROPERTYOBJECT_MULTIOBJECT ( ListVlaObjectText / 
	GroupListPropertyObject
	ListPropertyObjectGlobal
	ListNameProperty)

	(setq GroupListPropertyObject (mapcar 'CAEX_GET_PROPERTIES_OBJECT ListVlaObjectText))
	(setq ListNameProperty (mapcar 'car (car GroupListPropertyObject)))
	(setq ListPropertyObjectGlobal
		(mapcar
			'(lambda (NameProperty)
				(CAEX_FIND_VALUE_POPULAR
					(mapcar '(lambda (ListPropertyObject) (assoc NameProperty ListPropertyObject)) GroupListPropertyObject)
				)
			)
			ListNameProperty
		)
	)
	ListPropertyObjectGlobal
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_UPDATE_TABLE_FOR_CAD ( /
	Alignment
	CodeAlignment
	DataTable
	DeltaX
	DeltaY
	FontBold
	FontItalic
	FontName
	GapText
	HeightText
	LengthMaxX
	LengthMaxY
	LengthTextX
	LengthTextY
	ListDataBoundary
	ListPropertyObject
	ListStringContent
	ListVlaObjectTextSelect
	ListVlaObjectTextNew
	NameTextStyle
	NumText
	PointMaxX
	PointMaxY
	PointMinX
	PointMinY
	PointText
	PointTextBase
	PointTextTemp
	PointTextX
	PointTextY
	Rotation
	SelectionFilter
	ValueScaleText
	VlaObjectText)

	(setq SelectionFilter
		(list
			(cons -4 "<OR")
			(cons 0 "TEXT")
			(cons 0 "MTEXT")
			(cons -4 "OR>")
		)
	)

	(foreach ListAddress GroupListAddressUpdate
		(setq DataTable (assoc (list 1 ListAddress) ListDataTable))
		(if DataTable
			(progn
				(setq ListStringContent (nth 1 (assoc 2 DataTable)))
				(setq FontName (nth 1 (assoc 3 DataTable)))
				(setq FontBold (nth 1 (assoc 4 DataTable)))
				(setq FontItalic (nth 1 (assoc 5 DataTable)))
				(setq Alignment (nth 1 (assoc 7 DataTable)))
				(setq Rotation (nth 1 (assoc 8 DataTable)))
				(setq ListVlaObjectTextSelect (CAEX_GET_LISTVLATEXT_IN_CELL ListAddress))
				(if ListStringContent
					(progn
						(setq HeightText (CAEX_FIND_VALUE_POPULAR (mapcar 'vla-get-Height ListVlaObjectTextSelect)))
						(if (not HeightText)
							(setq HeightText HeightTextGlobal)
						)
						(setq ListPropertyObject (CAEX_GET_LISTPROPERTYOBJECT_MULTIOBJECT ListVlaObjectTextSelect))
						(if (not ListPropertyObject)
							(setq ListPropertyObject ListPropertyObjectGlobal)
						)

						(setq PointMinX (nth (nth 0 ListAddress) ListCoordinateX))
						(setq PointMaxY (nth (nth 1 ListAddress) ListCoordinateY))
						(setq PointMaxX (nth (+ (nth 2 ListAddress) 1) ListCoordinateX))
						(setq PointMinY (nth (+ (nth 3 ListAddress) 1) ListCoordinateY))

						(setq NameTextStyle (CAEX_FIND_NAMETEXTSTYLE FontName FontBold FontItalic))
						(setq ListVlaObjectTextNew Nil)
						(setq NumText (length ListStringContent))

						(if (= Rotation 0.0)
							(progn
								(setq GapText (/ (- PointMaxY PointMinY (* NumText HeightText)) (+ NumText 1)))
								(cond
									((= Alignment "Left")
										(setq PointTextX (+ PointMinX (* HeightText 0.5)))
										(setq CodeAlignment acAlignmentBottomLeft)
									)
									((= Alignment "Center")
										(setq PointTextX (+ PointMinX (* (- PointMaxX PointMinX) 0.5)))
										(setq CodeAlignment acAlignmentBottomCenter)
									)
									((= Alignment "Right")
										(setq PointTextX (- PointMaxX (* HeightText 0.5)))
										(setq CodeAlignment acAlignmentBottomRight)
									)
								)
								(setq PointTextY (- PointMaxY GapText (* HeightText 1.0)))
								(setq PointText (list PointTextX PointTextY 0.0))
								(foreach StringContent ListStringContent
									(setq VlaObjectText (vla-AddText VlaSpace StringContent (vlax-3d-point PointText) HeightText))
									(setq ListVlaObjectTextNew (cons VlaObjectText ListVlaObjectTextNew))
									(setq ListVlaObjectOfTable (cons VlaObjectText ListVlaObjectOfTable))
									(vla-put-alignment VlaObjectText CodeAlignment)
									(setq PointTextTemp
										(list
											(nth 0 PointText)
											(- (nth 1 PointText) (nth 1 (vlax-safearray->list (vlax-variant-value (vla-get-InsertionPoint VlaObjectText)))))
											(nth 2 PointText)
										)
									)
									(vla-put-TextAlignmentPoint VlaObjectText (vlax-3d-point PointTextTemp))
									(vla-put-StyleName VlaObjectText NameTextStyle)
									(setq PointTextY (- PointTextY GapText HeightText))
									(setq PointText (list PointTextX PointTextY 0.0))
								)
							)
							(progn
								(setq GapText (/ (- PointMaxX PointMinX (* NumText HeightText)) (+ NumText 1)))
								(cond
									((= Alignment "Bottom")
										(progn
											(setq PointTextY (+ PointMinY (* HeightText 0.5)))
											(if (= Rotation (* Pi 0.5))
												(setq CodeAlignment acAlignmentBottomLeft)
												(setq CodeAlignment acAlignmentBottomRight)
											)
										)
									)
									((= Alignment "Center")
										(setq PointTextY (+ PointMinY (* (- PointMaxY PointMinY) 0.5)))
										(setq CodeAlignment acAlignmentBottomCenter)
									)
									((= Alignment "Top")
										(progn
											(setq PointTextY (- PointMaxY (* HeightText 0.5)))
											(if (= Rotation (* Pi 0.5))
												(setq CodeAlignment acAlignmentBottomRight)
												(setq CodeAlignment acAlignmentBottomLeft)
											)
										)
									)
								)
								(if (= Rotation (* Pi 0.5))
									(progn
										(setq PointTextX (+ PointMinX GapText (* HeightText 1.0)))
									)
									(progn
										(setq ListStringContent (reverse ListStringContent))
										(setq PointTextX (+ PointMinX GapText (* HeightText 0.0)))
									)
								)
								(setq PointText (list PointTextX PointTextY 0.0))
								(foreach StringContent ListStringContent
									(setq VlaObjectText (vla-AddText VlaSpace StringContent (vlax-3d-point PointText) HeightText))
									(setq ListVlaObjectTextNew (cons VlaObjectText ListVlaObjectTextNew))
									(setq ListVlaObjectOfTable (cons VlaObjectText ListVlaObjectOfTable))
									(vla-put-Rotation VlaObjectText Rotation)
									(vla-put-alignment VlaObjectText CodeAlignment)
									(setq PointTextTemp
										(list
											(- (nth 0 PointText) (nth 0 (vlax-safearray->list (vlax-variant-value (vla-get-InsertionPoint VlaObjectText)))))
											(nth 1 PointText)
											(nth 2 PointText)
										)
									)
									(vla-put-TextAlignmentPoint VlaObjectText (vlax-3d-point PointTextTemp))
									(vla-put-StyleName VlaObjectText NameTextStyle)
									(setq PointTextX (+ PointTextX GapText HeightText))
									(setq PointText (list PointTextX PointTextY 0.0))
								)
							)
						)

						(setq ListDataBoundary (mapcar 'CAEX_GET_POINTMIN_POINTMAX_TEXT_MTEXT ListVlaObjectTextNew))
						(setq ListDataBoundary (vl-remove ListDataBoundary Nil))
						(if ListDataBoundary
							(progn
								(setq LengthTextX
									(-
										(apply 'max (mapcar '(lambda (x) (nth 0 (nth 1 x))) ListDataBoundary))
										(apply 'min (mapcar '(lambda (x) (nth 0 (nth 0 x))) ListDataBoundary))
									)
								)
								(setq LengthTextY
									(-
										(apply 'max (mapcar '(lambda (x) (nth 1 (nth 1 x))) ListDataBoundary))
										(apply 'min (mapcar '(lambda (x) (nth 1 (nth 0 x))) ListDataBoundary))
									)
								)
							)
							(progn
								(setq LengthTextX 0.0)
								(setq LengthTextY 0.0)
							)
						)
						(setq LengthMaxX (- PointMaxX PointMinX (* HeightText 0.1)))
						(setq DeltaX (- LengthMaxX LengthTextX))
						(setq LengthMaxY (- PointMaxY PointMinY (* HeightText 0.1)))
						(setq DeltaY (- LengthMaxY LengthTextY))
						(if
							(or
								(< DeltaX 0.0)
								(< DeltaY 0.0)
							)
							(progn
								(if (< DeltaX DeltaY)
									(progn
										(setq LengthMax LengthMaxX)
										(setq LengthText LengthTextX)
									)
									(progn
										(setq LengthMax LengthMaxY)
										(setq LengthText LengthTextY)
									)
								)
								(setq ValueScaleText (/ LengthMax LengthText))

								(if (= Rotation 0.0)
									(progn
										(cond
											((= Alignment "Left")
												(setq PointTextBase
													(list
														PointMinX
														(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
														0.0
													)
												)
											)
											((= Alignment "Center")
												(setq PointTextBase
													(list
														(+ PointMinX (* (- PointMaxX PointMinX) 0.5))
														(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
														0.0
													)
												)
											)
											((= Alignment "Right")
												(setq PointTextBase
													(list
														PointMaxX
														(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
														0.0
													)
												)
											)
										)
									)
									(progn
										(cond
											((= Alignment "Left")
												(setq PointTextBase
													(list
														PointMinX
														(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
														0.0
													)
												)
											)
											((= Alignment "Center")
												(setq PointTextBase
													(list
														(+ PointMinX (* (- PointMaxX PointMinX) 0.5))
														(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
														0.0
													)
												)
											)
											((= Alignment "Right")
												(setq PointTextBase
													(list
														PointMaxX
														(+ PointMinY (* (- PointMaxY PointMinY) 0.5))
														0.0
													)
												)
											)
										)
									)
								)

								(foreach VlaObjectText ListVlaObjectTextNew
									(vla-ScaleEntity VlaObjectText (vlax-3d-point PointTextBase) ValueScaleText)
								)
							)
						)

						(foreach VlaObjectText ListVlaObjectTextNew
							(CAEX_PUT_PROPERTIES_OBJECT VlaObjectText ListPropertyObject)
						)
					)
				)
				(foreach VlaObjectText ListVlaObjectTextSelect
					(setq ListVlaObjectText (vl-remove VlaObjectText ListVlaObjectText))
				)
				(setq ListVlaObjectText (append ListVlaObjectText ListVlaObjectTextNew))
				(mapcar 'vla-delete ListVlaObjectTextSelect)
			)
			(princ "\nCan't find matching value on excel!")
		)
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_LISTADDRESS_TO_STRINGADDRESS ( ListAddress /
	NumLength
	StringAddress)

	(setq NumLength (length ListAddress))
	(if (= NumLength 2)
		(setq StringAddress
			(strcat
				(CAEX_NUMCOLUMN_TO_STRINGCOLUMN (nth 0 ListAddress))
				(itoa (+ (nth 1 ListAddress) 1))
			)
		)
	)
	(if (= NumLength 4)
		(setq StringAddress
			(strcat
				(CAEX_NUMCOLUMN_TO_STRINGCOLUMN (nth 0 ListAddress))
				(itoa (+ (nth 1 ListAddress) 1))
				":"
				(CAEX_NUMCOLUMN_TO_STRINGCOLUMN (nth 2 ListAddress))
				(itoa (+ (nth 3 ListAddress) 1))
			)
		)
	)
	StringAddress
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_STRINGADDRESS_TO_LISTADDRESS ( StringAddress / 
	ListStringAddressCell
	ListAddress)

	(setq ListStringAddressCell (CAEX_STRING_TO_LIST_NEW StringAddress ":"))
	(setq ListAddress (apply 'append (mapcar 'CAEX_STRINGADDRESSCELL_TO_LISTADDRESSCELL ListStringAddressCell)))
	ListAddress
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_STRINGADDRESSCELL_TO_LISTADDRESSCELL ( StringAddressCell / 
	Char
	ListAddressCell
	Pos
	PosBase
	PosTotal)

	(setq PosTotal (strlen StringAddressCell))
	(setq Pos 1)
	(while
		(and
			(not PosBase)
			(<= Pos PosTotal)
		)
		(setq Char (substr StringAddressCell Pos 1))
		(if (member Char (list "0" "1" "2" "3" "4" "5" "6" "7" "8" "9"))
			(setq PosBase Pos)
		)
		(setq Pos (+ Pos 1))
	)
	(setq ListAddressCell
		(list
			(CAEX_STRINGCOLUMN_TO_NUMCOLUMN (substr StringAddressCell 1 (- PosBase 1)))
			(- (atoi (substr StringAddressCell PosBase)) 1)
		)
	)
	ListAddressCell
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_STRINGCOLUMN_TO_NUMCOLUMN ( StringColumn / 
	Base
	NumColumn
	ListTemp)

	(setq Base 1)
	(setq ListTemp (reverse (mapcar '(lambda (x) (- x 64)) (vl-string->list StringColumn))))
	(setq NumColumn 0)
	(foreach Temp ListTemp
		(setq NumColumn (+ NumColumn (* Temp Base)))
		(setq Base (* Base 26))
	)
	(setq NumColumn (- NumColumn 1))
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_NUMCOLUMN_TO_STRINGCOLUMN ( NumColumn /
	NumMod
	StringColumn)

	(setq NumColumn (+ NumColumn 1))
	(setq StringColumn "")
	(while (> NumColumn 0)
		(setq NumMod (rem NumColumn 26))
		(setq StringColumn
			(strcat
				(if (= NumMod 0) "Z" (chr (+ NumMod 64)))
				StringColumn
			)
		)
		(if (= NumMod 0)
			(setq NumColumn (- (/ NumColumn 26) 1))
			(setq NumColumn (/ NumColumn 26))
		)
	)
	StringColumn
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_STRINGCONTENT_MULTIOBJECT ( ListVlaObject / 
	ListDataText
	ListPointMax
	ListPointMin
	ListStringContent
	ListTemp
	NumRoundPosition
	PointMax
	PointMin
	PointMinX
	PointMinY
	StringContent
	StringTemp
	Temp1
	Temp2)

	(setq NumRoundPosition (* (apply 'min (mapcar 'vla-get-height ListVlaObject)) 0.25))
	(foreach VlaObject ListVlaObject
		(setq ListTemp (CAEX_GET_POINTMIN_POINTMAX_TEXT_MTEXT VlaObject))
		(setq PointMin (nth 0 ListTemp))
		(setq PointMax (nth 1 ListTemp))
		(setq ListPointMin (cons PointMin ListPointMin))
		(setq ListPointMax (cons PointMax ListPointMax))

		(setq PointMin (mapcar '(lambda (x) (CAEX_ROUNDOFF_NUMBER x NumRoundPosition)) PointMin))
		(setq PointMax (mapcar '(lambda (x) (CAEX_ROUNDOFF_NUMBER x NumRoundPosition)) PointMax))
		(setq PointMinX (nth 0 PointMin))
		(setq PointMinY (nth 1 PointMin))
		(setq StringTemp (CAEX_GET_LISTSTRINGCONTENT VlaObject))
		(setq Temp2 (cons StringTemp PointMinX))
		(if (setq Temp1 (assoc PointMinY ListDataText))
			(if (not (member Temp2 Temp1))
				(setq ListDataText (subst (append Temp1 (list Temp2)) Temp1 ListDataText))
			)
			(setq ListDataText (cons (list PointMinY Temp2) ListDataText))
		)
	)

	(setq ListDataText (vl-sort ListDataText '(lambda (a b) (> (car a) (car b)))))
	(setq ListDataText
		(mapcar
			'(lambda (x) 
				(vl-sort (cdr x) '(lambda (a b) (< (cdr a) (cdr b))))
			)
			ListDataText
		)
	)

	(setq ListStringContent (mapcar '(lambda (x) (CAEX_LIST_TO_STRING (mapcar 'car (mapcar 'car x)) " ")) ListDataText))
	(setq StringContent (CAEX_LISTSTRING_TO_FORMULAR_EXCEL ListStringContent))
	(setq PointMin
		(list
			(apply 'min (mapcar '(lambda (x) (nth 0 x)) ListPointMin))
			(apply 'min (mapcar '(lambda (x) (nth 1 x)) ListPointMin))
			0.0
		)
	)
	(setq PointMax
		(list
			(apply 'max (mapcar '(lambda (x) (nth 0 x)) ListPointMax))
			(apply 'max (mapcar '(lambda (x) (nth 1 x)) ListPointMax))
			0.0
		)
	)
	(list StringContent PointMin PointMax)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_POINTMIN_POINTMAX_TEXT_MTEXT ( VlaObject /
	ListPointMax
	ListPointMin
	ListVlaObjectTemp
	PointMax
	PointMin
	SelectionSetTemp
	TypeObject
	VlaObjectCopy)

	(setq TypeObject (vla-get-ObjectName VlaObject))
	(if (= TypeObject "AcDbText")
		(progn
			(vl-catch-all-apply (function (lambda ( / )
				(vla-GetBoundingBox VlaObject 'PointMin 'PointMax)
				(setq PointMin (vlax-safearray->list PointMin))
				(setq PointMax (vlax-safearray->list PointMax))
			)))
		)
	)

	(if (= TypeObject "AcDbMText")
		(progn
			(setq VlaObjectCopy (vla-copy VlaObject))
			(setq SelectionSetTemp (acet-explode (vlax-vla-object->ename VlaObjectCopy)))
			(setq ListVlaObjectTemp (CAEX_CONVERT_SELECTIONSET_TO_LISTVLAOBJECT SelectionSetTemp))
			(foreach VlaObjectTemp ListVlaObjectTemp
				(vl-catch-all-apply (function (lambda ( / )
					(vla-GetBoundingBox VlaObjectTemp 'PointMin 'PointMax)
					(setq PointMin (vlax-safearray->list PointMin))
					(setq PointMax (vlax-safearray->list PointMax))
					(setq ListPointMin (cons PointMin ListPointMin))
					(setq ListPointMax (cons PointMax ListPointMax))
				)))
			)
			(if ListPointMin
				(setq PointMin
					(list
						(apply 'min (mapcar '(lambda (x) (nth 0 x)) ListPointMin))
						(apply 'min (mapcar '(lambda (x) (nth 1 x)) ListPointMin))
						0.0
					)
				)
				(setq PointMin Nil)
			)
			(if ListPointMax
				(setq PointMax
					(list
						(apply 'max (mapcar '(lambda (x) (nth 0 x)) ListPointMax))
						(apply 'max (mapcar '(lambda (x) (nth 1 x)) ListPointMax))
						0.0
					)
				)
				(setq PointMax Nil)
			)
			(mapcar 'vla-delete ListVlaObjectTemp)
		)
	)

	(if (and PointMin PointMax)
		(list PointMin PointMax)
		Nil
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_LISTSTRINGCONTENT ( VlaObject /
	ListStringContent
	StringTemp
	TypeObject)

	(setq TypeObject (vla-get-ObjectName VlaObject))
	(if (= TypeObject "AcDbText")
		(progn
			(setq StringTemp (cdr (assoc 1 (entget (vlax-vla-object->ename VlaObject)))))
			(setq ListStringContent (list StringTemp))
		)
	)
	(if (= TypeObject "AcDbMText")
		(progn
			(setq StringTemp (getpropertyvalue (vlax-vla-object->ename VlaObject) "TEXT"))
			(setq ListStringContent (CAEX_STRING_TO_LIST_NEW StringTemp "\r\n"))
		)
	)
	ListStringContent
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CONVERT_SELECTIONSET_TO_LISTVLAOBJECT ( SelectionSet /
	VlaObject
	ListVlaObject
	Num)

	(if SelectionSet
		(progn
			(setq Num 0)
			(repeat (sslength SelectionSet)
				(setq VlaObject (vlax-ename->vla-object (ssname SelectionSet Num)))
				(setq ListVlaObject (cons VlaObject ListVlaObject))
				(setq Num (+ Num 1))
			)
		)
	)
	ListVlaObject
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_RANDOM_STRING ( NumStringLength / m a c Temp RandomString)
	(setq m 4294967296.0)
	(setq a 1664525.0)
	(setq c 1013904223.0)
	(repeat NumStringLength
		(setq $xn (rem (+ c (* a (cond ($xn) ((getvar 'date))))) m))
		(setq Temp (fix (* (/ $xn m) 36)))
		(if (= Temp 0) (setq Temp "0"))
		(if (= Temp 1) (setq Temp "1"))
		(if (= Temp 2) (setq Temp "2"))
		(if (= Temp 3) (setq Temp "3"))
		(if (= Temp 4) (setq Temp "4"))
		(if (= Temp 5) (setq Temp "5"))
		(if (= Temp 6) (setq Temp "6"))
		(if (= Temp 7) (setq Temp "7"))
		(if (= Temp 8) (setq Temp "8"))
		(if (= Temp 9) (setq Temp "9"))
		(if (= Temp 10) (setq Temp "A"))
		(if (= Temp 11) (setq Temp "B"))
		(if (= Temp 12) (setq Temp "C"))
		(if (= Temp 13) (setq Temp "D"))
		(if (= Temp 14) (setq Temp "E"))
		(if (= Temp 15) (setq Temp "F"))
		(if (= Temp 16) (setq Temp "G"))
		(if (= Temp 17) (setq Temp "H"))
		(if (= Temp 18) (setq Temp "I"))
		(if (= Temp 19) (setq Temp "J"))
		(if (= Temp 20) (setq Temp "K"))
		(if (= Temp 21) (setq Temp "L"))
		(if (= Temp 22) (setq Temp "M"))
		(if (= Temp 23) (setq Temp "N"))
		(if (= Temp 24) (setq Temp "O"))
		(if (= Temp 25) (setq Temp "P"))
		(if (= Temp 26) (setq Temp "Q"))
		(if (= Temp 27) (setq Temp "R"))
		(if (= Temp 28) (setq Temp "S"))
		(if (= Temp 29) (setq Temp "T"))
		(if (= Temp 30) (setq Temp "U"))
		(if (= Temp 31) (setq Temp "V"))
		(if (= Temp 32) (setq Temp "W"))
		(if (= Temp 33) (setq Temp "X"))
		(if (= Temp 34) (setq Temp "Y"))
		(if (= Temp 35) (setq Temp "Z"))
		(if RandomString
			(setq RandomString (strcat Temp RandomString))
			(setq RandomString Temp)
		)
	)
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_ROUNDOFF_NUMBER ( Number ValueRoundOff /
	Temp1
	Temp2
	ValueResult
	NumberActive
	NumSwitchPositveActive)

	(setq NumberActive (abs (* Number 1.0)))
	(if (>= Number 0.0)
		(setq NumSwitchPositveActive 1.0)
		(setq NumSwitchPositveActive -1.0)
	)
	(setq Temp1 (/ NumberActive ValueRoundOff))
	(setq Temp2 (fix Temp1))
	(if (>= (abs (- Temp1 Temp2)) 0.4999999999)
		(setq ValueResult (* ValueRoundOff (+ Temp2 1.0)))
		(setq ValueResult (* ValueRoundOff (+ Temp2 0.0)))
	)
	(setq ValueResult (* NumSwitchPositveActive ValueResult))
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_STRING_TO_LIST_NEW (Stg Del / ListString)
	(setq ListString (CAEX_STRING_TO_LIST_NO_TRIM Stg Del))
	(setq ListString (mapcar '(lambda (x) (vl-string-trim " " x)) ListString))
	(setq ListString (mapcar '(lambda (x) (vl-string-trim "\t" x)) ListString))
	ListString
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_STRING_TO_LIST_NO_TRIM (Stg Del / LenDel StgTemp Pos StgSub StgSubTemp ListString)
	(if Stg
		(progn
			(setq LenDel (strlen Del))
			(setq StgTemp Stg)
			(while (setq Pos (vl-string-search Del StgTemp))
				(setq StgSub (substr StgTemp 1 Pos))
				(setq StgTemp (substr StgTemp (+ Pos 1 LenDel)))
				(setq StgSubTemp StgSub)
				(if (/= StgSubTemp "")
					(setq ListString (cons StgSub ListString))
				)
			)
			(setq StgSub StgTemp)
			(setq StgSubTemp StgSub)

			(if (/= StgSubTemp "")
				(setq ListString (cons StgSub ListString))
			)
			(if (not ListString)
				(setq ListString (list Stg))
			)
			(setq ListString (reverse ListString))
		)
	)
	ListString
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_LIST_TO_STRING ( ListString Sep / StringValue)
	(setq StringValue (car ListString))
	(foreach StringTemp (cdr ListString)
		(setq StringValue (strcat StringValue Sep StringTemp))
	)
	StringValue
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_FIND_ALIGNMENT_TEXT_MTEXT ( VlaObject / 
	Alignment
	ListDataAttachment
	TypeObject)

	(setq TypeObject (vla-get-ObjectName VlaObject))
	(if (= TypeObject "AcDbMText")
		(progn
			(setq ListDataAttachment
				(list
					(cons acAttachmentPointTopLeft "Left")
					(cons acAttachmentPointTopCenter "Center")
					(cons acAttachmentPointTopRight "Right")
					(cons acAttachmentPointMiddleLeft "Left")
					(cons acAttachmentPointMiddleCenter "Center")
					(cons acAttachmentPointMiddleRight "Right")
					(cons acAttachmentPointBottomLeft "Left")
					(cons acAttachmentPointBottomCenter "Center")
					(cons acAttachmentPointBottomRight "Right")
				)
			)
			(setq Alignment (vla-get-AttachmentPoint VlaObject))
			(setq Alignment (cdr (assoc Alignment ListDataAttachment)))
		)
	)
	(if (= TypeObject "AcDbText")
		(progn
			(setq ListDataAttachment
				(list
					(cons acAlignmentLeft "Left")
					(cons acAlignmentCenter "Center")
					(cons acAlignmentRight "Right")
					(cons acAlignmentAligned "Center")
					(cons acAlignmentMiddle "Center")
					(cons acAlignmentFit "Center")
					(cons acAlignmentTopLeft "Left")
					(cons acAlignmentTopCenter "Center")
					(cons acAlignmentTopRight "Right")
					(cons acAlignmentMiddleLeft "Left")
					(cons acAlignmentMiddleCenter "Center")
					(cons acAlignmentMiddleRight "Right")
					(cons acAlignmentBottomLeft "Left")
					(cons acAlignmentBottomCenter "Center")
					(cons acAlignmentBottomRight "Right")
				)
			)
			(setq Alignment (vla-get-Alignment VlaObject))
			(setq Alignment (cdr (assoc Alignment ListDataAttachment)))
		)
	)
	Alignment
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_FIND_VALUE_POPULAR ( ListValue / ListData )
	(foreach Value ListValue
		(if (setq Temp (assoc Value ListData))
			(setq ListData (subst (cons Value (+ (cdr Temp) 1)) Temp ListData))
			(setq ListData (cons (cons Value 1) ListData))
		)
	)
	(setq ListData (vl-sort ListData '(lambda (a b) (> (cdr a) (cdr b)))))
	(car (car ListData))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_VLA_GET_STYLENAME ( VlaObject /
	NameStyle
	TypeObject
	DataEname
	NumCode
	NameStyle
	DataEname
	DataEnameTemp)

	(vl-catch-all-apply (function (lambda ( / )
		(setq NameStyle (vla-get-stylename VlaObject))
	)))
	(if
		(or
			(not NameStyle)
			(and
				NameStyle
				(vl-string-search "?" NameStyle)
			)
		)
		(progn
			(setq TypeObject (vla-get-ObjectName VlaObject))
			(if
				(or
					(= TypeObject "AcDb2LineAngularDimension")
					(= TypeObject "AcDb3PointAngularDimension")
					(= TypeObject "AcDbAlignedDimension")
					(= TypeObject "AcDbArcDimension")
					(= TypeObject "AcDbDiametricDimension")
					(= TypeObject "AcDbFcf")
					(= TypeObject "AcDbLeader")
					(= TypeObject "AcDbOrdinateDimension")
					(= TypeObject "AcDbRadialDimension")
					(= TypeObject "AcDbRadialDimensionLarge")
					(= TypeObject "AcDbRotatedDimension")
				)
				(setq NameStyle (cdr (assoc 3 (entget (vlax-vla-object->ename VlaObject)))))
			)
			(if
				(or
					(= TypeObject "AcDbAttributeDefinition")
					(= TypeObject "AcDbMText")
					(= TypeObject "AcDbText")
					(= TypeObject "AcDbAttribute")
				)
				(setq NameStyle (cdr (assoc 7 (entget (vlax-vla-object->ename VlaObject)))))
			)
			(if (= TypeObject "AcDbMline")
				(setq NameStyle (cdr (assoc 2 (entget (vlax-vla-object->ename VlaObject)))))
			)
			(if (= TypeObject "AcDbMLeader")
				(progn
					(setq DataEname (entget (vlax-vla-object->ename VlaObject)))
					(setq NumCode 340)
					(setq NameStyle Nil)
					(while (and (assoc NumCode DataEname) (not NameStyle))
						(setq DataEnameTemp (entget (cdr (assoc NumCode DataEname))))
						(if
							(= (cdr (assoc 0 DataEnameTemp)) "MLEADERSTYLE")
							(setq NameStyle (CAEX_VLA_GET_NAME (vlax-ename->vla-object (cdr (assoc NumCode DataEname)))))
						)
						(setq DataEname (vl-remove (assoc NumCode DataEname) DataEname))
					)
				)
			)
			(if (= TypeObject "AcDbTable")
				(progn
					(setq DataEname (entget (vlax-vla-object->ename VlaObject)))
					(setq NumCode 342)
					(setq NameStyle Nil)
					(while (and (assoc NumCode DataEname) (not NameStyle))
						(setq DataEnameTemp (entget (cdr (assoc NumCode DataEname))))
						(if
							(= (cdr (assoc 0 DataEnameTemp)) "TABLESTYLE")
							(setq NameStyle (CAEX_VLA_GET_NAME (vlax-ename->vla-object (cdr (assoc NumCode DataEname)))))
						)
						(setq DataEname (vl-remove (assoc NumCode DataEname) DataEname))
					)
				)
			)
		)
	)
	NameStyle
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_LISTSTRING_TO_FORMULAR_EXCEL ( ListString / StringResult)
	(setq ListString (mapcar 'CAEX_TRANSLATE_SEPARATOR_EXCEL ListString))
	(setq StringResult (strcat "=" (CAEX_LIST_TO_STRING ListString "&CHAR(10)&")))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_TRANSLATE_SEPARATOR_EXCEL ( String /
	ListTemp
	StringResult)

	(setq ListTemp (CAEX_STRING_TO_LIST_NO_TRIM String SeparatorCSV))
	(setq ListTemp (mapcar 'CAEX_TRANSLATE_UNICODE_EXCEL ListTemp))
	(setq StringResult (CAEX_LIST_TO_STRING ListTemp (strcat "&CHAR(" (itoa (car (vl-string->list SeparatorCSV))) ")&")))
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_TRANSLATE_UNICODE_EXCEL ( String /
	NumLength
	StringFormular
	ListTemp
	Temp1
	Temp2)
	
	(setq String (CAEX_CONVERT_STRING_TO_UNICODE String))
	(setq String (strcat "*" String))
	(setq ListTemp (CAEX_STRING_TO_LIST_NO_TRIM String "\\U+"))
	(setq Temp1 (car ListTemp))
	(setq Temp2 (substr Temp1 2))
	(setq StringFormular (strcat "\"" Temp2 "\""))

	(foreach Temp1 (cdr ListTemp)
		(if (< (strlen Temp1) 4)
			(setq StringFormular (strcat StringFormular "&" "\"" Temp1 "\""))
			(progn
				(setq Temp2
					(strcat
						"UNICHAR(HEX2DEC(\""
						(substr Temp1 1 4)
						"\"))"
					)
				)
				(setq StringFormular (strcat StringFormular "&" Temp2))
				(setq Temp2 (substr Temp1 5))
				(if (/= Temp2 "")
					(setq StringFormular (strcat StringFormular "&" "\"" Temp2 "\""))
				)
			)
		)
	)
	StringFormular
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_CONVERT_STRING_TO_UNICODE ( StringInput / StringResult)
	(setq StringResult
		(apply 'strcat
			(mapcar
				'(lambda ( x / s )
					(if (<= x 121)
						(setq s (chr x))
						(progn
							(setq s (CAEX_CONVERT_DECIMAL_TO_BASE x 16))
							(repeat (- 4 (strlen s))
								(setq s (strcat "0" s))
							)
							(setq s (strcat "\\U+" s))
						)
					)
					s
				)
				(vl-string->list StringInput)
			)
		)
	)
	StringResult
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_CONVERT_DECIMAL_TO_BASE ( Num Base / 
	CharTemp
	NumMod
	StringNum)

	(setq StringNum "")
	(while (> Num 0)
		(setq NumMod (rem Num Base))
		(if (< NumMod 10)
			(setq CharTemp (chr (+ NumMod 48)))
			(setq CharTemp (chr (+ NumMod 55)))
		)
		(setq StringNum (strcat CharTemp StringNum))
		(setq Num (/ Num Base))
	)
	(if (= StringNum "")
		(setq StringNum "0")
	)
	StringNum
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CHECK_SAME_POINT ( Point1 Point2 ToleranceValue / DistanceLength)
	(setq DistanceLength (distance Point1 Point2))
	(equal DistanceLength 0.0 ToleranceValue)
)
--------------------------------------------------------------------------------------------------------------------
(defun CAEX_CHECK_FILE_EXIST ( File /
	FileResult
	VlaFileSystem 
	VlaFile)

	(setq VlaFileSystem (vla-getinterfaceobject (vlax-get-acad-object) "Scripting.FileSystemObject"))
	(if (= (vlax-invoke-method VlaFileSystem "FileExists" File) :vlax-true)
		(progn
			(setq VlaFile (vlax-invoke-method VlaFileSystem "GetFile" File))
			(setq FileResult (vlax-get-property VlaFile "Path"))
		)
	)
	FileResult
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_GET_PROPERTIES_OBJECT ( VlaObject /
	ListNameProperty
	ListPropertyObject)

	(setq ListNameProperty
		(list
			"EntityTransparency"
			"Layer"
			"Linetype"
			"LinetypeScale"
			"Lineweight"
			"Material"
			"PlotStyleName"
			"TrueColor"
		)
	)
	(foreach NameProperty ListNameProperty
		(vl-catch-all-error-p (vl-catch-all-apply (function (lambda ( / )
			(setq ListPropertyObject (cons (cons NameProperty (vlax-get-property VlaObject NameProperty)) ListPropertyObject))
		))))
	)
	ListPropertyObject
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_PUT_PROPERTIES_OBJECT ( VlaObject ListPropertyObject /
	NameProperty
	ValueProperty)

	(foreach PropertyObject ListPropertyObject
		(vl-catch-all-error-p (vl-catch-all-apply (function (lambda ( / )
			(setq NameProperty (car PropertyObject))
			(setq ValueProperty (cdr PropertyObject))
			(vlax-put-property VlaObject NameProperty ValueProperty)
		))))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CREATE_POLYLINE ( DataPolyline CheckClosed /
	ListBulge
	ListPoint 
	ListCoordinates
	Num
	VlaObjectPolyline)

	(setq ListPoint (nth 0 DataPolyline))
	(setq ListBulge (nth 1 DataPolyline))
	(setq ListCoordinates (apply 'append (mapcar '(lambda (x) (list (nth 0 x) (nth 1 x))) ListPoint)))
	(setq VlaObjectPolyline
		(vla-AddLightWeightPolyline
			VlaSpace
			(vlax-safearray-fill
				(vlax-make-safearray
					vlax-vbDouble
					(cons 0 (- (length ListCoordinates) 1))
				)
				ListCoordinates
			)
		)
	)
	(setq Num 0)
	(repeat (length ListBulge)
		(setq Bulge (nth Num ListBulge))
		(vla-SetBulge VlaObjectPolyline Num Bulge)
		(setq Num (+ Num 1))
	)
	(if CheckClosed
		(vla-put-closed VlaObjectPolyline :vlax-true)
	)
	VlaObjectPolyline
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_CREATE_LISTVLALAYERLOCK ( / VlaLayersGroup)
	(setq VlaLayersGroup (vla-get-layers VlaDrawingCurrent))
	(vlax-for VlaLayer VlaLayersGroup
		(if
			(= (vla-get-Lock VlaLayer) :vlax-true)
			(progn
				(vla-put-Lock VlaLayer :vlax-false)
				(setq ListVlaLayerLock (cons VlaLayer ListVlaLayerLock))
			)
		)
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_RESTORE_LOCK_LAYER ( / )
	(foreach VlaLayerLock ListVlaLayerLock
		(vl-catch-all-error-p (vl-catch-all-apply 'vla-put-Lock (list VlaLayerLock :vlax-true)))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_SET_VARSYSTEM_C2E ( / Temp VarSystem)
	(foreach Temp (list (list "CMDECHO" 0) (list "DIMZIN" 8) (list "MODEMACRO" "Export table in cad to excel..."))
		(setq VarSystem (car Temp))
		(setq ListVarSystem_OldValue (cons (list VarSystem (getvar VarSystem)) ListVarSystem_OldValue))
		(setvar VarSystem (cadr Temp))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_SET_VARSYSTEM_E2C ( / Temp VarSystem)
	(foreach Temp (list (list "CMDECHO" 0) (list "DIMZIN" 8) (list "MODEMACRO" "Export table in excel to cad..."))
		(setq VarSystem (car Temp))
		(setq ListVarSystem_OldValue (cons (list VarSystem (getvar VarSystem)) ListVarSystem_OldValue))
		(setvar VarSystem (cadr Temp))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_SET_VARSYSTEM_CUE ( / Temp VarSystem)
	(foreach Temp (list (list "CMDECHO" 0) (list "DIMZIN" 8) (list "MODEMACRO" "Update table in cad from excel..."))
		(setq VarSystem (car Temp))
		(setq ListVarSystem_OldValue (cons (list VarSystem (getvar VarSystem)) ListVarSystem_OldValue))
		(setvar VarSystem (cadr Temp))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_SET_VARSYSTEM_EUC ( / Temp VarSystem)
	(foreach Temp (list (list "CMDECHO" 0) (list "DIMZIN" 8) (list "MODEMACRO" "Update table in excel from cad..."))
		(setq VarSystem (car Temp))
		(setq ListVarSystem_OldValue (cons (list VarSystem (getvar VarSystem)) ListVarSystem_OldValue))
		(setvar VarSystem (cadr Temp))
	)
)
-------------------------------------------------------------------------------------------------------------------
(defun CAEX_RESET_VARSYSTEM ( / Temp VarSystem)
	(foreach Temp ListVarSystem_OldValue
		(setvar (car Temp) (cadr Temp))
	)
)



