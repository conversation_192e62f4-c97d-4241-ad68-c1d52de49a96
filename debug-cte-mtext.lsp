; Debug file cho CTE MTEXT issue
; Kiem tra tai sao CTE khong xuat duoc MTEXT

; Load file CTE (reload to get latest changes)
(load "HNP_Cad Link Excel_CTE.lsp")

; Ham debug CTE step by step
(defun C:DEBUG-CTE ( / 
	ListTemp
	ListVlaObjectLine
	ListVlaObjectText
	ListVlaObjectDelete
	ListDataLineX
	ListDataLineY
	ListCoordinateX
	ListCoordinateY
	GroupListAddress
	ListDataTable
	ToleranceValue)

	(princ "\n=== DEBUG CTE PROCESS ===")
	
	; Buoc 1: Chon objects
	(princ "\nBuoc 1: Chon objects...")
	(setq ToleranceValue 1e-8)
	(setq ListTemp (CAEX_SELECT_TABLE_IN_CAD))
	(setq ListVlaObjectLine (nth 0 ListTemp))
	(setq ListVlaObjectText (nth 1 ListTemp))
	(setq ListVlaObjectDelete (nth 2 ListTemp))
	
	(princ (strcat "\nSo line objects: " (itoa (length ListVlaObjectLine))))
	(princ (strcat "\nSo text objects: " (itoa (length ListVlaObjectText))))
	(princ (strcat "\nSo delete objects: " (itoa (length ListVlaObjectDelete))))
	
	; Kiem tra loai text objects
	(if ListVlaObjectText
		(progn
			(princ "\nLoai text objects:")
			(foreach VlaObj ListVlaObjectText
				(princ (strcat "\n- " (vla-get-ObjectName VlaObj)))
			)
		)
		(princ "\nKHONG CO TEXT OBJECTS!")
	)
	
	; Buoc 2: Xu ly lines
	(if (and ListVlaObjectLine ListVlaObjectText)
		(progn
			(princ "\nBuoc 2: Xu ly lines...")
			(setq ListTemp (CAEX_GET_LISTDATALINE_FROM_CAD))
			(setq ListDataLineX (nth 0 ListTemp))
			(setq ListDataLineY (nth 1 ListTemp))
			(setq ListCoordinateX (mapcar 'car ListDataLineX))
			(setq ListCoordinateY (mapcar 'car ListDataLineY))
			
			(princ (strcat "\nSo coordinate X: " (itoa (length ListCoordinateX))))
			(princ (strcat "\nSo coordinate Y: " (itoa (length ListCoordinateY))))
			
			; Buoc 3: Tao group address
			(princ "\nBuoc 3: Tao group address...")
			(setq GroupListAddress (CAEX_GET_GROUPLISTADDRESS_FROM_CAD))
			(princ (strcat "\nSo group address: " (itoa (length GroupListAddress))))
			
			; Buoc 4: Xu ly data table
			(princ "\nBuoc 4: Xu ly data table...")
			(setq ListDataTable (vl-catch-all-apply 'CAEX_GET_LISTDATATABLE_FROM_CAD '()))
			(if (vl-catch-all-error-p ListDataTable)
				(progn
					(princ "\nLOI trong CAEX_GET_LISTDATATABLE_FROM_CAD:")
					(princ (vl-catch-all-error-message ListDataTable))
					(setq ListDataTable nil)
				)
				(princ (strcat "\nSo data table: " (itoa (length ListDataTable))))
			)
			
			; Hien thi chi tiet data table
			(if ListDataTable
				(progn
					(princ "\nChi tiet data table:")
					(foreach DataTable ListDataTable
						(setq StringContent (nth 1 (assoc 2 DataTable)))
						(princ (strcat "\n- Content: '" StringContent "'"))
					)
				)
				(princ "\nKHONG CO DATA TABLE!")
			)
		)
		(princ "\nKHONG DU LINE VA TEXT OBJECTS!")
	)
	
	(princ "\n=== DEBUG COMPLETED ===")
	(princ)
)

; Ham debug chi tiet MTEXT processing
(defun C:DEBUG-MTEXT-PROCESSING ( / ss ent vla-obj type-obj string-content cleaned-content)
	(princ "\n=== DEBUG MTEXT PROCESSING ===")
	(princ "\nChon mot MTEXT object: ")
	(setq ss (ssget '((0 . "MTEXT"))))
	
	(if ss
		(progn
			(setq ent (ssname ss 0))
			(setq vla-obj (vlax-ename->vla-object ent))
			(setq type-obj (vla-get-ObjectName vla-obj))
			
			(princ (strcat "\nObject type: " type-obj))
			
			; Test ham CAEX_GET_LISTSTRINGCONTENT
			(setq string-content (CAEX_GET_LISTSTRINGCONTENT vla-obj))
			(princ (strcat "\nCACEX_GET_LISTSTRINGCONTENT result: " (vl-prin1-to-string string-content)))
			
			; Test truc tiep getpropertyvalue
			(setq raw-text (getpropertyvalue ent "TEXT"))
			(princ (strcat "\nRaw TEXT property: '" raw-text "'"))
			
			; Test CLEAN-MTEXT
			(setq cleaned-content (CLEAN-MTEXT raw-text))
			(princ (strcat "\nCLEAN-MTEXT result: '" cleaned-content "'"))
			
			; Test boundary box
			(setq boundary-result (CAEX_GET_POINTMIN_POINTMAX_TEXT_MTEXT vla-obj))
			(princ (strcat "\nBoundary result: " (vl-prin1-to-string boundary-result)))
		)
		(princ "\nKhong co MTEXT nao duoc chon!")
	)
	
	(princ "\n=== MTEXT PROCESSING DEBUG COMPLETED ===")
	(princ)
)

; Ham debug cell detection
(defun C:DEBUG-CELL-DETECTION ( / 
	ListTemp
	ListVlaObjectLine
	ListVlaObjectText
	ListDataLineX
	ListDataLineY
	ListCoordinateX
	ListCoordinateY
	GroupListAddress
	ToleranceValue
	test-address
	result-text)

	(princ "\n=== DEBUG CELL DETECTION ===")
	
	; Setup co ban
	(setq ToleranceValue 1e-8)
	(setq ListTemp (CAEX_SELECT_TABLE_IN_CAD))
	(setq ListVlaObjectLine (nth 0 ListTemp))
	(setq ListVlaObjectText (nth 1 ListTemp))
	
	(if (and ListVlaObjectLine ListVlaObjectText)
		(progn
			; Xu ly lines
			(setq ListTemp (CAEX_GET_LISTDATALINE_FROM_CAD))
			(setq ListDataLineX (nth 0 ListTemp))
			(setq ListDataLineY (nth 1 ListTemp))
			(setq ListCoordinateX (mapcar 'car ListDataLineX))
			(setq ListCoordinateY (mapcar 'car ListDataLineY))
			
			; Tao group address
			(setq GroupListAddress (CAEX_GET_GROUPLISTADDRESS_FROM_CAD))
			
			(princ (strcat "\nSo cells detected: " (itoa (length GroupListAddress))))
			
			; Test tung cell
			(foreach ListAddress GroupListAddress
				(princ (strcat "\nTesting cell: " (vl-prin1-to-string ListAddress)))
				(setq result-text (CAEX_GET_LISTVLATEXT_IN_CELL ListAddress))
				(princ (strcat "\nText objects in cell: " (itoa (length result-text))))
				(if result-text
					(foreach txt-obj result-text
						(princ (strcat "\n- Type: " (vla-get-ObjectName txt-obj)))
					)
				)
			)
		)
		(princ "\nKhong du line va text objects!")
	)
	
	(princ "\n=== CELL DETECTION DEBUG COMPLETED ===")
	(princ)
)

(princ "\nDebug functions loaded:")
(princ "\n- DEBUG-CTE: Debug toan bo quy trinh CTE")
(princ "\n- DEBUG-MTEXT-PROCESSING: Debug xu ly MTEXT")
(princ "\n- DEBUG-CELL-DETECTION: Debug phat hien cells")
(princ)
