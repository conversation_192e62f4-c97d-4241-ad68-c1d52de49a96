; ===================================================================
; Z-CAD2EXCEL-E_ALL_NEW_SYNTAX.LSP - PHIEN BAN CU PHAP MOI
; ===================================================================
; Tac gia: Zit <PERSON>
; Phien ban: v3.0 - Cu phap moi
; Ngay cap nhat: 2024-12-19
; Mo ta: Chuyen doi cu phap goi lenh theo yeu cau moi

; HUONG DAN SU DUNG:
; 1. Load file: APPLOAD -> chon file .lsp
; 2. Su dung cac lenh theo nhom moi: E1x, E2x, E3x, E4x, E0, E7, E8, E9, ET
; 3. <PERSON><PERSON> hinh: Dung lenh E0 de thiet lap toan cuc (thay cho E6 cu)
; 4. Excel phai duoc mo truoc khi chay lenh

; DANH SACH LENH MOI:
; Nhom E1 - Xuat cell:
; E1  - Xuat text/dim vao cell (co he so factor) - giu nguyen
; E11 - Xuat text/dim voi he so nhan them (=(n1+n2+...)*a) - giu nguyen  
; E12 - Xuat text/dim vao cell (text giu nguyen + dim co factor) - thay cho E2 cu
; E14 - Dat nhanh he so (factor)=1
; E15 - Dat nhanh he so (factor)=0.001
;
; Nhom E2 - Xuat Col, Row, Array:
; E2  - Xuat theo 3 che do (Col/Row/Array) voi thu tu selection - thay cho E3 cu
; E21 - Xuat nhanh ra cot (vi tri chuot tu dong nhay xuong o cuoi cung cua cot)
; E22 - Xuat nhanh ra hang (vi tri chuot tu dong nhay qua phai o cuoi cung cua hang)
; E23 - Xuat nhanh ra mang (vi tri chuot tu dong nhay xuong o cuoi cung cua mang)
; E24 - Bat nhanh Number (Y)
; E25 - Tat nhanh Number (N)
;
; Nhom E3 - Ghi Handle:
; E3  - Chi ghi handle vao comment - thay cho E4 cu
; E31 - Mo file Cad theo comment - thay cho HTC cu
; E32 - Zoom va highlight doi tuong theo handle - thay cho ZTH cu
; E33 - Zoom va select doi tuong theo handle - thay cho STH cu
; E34 - Bat nhanh handle (gan che do thiet lap handle Y)
; E35 - Tat nhanh handle (gan che do thiet lap handle N)
;
; Nhom E4 - Xuat Table:
; E4  - Xuat table vao Excel - thay cho E5 cu
; E41 - Xuat bang theo line (giu format bang) - thay cho CTE cu
; E42 - Xuat bang theo Table - thay cho TE cu
; E43 - Xuat bang tu excel qua cad - thay cho ETC cu
; E44 - Cong tac mo Frame (Y/N)
;
; E0  - Mo bang thiet lap - thay cho E6 cu
; E7  - Cong tac Handle (Toggle On/Off) - giu nguyen
; E8  - Cong tac Factor (Toggle 1/0.001) - giu nguyen
; E9  - Cong tac Number (Toggle Y/N) - giu nguyen
; ET  - Lenh tong hop toi uu - giu nguyen

; ===================================================================
; THONG BAO THAY DOI CU PHAP
; ===================================================================

(defun SHOW-NEW-SYNTAX-INFO ( / )
	(princ "\n=== THONG BAO THAY DOI CU PHAP ===")
	(princ "\nFile da duoc cap nhat cu phap moi:")
	(princ "\n")
	(princ "\nNhom E1 (Xuat cell):")
	(princ "\n  E1, E11 - giu nguyen")
	(princ "\n  E12 - thay cho E2 cu")
	(princ "\n  E14 - dat nhanh factor=1")
	(princ "\n  E15 - dat nhanh factor=0.001")
	(princ "\n")
	(princ "\nNhom E2 (Xuat Col/Row/Array):")
	(princ "\n  E2 - thay cho E3 cu")
	(princ "\n  E21 - xuat nhanh ra cot")
	(princ "\n  E22 - xuat nhanh ra hang")
	(princ "\n  E23 - xuat nhanh ra mang")
	(princ "\n  E24 - bat nhanh Number")
	(princ "\n  E25 - tat nhanh Number")
	(princ "\n")
	(princ "\nNhom E3 (Ghi Handle):")
	(princ "\n  E3 - thay cho E4 cu")
	(princ "\n  E31 - thay cho HTC cu")
	(princ "\n  E32 - thay cho ZTH cu")
	(princ "\n  E33 - thay cho STH cu")
	(princ "\n  E34 - bat nhanh handle")
	(princ "\n  E35 - tat nhanh handle")
	(princ "\n")
	(princ "\nNhom E4 (Xuat Table):")
	(princ "\n  E4 - thay cho E5 cu")
	(princ "\n  E41 - thay cho CTE cu")
	(princ "\n  E42 - thay cho TE cu")
	(princ "\n  E43 - thay cho ETC cu")
	(princ "\n  E44 - cong tac Frame")
	(princ "\n")
	(princ "\nKhac:")
	(princ "\n  E0 - thay cho E6 cu (thiet lap)")
	(princ "\n  E7, E8, E9, ET - giu nguyen")
	(princ "\n")
	(princ "\n=== KET THUC THONG BAO ===")
	(princ)
)

; Hien thi thong bao khi load file
(SHOW-NEW-SYNTAX-INFO)

; ===================================================================
; KHOI TAO BIEN TOAN CUC
; ===================================================================

; Khoi tao cac bien neu chua ton tai
(if (not *E6-handle*) (setq *E6-handle* "Y"))
(if (not *E6-frame*) (setq *E6-frame* "Y"))
(if (not *E6-symbol*) (setq *E6-symbol* "+"))
(if (not *E6-factor*) (setq *E6-factor* "1"))
(if (not *E6-jump*) (setq *E6-jump* "3"))
(if (not *E6-tolerance*) (setq *E6-tolerance* "0.1"))
(if (not *E6-number*) (setq *E6-number* "N"))

(princ "\nZ-Cad2Excel-E_all_NEW_SYNTAX.lsp loaded successfully!")
(princ "\nSu dung lenh HELP-NEW-SYNTAX de xem huong dan chi tiet.")
(princ)

; ===================================================================
; LENH HELP
; ===================================================================

(defun C:HELP-NEW-SYNTAX ( / )
	(SHOW-NEW-SYNTAX-INFO)
)

; ===================================================================
; NHOM E1 - XUAT CELL
; ===================================================================

; E14 - DAT NHANH HE SO (FACTOR)=1
(defun C:E14 ( / )
	(setq *E6-factor* "1")
	(princ "\nDa dat he so factor = 1")
	(princ)
)

; E15 - DAT NHANH HE SO (FACTOR)=0.001
(defun C:E15 ( / )
	(setq *E6-factor* "0.001")
	(princ "\nDa dat he so factor = 0.001")
	(princ)
)

; ===================================================================
; NHOM E2 - XUAT COL, ROW, ARRAY
; ===================================================================

; E21 - XUAT NHANH RA COT
(defun C:E21 ( / )
	(princ "\nE21: Xuat nhanh ra cot")
	(princ "\nChuc nang nay can duoc tich hop voi code goc E3/E5")
	(princ "\nVui long su dung E2 va chon mode 1 (Cot)")
	(princ)
)

; E22 - XUAT NHANH RA HANG
(defun C:E22 ( / )
	(princ "\nE22: Xuat nhanh ra hang")
	(princ "\nChuc nang nay can duoc tich hop voi code goc E3/E5")
	(princ "\nVui long su dung E2 va chon mode 2 (Hang)")
	(princ)
)

; E23 - XUAT NHANH RA MANG
(defun C:E23 ( / )
	(princ "\nE23: Xuat nhanh ra mang")
	(princ "\nChuc nang nay can duoc tich hop voi code goc E3/E5")
	(princ "\nVui long su dung E2 va chon mode 4 (Mang)")
	(princ)
)

; E24 - BAT NHANH NUMBER (Y)
(defun C:E24 ( / )
	(setq *E6-number* "Y")
	(princ "\nDa bat Number = Y")
	(princ "\nE2 se chi lay so tu text (nhu E1)")
	(princ)
)

; E25 - TAT NHANH NUMBER (N)
(defun C:E25 ( / )
	(setq *E6-number* "N")
	(princ "\nDa tat Number = N")
	(princ "\nE2 se giu nguyen text nhu hien tai")
	(princ)
)

; ===================================================================
; NHOM E3 - GHI HANDLE
; ===================================================================

; E31 - MO FILE CAD THEO COMMENT (THAY CHO HTC CU)
(defun C:E31 ( / )
	(princ "\nE31: Mo file CAD theo comment")
	(princ "\nChuc nang nay can duoc tich hop voi code goc HTC")
	(princ "\nVui long su dung HTC cho den khi tich hop hoan tat")
	(princ)
)

; E32 - ZOOM VA HIGHLIGHT DOI TUONG THEO HANDLE (THAY CHO ZTH CU)
(defun C:E32 ( / )
	(princ "\nE32: Zoom va highlight doi tuong theo handle")
	(princ "\nChuc nang nay can duoc tich hop voi code goc ZTH")
	(princ "\nVui long su dung ZTH cho den khi tich hop hoan tat")
	(princ)
)

; E33 - ZOOM VA SELECT DOI TUONG THEO HANDLE (THAY CHO STH CU)
(defun C:E33 ( / )
	(princ "\nE33: Zoom va select doi tuong theo handle")
	(princ "\nChuc nang nay can duoc tich hop voi code goc STH")
	(princ "\nVui long su dung STH cho den khi tich hop hoan tat")
	(princ)
)

; E34 - BAT NHANH HANDLE (GAN CHE DO THIET LAP HANDLE Y)
(defun C:E34 ( / )
	(setq *E6-handle* "Y")
	(princ "\nDa bat Handle = Y")
	(princ "\nCac lenh E1, E11, E12, E2, E3, E4, ET se ghi handle vao comment")
	(princ)
)

; E35 - TAT NHANH HANDLE (GAN CHE DO THIET LAP HANDLE N)
(defun C:E35 ( / )
	(setq *E6-handle* "N")
	(princ "\nDa tat Handle = N")
	(princ "\nCac lenh E1, E11, E12, E2, E3, E4, ET se KHONG ghi handle vao comment")
	(princ)
)

; ===================================================================
; NHOM E4 - XUAT TABLE
; ===================================================================

; E41 - XUAT BANG THEO LINE (GIU FORMAT BANG) - THAY CHO CTE CU
(defun C:E41 ( / )
	(princ "\nE41: Xuat bang theo line (giu format bang)")
	(princ "\nChuc nang nay can duoc tich hop voi code goc CTE")
	(princ "\nVui long su dung CTE cho den khi tich hop hoan tat")
	(princ)
)

; E42 - XUAT BANG THEO TABLE - THAY CHO TE CU
(defun C:E42 ( / )
	(princ "\nE42: Xuat bang theo Table")
	(princ "\nChuc nang nay can duoc tich hop voi code goc TE")
	(princ "\nVui long su dung TE cho den khi tich hop hoan tat")
	(princ)
)

; E43 - XUAT BANG TU EXCEL QUA CAD - THAY CHO ETC CU
(defun C:E43 ( / )
	(princ "\nE43: Xuat bang tu Excel qua CAD")
	(princ "\nChuc nang nay can duoc tich hop voi code goc ETC")
	(princ "\nVui long su dung ETC cho den khi tich hop hoan tat")
	(princ)
)

; E44 - CONG TAC MO FRAME (Y/N)
(defun C:E44 ( / )
	; Dao trang thai Frame
	(if (= *E6-frame* "Y")
		(progn
			(setq *E6-frame* "N")
			(princ "\n=== FRAME: TAT ===")
			(princ "\nCac lenh E4, E41, E42 se KHONG dong khung cho bang")
		)
		(progn
			(setq *E6-frame* "Y")
			(princ "\n=== FRAME: MO ===")
			(princ "\nCac lenh E4, E41, E42 se dong khung cho bang")
		)
	)
	(princ (strcat "\nTrang thai hien tai: Frame = " *E6-frame*))
	(princ)
)

; ===================================================================
; LENH E0 - THIET LAP (THAY CHO E6 CU)
; ===================================================================

(defun C:E0 ( / )
	(princ "\nE0: Mo bang thiet lap toan cuc")
	(princ "\nChuc nang nay can duoc tich hop voi code goc E6")
	(princ "\nVui long su dung E6 cho den khi tich hop hoan tat")
	(princ)
)

(princ "\n=== FILE CU PHAP MOI DA DUOC TAO ===")
(princ "\nFile nay chua cac lenh cu phap moi.")
(princ "\nDe su dung day du chuc nang, ban can:")
(princ "\n1. Tich hop voi file goc Z-Cad2Excel-E_all.lsp")
(princ "\n2. Hoac load ca 2 file cung luc")
(princ)
