;; LISP T4: Tìm Text/MText dựa trên mẫu, Zoom và Chọn kết quả (Phiên bản Ho<PERSON>)
(defun c:T4 ( /
              ;; Biến cho xử lý lỗi tùy chỉnh
              *error* original-error-handler
              ;; <PERSON>iế<PERSON> lưu trạng thái hệ thống
              oldCmdEcho oldHighlight
              ;; <PERSON>iế<PERSON> cho việc chọn và xử lý Text/MText mẫu
              sourceEnamePoint sourceEname sourceEntityData sourceType sourceTextContent strSourceTextLower
              ;; Biến cho vùng tìm kiếm và kết quả
              ssSearchArea resultSS numFound i entName currentText
              ;; Biến cho các đối tượng VLA và tính toán bounding box
              obj minPoint maxPoint minPt maxPt
              overallMinPt overallMaxPt currentMinPt currentMaxPt
              ;; <PERSON>i<PERSON><PERSON> cho tính toán cửa sổ zoom
              viewMinPt viewMaxPt deltaX deltaY
             )

  ;; --- <PERSON><PERSON><PERSON> nghĩa hàm xử lý lỗi cục bộ ---
  (setq original-error-handler *error*) ; <PERSON><PERSON><PERSON> lại trình xử lý *error* hiện tại
  (defun *error* (msg) ; msg là thông điệp lỗi (nếu có)
    ;; Khôi phục các biến hệ thống về giá trị ban đầu
    (if oldCmdEcho (setvar "CMDECHO" oldCmdEcho))
    (if oldHighlight (setvar "HIGHLIGHT" oldHighlight))
    ;; Khôi phục trình xử lý lỗi *error* gốc của AutoCAD
    (setq *error* original-error-handler)
    ;; Chỉ hiển thị thông báo lỗi nếu đó không phải là hủy lệnh từ người dùng
    (if (and msg (not (member msg '("Function cancelled" "quit / exit abort" "console break"))))
        (princ (strcat "\nLỗi T4: " msg))
    )
    (princ) ; Kết thúc "sạch" hàm *error*
  )

  ;; --- Khởi tạo: Lưu trạng thái biến hệ thống và thiết lập môi trường ---
  (setq oldCmdEcho (getvar "CMDECHO"))
  (setq oldHighlight (getvar "HIGHLIGHT"))
  (setvar "CMDECHO" 0) ; Tắt command echo để giao diện gọn gàng
  (vl-load-com)     ; Đảm bảo các hàm Visual LISP COM sẵn sàng

  (princ "\nLệnh T4: Tìm Text theo mẫu, Zoom và Chọn kết quả (Phiên bản Hoàn Thiện).")

  ;; --- B1: Chọn đối tượng Text/MText mẫu ---
  (setq sourceEnamePoint nil sourceTextContent :NOT_YET_SET) ; Đánh dấu chưa có nội dung
  (while (not sourceEnamePoint) ; Lặp cho đến khi chọn được hoặc hủy
    (setq sourceEnamePoint (entsel "\nChọn Text/MText mẫu để lấy nội dung tìm kiếm: "))
    (cond
      (sourceEnamePoint ; Người dùng đã chọn một đối tượng
       (setq sourceEname (car sourceEnamePoint))
       (setq sourceEntityData (entget sourceEname))
       (setq sourceType (cdr (assoc 0 sourceEntityData)))
       (if (not (member sourceType '("TEXT" "MTEXT")))
         (progn
           (princ (strcat "\nĐối tượng chọn không phải là TEXT hoặc MTEXT (Loại: " sourceType "). Vui lòng chọn lại."))
           (setq sourceEnamePoint nil) ; Yêu cầu chọn lại
         )
         (progn ; Đối tượng hợp lệ, lấy nội dung
           (setq sourceTextContent (cdr (assoc 1 sourceEntityData)))
           (if (or (not sourceTextContent) (= sourceTextContent ""))
             (progn
               (princ "\nText/MText mẫu không có nội dung hoặc nội dung rỗng.")
               (setq sourceEnamePoint T) ; Đặt để thoát vòng lặp while
               (setq sourceTextContent :CONTENT_EMPTY_OR_INVALID) ; Đánh dấu nội dung không hợp lệ
             )
             (princ (strcat "\nNội dung mẫu cần tìm: \"" sourceTextContent "\""))
           )
         )
       )
      )
      (t ; Người dùng nhấn ESC (entsel trả về nil)
       (princ "\nĐã hủy việc chọn đối tượng mẫu.")
       (*error* "Function cancelled") ; Gọi *error* với thông điệp hủy chuẩn
       (return-from c:T4 nil) ; Thoát khỏi hàm C:T4
      )
    )
  )

  ;; Kiểm tra lại nội dung mẫu trước khi tiếp tục
  (if (or (eq sourceTextContent :NOT_YET_SET) (eq sourceTextContent :CONTENT_EMPTY_OR_INVALID))
    (progn
      (princ "\nKhông có nội dung mẫu hợp lệ để tìm kiếm. Kết thúc lệnh T4.")
      (*error* nil) ; Gọi *error* để dọn dẹp (nếu có) và khôi phục *error* gốc
      (return-from c:T4 nil)
    )
  )
  (setq strSourceTextLower (strcase sourceTextContent)) ; Chuẩn hóa text mẫu (không phân biệt hoa/thường)

  ;; --- B2: Chọn vùng các đối tượng Text/MText để tìm kiếm ---
  (princ "\nChọn các đối tượng Text/MText để tìm kiếm trong đó: ")
  (setq ssSearchArea (ssget '((0 . "TEXT,MTEXT")))) ; Lọc chỉ TEXT hoặc MTEXT

  (if ssSearchArea
    (progn
      ;; --- B3: Tìm kiếm các đối tượng thỏa mãn điều kiện ---
      (setq resultSS (ssadd) i 0) ; Khởi tạo selection set kết quả và biến đếm
      (repeat (sslength ssSearchArea)
        (setq entName (ssname ssSearchArea i))
        (setq currentText (cdr (assoc 1 (entget entName))))
        (if currentText ; Đảm bảo đối tượng có nội dung text
          (if (= (strcase currentText) strSourceTextLower) ; So sánh chính xác (không phân biệt hoa/thường)
            (ssadd entName resultSS) ; Thêm vào kết quả nếu trùng khớp
          )
        )
        (setq i (1+ i))
      )

      ;; --- B4: Xử lý, Zoom và Chọn kết quả ---
      (setq numFound (sslength resultSS))
      (if (> numFound 0)
        (progn ; THEN: Nếu tìm thấy đối tượng
          (princ (strcat "\nĐã tìm thấy " (itoa numFound) " đối tượng có nội dung giống với mẫu."))
          (princ "\nĐang zoom và chọn đối tượng...")
          (cond
            ((= numFound 1) ; Trường hợp tìm thấy 1 đối tượng
             (setq entName (ssname resultSS 0)) (setq obj (vlax-ename->vla-object entName))
             (if (and obj (vlax-method-applicable-p obj 'GetBoundingBox))
               (progn
                 (vla-GetBoundingBox obj 'minPoint 'maxPoint) (setq minPt (vlax-safearray->list minPoint)) (setq maxPt (vlax-safearray->list maxPoint))
                 (setq deltaX (* (- (car maxPt) (car minPt)) 0.15)) (setq deltaY (* (- (cadr maxPt) (cadr minPt)) 0.15))
                 (if (< deltaX 0.5) (setq deltaX 0.5)) (if (< deltaY 0.5) (setq deltaY 0.5))
                 (setq viewMinPt (list (- (car minPt) deltaX) (- (cadr minPt) deltaY) (caddr minPt)))
                 (setq viewMaxPt (list (+ (car maxPt) deltaX) (+ (cadr maxPt) deltaY) (caddr maxPt)))
                 (command "_.ZOOM" "_Window" viewMinPt viewMaxPt)
               )
               (command "_.ZOOM" "_Object" entName "") ; Fallback zoom object
             )
            )
            ((> numFound 1) ; Trường hợp tìm thấy nhiều đối tượng
             (setq overallMinPt nil overallMaxPt nil i 0)
             (repeat numFound
               (setq entName (ssname resultSS i)) (setq obj (vlax-ename->vla-object entName))
               (if (and obj (vlax-method-applicable-p obj 'GetBoundingBox))
                 (progn
                   (vla-GetBoundingBox obj 'minPoint 'maxPoint) (setq currentMinPt (vlax-safearray->list minPoint)) (setq currentMaxPt (vlax-safearray->list maxPoint))
                   (if (not overallMinPt)
                     (progn (setq overallMinPt currentMinPt) (setq overallMaxPt currentMaxPt))
                     (progn
                       (setq overallMinPt (list (min (car currentMinPt) (car overallMinPt)) (min (cadr currentMinPt) (cadr overallMinPt)) (min (caddr currentMinPt) (caddr overallMinPt))))
                       (setq overallMaxPt (list (max (car currentMaxPt) (car overallMaxPt)) (max (cadr currentMaxPt) (cadr overallMaxPt)) (max (caddr currentMaxPt) (caddr overallMaxPt))))
                     )
                   )
                 )
                 (princ (strcat "\nCảnh báo: Không thể lấy Bounding Box cho entity: " (vl-princ-to-string entName)))
               )
               (setq i (1+ i))
             )
             (if (and overallMinPt overallMaxPt)
                (progn
                  (setq deltaX (* (- (car overallMaxPt) (car overallMinPt)) 0.15)) (setq deltaY (* (- (cadr overallMaxPt) (cadr overallMinPt)) 0.15))
                  (if (< deltaX 1.0) (setq deltaX 1.0)) (if (< deltaY 1.0) (setq deltaY 1.0))
                  (if (<= (- (car overallMaxPt) (car overallMinPt)) 1e-6) (setq deltaX 1.0)) ; Đảm bảo padding tối thiểu
                  (if (<= (- (cadr overallMaxPt) (cadr overallMinPt)) 1e-6) (setq deltaY 1.0)) ; nếu các text thẳng hàng
                  (setq viewMinPt (list (- (car overallMinPt) deltaX) (- (cadr overallMinPt) deltaY) (caddr overallMinPt)))
                  (setq viewMaxPt (list (+ (car overallMaxPt) deltaX) (+ (cadr overallMaxPt) deltaY) (caddr overallMaxPt)))
                  (command "_.ZOOM" "_Window" viewMinPt viewMaxPt)
                )
                (command "_.ZOOM" "_Object" resultSS "") ; Fallback zoom extents nếu không tính được bounding box
             )
            )
          ) ; Kết thúc cond zoom
          (if (and resultSS (> (sslength resultSS) 0)) ; Đảm bảo có kết quả để chọn
              (progn
                (setvar "HIGHLIGHT" 1) ; Đảm bảo đối tượng được tô sáng khi chọn
                (sssetfirst nil resultSS) ; Chọn các đối tượng tìm thấy
                (princ "\nCác đối tượng tìm thấy đã được CHỌN và zoom.")
              )
          )
        )
        (princ "\nKhông tìm thấy đối tượng nào có nội dung giống với mẫu.") ; ELSE: Nếu không tìm thấy
      ) ; Kết thúc if (> numFound 0)
    ) ; Kết thúc progn của if ssSearchArea
    (princ "\nKhông có đối tượng Text/MText nào được chọn để tìm kiếm (vùng chọn rỗng).")
  ) ; Kết thúc if ssSearchArea

  ;; --- Kết thúc lệnh và khôi phục ---
  (*error* nil) ; Gọi *error* với nil để khôi phục các biến hệ thống và *error* gốc
  (princ)       ; Ngăn chặn in giá trị trả về của (defun c:T4) ra command line
) ; Kết thúc (defun c:T4 ...)