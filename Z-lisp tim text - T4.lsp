;; Chọn text mẫu, tìm các text tương tự, zoom tới và CHỌN KẾT QUẢ
(defun c:T4 (/ sourceEnt sourceEdata sourceObjType searchText ss ent txtContent resultSS numFound i obj minPoint maxPoint
               minPt maxPt overallMinPt overallMaxPt currentMinPt currentMaxPt
               viewMinPt viewMaxPt deltaX deltaY)
  (vl-load-com) ; Đ<PERSON>m bảo các hàm Visual LISP được tải

  (princ "\nLệnh T4: Chọn text mẫu, tìm các text tương tự, zoom và CHỌN kết quả.")

  ;; B1: Chọn đối tượng text/mtext mẫu
  (setq sourceEnt (car (entsel "\nChọn một đối tượng TEXT hoặc MTEXT mẫu: ")))

  (if (and sourceEnt (eq (type sourceEnt) 'ENAME))
    (progn
      (setq sourceEdata (entget sourceEnt))
      (setq sourceObjType (cdr (assoc 0 sourceEdata)))

      (if (or (eq sourceObjType "TEXT") (eq sourceObjType "MTEXT"))
        (progn
          (setq searchText (cdr (assoc 1 sourceEdata))) ; Nội dung text mẫu

          (if (and searchText (not (eq searchText ""))) ; Kiểm tra text mẫu không rỗng
            (progn
              (princ (strcat "\nĐã chọn text mẫu: \"" searchText "\""))

              ;; B2: Chọn các đối tượng text/mtext để tìm kiếm trong đó
              (setq ss (ssget "\nChọn các đối tượng TEXT/MTEXT để tìm kiếm:" '((0 . "TEXT,MTEXT"))))

              (if ss
                (progn
                  ;; B3: Tìm kiếm và tạo selection set kết quả
                  (setq resultSS (ssadd))
                  (setq i 0)
                  (repeat (sslength ss)
                    (setq ent (ssname ss i))
                    (setq txtContent (cdr (assoc 1 (entget ent))))
                    ;; So sánh chính xác, không phân biệt chữ hoa/thường
                    (if (eq (strcase txtContent) (strcase searchText))
                      (ssadd ent resultSS)
                    )
                    (setq i (1+ i))
                  )

                  ;; B4: Kiểm tra và xử lý kết quả
                  (setq numFound (sslength resultSS))
                  (if (> numFound 0)
                    (progn
                      (princ (strcat "\nĐã tìm thấy " (itoa numFound) " đối tượng giống với text mẫu."))
                      (princ "\nĐang zoom tới đối tượng...")
                      
                      ;; ===== B5: ZOOM TỚI ĐỐI TƯỢNG TÌM THẤY =====
                      (cond
                        ((= numFound 1)
                         ;; Trường hợp tìm thấy 1 đối tượng
                         (setq ent (ssname resultSS 0))
                         (setq obj (vlax-ename->vla-object ent))
                         (if (and obj (vlax-method-applicable-p obj 'GetBoundingBox))
                           (progn
                             ;; Sửa: Khởi tạo biến minPoint, maxPoint đúng kiểu
                             (setq minPoint (vlax-make-variant (vlax-safearray-fill (vlax-make-safearray vlax-vbDouble '(0 . 2)) '(0 0 0))))
                             (setq maxPoint (vlax-make-variant (vlax-safearray-fill (vlax-make-safearray vlax-vbDouble '(0 . 2)) '(0 0 0))))
                             (vla-GetBoundingBox obj 'minPoint 'maxPoint)
                             (setq minPt (vlax-safearray->list (vlax-variant-value minPoint)))
                             (setq maxPt (vlax-safearray->list (vlax-variant-value maxPoint)))
                             (if (and minPt maxPt
                                      (= (length minPt) 3)
                                      (= (length maxPt) 3))
                               (progn
                                 (setq deltaX (* (- (car maxPt) (car minPt)) 0.15))
                                 (setq deltaY (* (- (cadr maxPt) (cadr minPt)) 0.15))
                                 (if (< deltaX 0.5) (setq deltaX 0.5)) 
                                 (if (< deltaY 0.5) (setq deltaY 0.5))
                                 (setq viewMinPt (list (- (car minPt) deltaX) (- (cadr minPt) deltaY)))
                                 (setq viewMaxPt (list (+ (car maxPt) deltaX) (+ (cadr maxPt) deltaY)))
                                 (command "_.ZOOM" "_Window" viewMinPt viewMaxPt)
                               )
                               (progn
                                 (princ "\nKhông thể lấy Bounding Box hợp lệ. Zoom tới đối tượng bằng lệnh ZOOM Object.")
                                 (command "_.ZOOM" "_Object" ent "")
                               )
                             )
                        )
                        ((> numFound 1)
                         ;; Trường hợp tìm thấy nhiều đối tượng
                         (setq overallMinPt nil overallMaxPt nil i 0)
                         (repeat numFound
                           (setq ent (ssname resultSS i))
                           (setq obj (vlax-ename->vla-object ent))
                           (if (and obj (vlax-method-applicable-p obj 'GetBoundingBox))
                             (progn
                               ;; Sửa: Khởi tạo biến minPoint, maxPoint đúng kiểu
                               (setq minPoint (vlax-make-variant (vlax-safearray-fill (vlax-make-safearray vlax-vbDouble '(0 . 2)) '(0 0 0))))
                               (setq maxPoint (vlax-make-variant (vlax-safearray-fill (vlax-make-safearray vlax-vbDouble '(0 . 2)) '(0 0 0))))
                               (vla-GetBoundingBox obj 'minPoint 'maxPoint)
                               (setq currentMinPt (vlax-safearray->list (vlax-variant-value minPoint)))
                               (setq currentMaxPt (vlax-safearray->list (vlax-variant-value maxPoint)))
                               (if (not overallMinPt) 
                                 (progn (setq overallMinPt currentMinPt) (setq overallMaxPt currentMaxPt))
                                 (progn
                                   (setq overallMinPt
                                           (list
                                               (min (car currentMinPt) (car overallMinPt))
                                               (min (cadr currentMinPt) (cadr overallMinPt))
                                               (let ((z_curr (caddr currentMinPt)) (z_ov (caddr overallMinPt)))
                                                   (cond ((and z_curr z_ov) (min z_curr z_ov)) 
                                                         (z_curr z_curr) 
                                                         (z_ov z_ov) 
                                                         (t 0.0)))
                                           )
                                   )
                                   (setq overallMaxPt
                                           (list
                                               (max (car currentMaxPt) (car overallMaxPt))
                                               (max (cadr currentMaxPt) (cadr overallMaxPt))
                                               (let ((z_curr (caddr currentMaxPt)) (z_ov (caddr overallMaxPt)))
                                                   (cond ((and z_curr z_ov) (max z_curr z_ov)) 
                                                         (z_curr z_curr) 
                                                         (z_ov z_ov) 
                                                         (t 0.0)))
                                           )
                                   )
                                 )
                               )
                             )
                             (princ (strcat "\nCảnh báo: Không thể lấy Bounding Box cho entity: " (vl-princ-to-string ent)))
                           )
                           (setq i (1+ i))
                         )
                         (if (and overallMinPt overallMaxPt)
                            (progn
                                (setq deltaX (* (- (car overallMaxPt) (car overallMinPt)) 0.15))
                                (setq deltaY (* (- (cadr overallMaxPt) (cadr overallMinPt)) 0.15))
                                (if (< deltaX 1.0) (setq deltaX 1.0)) 
                                (if (< deltaY 1.0) (setq deltaY 1.0))
                                (if (<= (- (car overallMaxPt) (car overallMinPt)) 1e-6) (setq deltaX 1.0)) 
                                (if (<= (- (cadr overallMaxPt) (cadr overallMinPt)) 1e-6) (setq deltaY 1.0))
                                (setq viewMinPt (list (- (car overallMinPt) deltaX) (- (cadr overallMinPt) deltaY)))
                                (setq viewMaxPt (list (+ (car overallMaxPt) deltaX) (+ (cadr overallMaxPt) deltaY)))
                                (command "_.ZOOM" "_Window" viewMinPt viewMaxPt)
                            )
                            (progn
                                (princ "\nKhông thể xác định vùng zoom cho các đối tượng. Sử dụng ZOOM Object.")
                                (command "_.ZOOM" "_Object" resultSS "") 
                            )
                         )
                        )
                      )
                      ;; ===== KẾT THÚC PHẦN ZOOM =====

                      ;; =================================================================
                      ;; BƯỚC CHỌN ĐỐI TƯỢNG (OBJECT SELECTION STEP) - SAU KHI ZOOM
                      ;; Sử dụng (sssetfirst nil resultSS) để chọn đối tượng.
                      ;; Đây là cách LISP thường dùng để chọn đối tượng và giữ lại lựa chọn.
                      ;; =================================================================
                      (if (and resultSS (> (sslength resultSS) 0)) 
                          (progn
                            (sssetfirst nil resultSS) ; CHỌN ĐỐI TƯỢNG Ở ĐÂY!
                            (princ "\nCác đối tượng đã được CHỌN và zoom. Hãy kiểm tra lệnh tiếp theo.")
                          )
                          (princ "\nKhông có đối tượng nào trong resultSS để chọn sau khi zoom.")
                      )
                      ;; =================================================================
                    )
                    (princ (strcat "\nKhông tìm thấy đối tượng nào có nội dung giống: \"" searchText "\""))
                  )
                )
                (princ "\nKhông có đối tượng text/mtext nào được chọn để tìm kiếm.")
              )
            )
            (princ "\nText mẫu rỗng hoặc không hợp lệ. Không thể tìm kiếm.")
          )
        )
        (princ "\nĐối tượng mẫu được chọn không phải là TEXT hoặc MTEXT.")
      )
    )
    (princ "\nKhông có đối tượng mẫu nào được chọn hoặc lựa chọn không hợp lệ.")
  )
  (princ) ; Kết thúc lệnh "sạch sẽ"
)