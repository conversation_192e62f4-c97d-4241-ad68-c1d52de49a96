; TE - Table Export v1.5 FINAL - Backup
; Phat trien boi Zit Dai Ka - 19/12/2024
; Xuat table CAD sang Excel voi xu ly format codes chinh xac

; Cac bien toan cuc (tuong thich voi E1-ET)
(if (not *E6-handle*) (setq *E6-handle* "Y"))
(if (not *E6-frame*) (setq *E6-frame* "N"))
(if (not *E6-jump*) (setq *E6-jump* "3"))

; Ham khoi tao Excel
(defun INIT-EXCEL ( / excel-app workbook worksheet)
	(setq excel-app (vlax-get-or-create-object "Excel.Application"))
	(if excel-app
		(progn
			(vla-put-visible excel-app :vlax-true)
			(setq workbook (vlax-invoke-method (vlax-get-property excel-app 'Workbooks) 'Add))
			(setq worksheet (vlax-get-property workbook 'ActiveSheet))
			(list excel-app workbook worksheet)
		)
		nil
	)
)

; Ham ghi cell Excel
(defun WRITE-EXCEL-CELL (worksheet row col value)
	(vlax-put-property 
		(vlax-get-property worksheet 'Cells row col)
		'Value
		value
	)
)

; Ham ghi handle vao comment Excel
(defun WRITE-HANDLE-TO-COMMENT (worksheet row col handle file-path)
	(if (= *E6-handle* "Y")
		(progn
			(setq cell (vlax-get-property worksheet 'Cells row col))
			(setq comment-text (strcat handle ";\nFileCad: " file-path))
			(vlax-invoke-method cell 'AddComment comment-text)
		)
	)
)

; Ham jump cursor Excel
(defun JUMP-EXCEL-CURSOR (worksheet last-row last-col)
	(setq jump-rows (atoi *E6-jump*))
	(setq new-row (+ last-row jump-rows))
	(vlax-put-property worksheet 'Range 
		(vlax-get-property worksheet 'Cells new-row 1)
	)
	(princ (strcat "Chuot dang o vi tri " (itoa new-row) ",1"))
)

; Ham chuyen doi ky hieu dac biet
(defun CONVERT-SPECIAL-SYMBOLS (text-string)
	(setq result text-string)
	(setq result (vl-string-subst "Ø" "%%c" result))
	(setq result (vl-string-subst "Ø" "%%C" result))
	(setq result (vl-string-subst "±" "%%p" result))
	(setq result (vl-string-subst "±" "%%P" result))
	(setq result (vl-string-subst "°" "%%d" result))
	(setq result (vl-string-subst "°" "%%D" result))
	result
)

; Ham trim string
(defun TRIM-STRING (text-string)
	(setq result text-string)
	; Trim dau
	(while (and (> (strlen result) 0) (= (substr result 1 1) " "))
		(setq result (substr result 2))
	)
	; Trim cuoi
	(while (and (> (strlen result) 0) (= (substr result (strlen result) 1) " "))
		(setq result (substr result 1 (1- (strlen result))))
	)
	result
)

; Ham loai bo format codes DUNG - da hieu dung pattern
(defun SIMPLE-REMOVE-FORMAT-CODES (text-string / result start-pos end-pos after-semicolon)
	(setq result text-string)
	
	; Loai bo pattern: \[letter][number]; (co the co spaces sau semicolon)
	(while (setq start-pos (vl-string-search "\\" result))
		; Tim semicolon sau backslash
		(setq end-pos (vl-string-search ";" result start-pos))
		
		(if end-pos
			(progn
				; Tim vi tri sau semicolon va spaces
				(setq after-semicolon (+ end-pos 1))
				
				; Bo qua cac spaces sau semicolon
				(while (and (< after-semicolon (strlen result))
							(= (substr result (+ after-semicolon 1) 1) " "))
					(setq after-semicolon (+ after-semicolon 1))
				)
				
				; Loai bo tu backslash den het spaces
				(setq result (strcat 
					(substr result 1 start-pos)
					(substr result (+ after-semicolon 1))
				))
			)
			; Neu khong co semicolon, loai bo het tu backslash
			(setq result (substr result 1 start-pos))
		)
	)
	
	result
)

; Ham xu ly text table dac biet cho tieng Viet - phien ban don gian va hieu qua
(defun CLEAN-TABLE-TEXT ( text-string / result)
	(if (and text-string (= (type text-string) 'STR) (> (strlen text-string) 0))
		(progn
			(setq result text-string)
			
			; Neu text khong co format codes, tra ve luon
			(if (and (not (vl-string-search "\\" result))
					 (not (vl-string-search "{" result))
					 (not (vl-string-search "}" result)))
				result
				; Neu co format codes, xu ly don gian va an toan
				(progn
					; Buoc 1: Loai bo cac format codes bang regex pattern
					(setq result (SIMPLE-REMOVE-FORMAT-CODES result))
					
					; Buoc 2: Loai bo dau ngoac nhon
					(setq result (vl-string-subst "" "{" result))
					(setq result (vl-string-subst "" "}" result))
					
					; Buoc 3: Chuyen doi ky hieu dac biet
					(setq result (CONVERT-SPECIAL-SYMBOLS result))
					
					; Buoc 4: Trim va lam sach cuoi cung
					(setq result (TRIM-STRING result))
					
					result
				)
			)
		)
		""
	)
)

; Ham chinh - xuat table sang Excel
(defun C:TE ( / table-obj sel excel-data excel-app workbook worksheet rows cols row col cell-value clean-value table-handle file-path)
	(princ "\n=== TABLE EXPORT v1.5 ===")
	
	; Chon table
	(princ "\nChon table CAD de xuat sang Excel: ")
	(setq sel (entsel))
	
	(if sel
		(progn
			(setq table-obj (vlax-ename->vla-object (car sel)))
			
			; Kiem tra xem co phai la table khong
			(if (= (vla-get-objectname table-obj) "AcDbTable")
				(progn
					; Khoi tao Excel
					(setq excel-data (INIT-EXCEL))
					(if excel-data
						(progn
							(setq excel-app (nth 0 excel-data))
							(setq workbook (nth 1 excel-data))
							(setq worksheet (nth 2 excel-data))
							
							; Lay thong tin table
							(setq rows (vla-get-rows table-obj))
							(setq cols (vla-get-columns table-obj))
							(setq table-handle (vla-get-handle table-obj))
							(setq file-path (getvar "DWGNAME"))
							
							(princ (strcat "\nTable co " (itoa rows) " hang va " (itoa cols) " cot"))
							
							; Duyet qua tung cell
							(setq row 0)
							(while (< row rows)
								(setq col 0)
								(while (< col cols)
									; Lay noi dung cell
									(setq cell-value (vla-gettext table-obj row col))
									
									; Lam sach text
									(setq clean-value (CLEAN-TABLE-TEXT cell-value))
									
									; Ghi vao Excel
									(if (> (strlen clean-value) 0)
										(WRITE-EXCEL-CELL worksheet (+ row 1) (+ col 1) clean-value)
									)
									
									; Ghi handle vao comment (chi cell dau tien)
									(if (and (= row 0) (= col 0))
										(WRITE-HANDLE-TO-COMMENT worksheet 1 1 table-handle file-path)
									)
									
									(setq col (+ col 1))
								)
								(setq row (+ row 1))
							)
							
							; Jump cursor
							(JUMP-EXCEL-CURSOR worksheet rows cols)
							
							(princ (strcat "\nHoan thanh! Da xuat table " (itoa rows) "x" (itoa cols) " sang Excel"))
						)
						(princ "\nLoi: Khong the khoi tao Excel!")
					)
				)
				(princ "\nObject duoc chon khong phai la table!")
			)
		)
		(princ "\nKhong chon duoc table!")
	)
	(princ)
)

(princ "\n=== TE - TABLE EXPORT v1.5 FINAL ===")
(princ "\nXuat table CAD sang Excel voi xu ly format codes chinh xac")
(princ "\nPattern format codes: \\[letter][number]; (co the co spaces)")
(princ "\nVi du: \\C256;MATERIAL → MATERIAL")
(princ "\nVi du: \\A1;  ĐẦU ĐO pH → ĐẦU ĐO pH")
(princ "\nLenh: TE")
(princ "\n**Zit Đại Ka**")
(setvar "MODEMACRO" "**Zit Đại Ka**")
(princ)
